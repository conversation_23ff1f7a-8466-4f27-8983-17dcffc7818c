<template>
  <div class="mb-6 flex flex-col gap-4">
    <form>
      <div
        class="
            grid gap-x-4 gap-y-4
            lg:grid-cols-2
          "
      >
        <FormFields :options="formFields" />
      </div>
      <div class="mt-4 flex items-center gap-2">
        <Button
          size="xl"
          class="w-fit"
          @click="search"
        >
          Search
        </Button>
        <Button
          v-if="timesheet.items.value && timesheet.items.value.length > 0"
          label="Export CSV"
          size="xl"
          class="w-fit"
          variant="outline"
          icon="proicons:add-square-multiple"
          color="neutral"
          @click="exportToExcel"
        />
      </div>
    </form>
    <div
      class="grid"
    >
      <TableSimple
        :options="tableOptions"
      >
        <template #name-cell="{ row }">
          <AvatarProfile :item="row.original" />
        </template>
      </TableSimple>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import { useTeamPageLoader } from '~/loaders/admin/team'
import { useAdminTimeSheetSummaryReportListLoader } from '~/loaders/admin/timesheet'
import { startOfMonth, endOfMonth, format } from 'date-fns'
import { COLUMN_TYPES, type TableColumn } from '#core/components/Table/types'

const companyHolidays = useHolidaysPageLoader()
const timesheet = useAdminTimeSheetSummaryReportListLoader()
const team = useTeamPageLoader()
const currentDate = ref(new Date())

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    date: v.nullish(v.object({
      year: v.number(),
      month: v.number(),
    })),
    team_code: v.optional(v.array(v.string()), []),
  })),
  initialValues: {
    team_code: [],
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },
})

companyHolidays.fetchSetLoading()
team.fetchSetLoading()
onMounted(() => {
  timesheet.run({
    params: {
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      team_code: Array.isArray(form.values.team_code) && form.values.team_code.length > 0
        ? form.values.team_code.join(',')
        : undefined,
    },
  })

  companyHolidays.fetchPage()
  team.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const summaryTimesheet = computed(() => {
  const allTimingsSet = new Map<string, Timing>()

  timesheet.items.value.forEach((user) => {
    user.timings?.forEach((t) => {
      if (t.type === TRACKER_TYPE.PROJECT) {
        const key = t.project_name

        allTimingsSet.set(`PROJECT:${key}`, {
          ...t,
          total_timing: 0,
        })
      }

      if (t.type === TRACKER_TYPE.SGA) {
        const key = t.sga_name

        allTimingsSet.set(`SGA:${key}`, {
          ...t,
          total_timing: 0,
        })
      }

      if (t.type === TRACKER_TYPE.OT) {
        const key = t.project_name

        allTimingsSet.set(`OT:${key}`, {
          ...t,
          total_timing: 0,
        })
      }

      if (t.type !== TRACKER_TYPE.PROJECT && t.type !== TRACKER_TYPE.SGA && t.type !== TRACKER_TYPE.OT) {
        allTimingsSet.set(`${t.type}`, {
          ...t,
          total_timing: 0,
        })
      }
    })
  })

  const allKeys = Array.from(allTimingsSet.keys())

  timesheet.items.value.forEach((user) => {
    const userKeys = new Set<string>()

    user.timings?.forEach((t) => {
      if (t.type === TRACKER_TYPE.PROJECT) userKeys.add(`PROJECT:${t.project_name}`)
      if (t.type === TRACKER_TYPE.SGA) userKeys.add(`SGA:${t.sga_name}`)
      if (t.type === TRACKER_TYPE.OT) userKeys.add(`OT:${t.project_name}`)
      if (t.type !== TRACKER_TYPE.PROJECT && t.type !== TRACKER_TYPE.SGA && t.type !== TRACKER_TYPE.OT) userKeys.add(`${t.type}`)
    })

    allKeys.forEach((key) => {
      if (!userKeys.has(key)) {
        user.timings?.push({
          ...allTimingsSet.get(key)!,
        })
      }
    })

    user.timings?.sort((a, b) => {
      const aKey
        = a.type === TRACKER_TYPE.PROJECT
          ? `PROJECT:${a.project_name}`
          : a.type === TRACKER_TYPE.SGA
            ? `SGA:${a.sga_name}`
            : a.type === TRACKER_TYPE.OT
              ? `OT:${a.project_name}`
              : a.type

      const bKey
        = b.type === TRACKER_TYPE.PROJECT
          ? `PROJECT:${b.project_name}`
          : b.type === TRACKER_TYPE.SGA
            ? `SGA:${b.sga_name}`
            : b.type === TRACKER_TYPE.OT
              ? `OT:${b.project_name}`
              : b.type

      return aKey.localeCompare(bKey)
    })
  })

  // เปลี่ยนชื่อ project_name สำหรับ OT display เป็น "project_name(OT)"
  timesheet.items.value.forEach((user) => {
    user.timings = user.timings?.map((t) => {
      if (t.type === TRACKER_TYPE.OT && t.project_name) {
        return {
          ...t,
          ot_name: `${t.project_name} (OT)`,
        }
      }

      return t
    })
  })

  return timesheet.items.value
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'lg:col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT_MULTIPLE,
    class: 'lg:col-span-1',
    props: {
      name: 'team_code',
      label: 'Team',
      placeholder: 'All Team',
      options: ArrayHelper.toOptions(team.fetch.items, 'code', 'name'),
      clearable: true,
    },
  },
])

const search = () => {
  timesheet.run({
    params: {
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      team_code: Array.isArray(form.values.team_code) && form.values.team_code.length > 0
        ? form.values.team_code.join(',')
        : undefined,
    },
  })
}

const tableOptions = useTableSimple<ITimesheet>({
  items: () => {
    return summaryTimesheet.value as any
  },
  status: () => timesheet.status.value,
  columns: () => {
    const baseColumns: TableColumn<ITimesheet>[] = [
      {
        accessorKey: 'name',
        header: 'Name',
        type: COLUMN_TYPES.TEXT,
      },
      {
        accessorKey: 'total_timing',
        header: 'Total',
        type: COLUMN_TYPES.TEXT,
      },
    ]

    if (summaryTimesheet.value) {
      summaryTimesheet.value.at(0)?.timings.forEach((data, index) => {
        baseColumns.push({
          accessorKey: `timings.${index}.total_timing`,
          header: () =>
            h('div', [
              `${data.ot_name || data.sga_name || data.project_name || data.type} `,
            ]),
          type: COLUMN_TYPES.TEXT,
          meta: {
            class: {
              td: 'text-center',
              th: 'text-center',
            },
          },
        } as TableColumn<ITimesheet>)
      })
    }

    return baseColumns
  },
})

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
}, {
  deep: true,
})

const exportToExcel = (): void => {
  // รวม project/SGA/Type ทั้งหมดจาก timings
  const allKeys = new Set<string>()

  timesheet.items.value.forEach((user) => {
    user.timings.forEach((t) => {
      const key = t.ot_name || t.project_name || t.sga_name || t.type

      allKeys.add(key)
    })
  })

  const keys = Array.from(allKeys)

  // header row
  const header = ['Name', 'Team', ...keys]

  // ข้อมูลรายคน
  const rows = timesheet.items.value.map((user) => {
    const row: (string | number)[] = [user.display_name, user.team.name]

    keys.forEach((key) => {
      const found = user.timings.find(
        (t) => (t.ot_name || t.project_name || t.sga_name || t.type) === key,
      )

      row.push(found ? found.total_timing : 0)
    })

    return row
  })

  // ใช้ QuickExcel.export
  QuickExcel.export(
    header,
    rows,
    'SummaryReport.xlsx',
  )
}
</script>
