import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '1',
      title: 'Pre-sale Document',
      fileName: '20251001_FirstTOR.pdf',
      channel: 'pre-sale',
      direction: 'inbound',
      date: '2025-06-30',
      owner: 'June',
      contentType: 'day',
    },
    {
      id: '2',
      title: 'Pre-sale Document',
      fileName: '20251001_FirstTOR.pdf',
      channel: 'pre-sale',
      direction: 'inbound',
      date: '2025-06-20',
      owner: 'Mee',
      contentType: 'day',

    },
    {
      id: '3',
      title: 'Sale Checklist',
      fileName: 'checklist.xlsx',
      channel: 'sale',
      direction: 'outbound',
      date: '2025-06-18',
      contentType: 'day',
      owner: 'Long',

    },
    {
      id: '4',
      title: 'Sale Checklist',
      fileName: 'checklist.xlsx',
      channel: 'sale',
      direction: 'outbound',
      date: '2025-07-18',
      contentType: 'day',
      owner: 'Sea',

    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
