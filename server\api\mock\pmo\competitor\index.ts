import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '11111111-aaaa-bbbb-cccc-222222222222',
      project_id: 'aaaa1111-bbbb-2222-cccc-333333333333',
      company: 'บริษัท เอ บี ซี จำกัด',
      detail: 'ผู้ให้บริการระบบ ERP รายใหญ่ในไทย',
      created_at: new Date('2025-01-05T09:00:00Z'),
      created_by_id: 'user-1111-2222-3333-4444',
      updated_at: new Date('2025-01-10T14:00:00Z'),
      updated_by_id: 'user-1111-2222-3333-4444',
      deleted_at: null,
      deleted_by_id: null,
    },
    {
      id: '33333333-dddd-eeee-ffff-444444444444',
      project_id: 'aaaa1111-bbbb-2222-cccc-333333333333',
      company: 'FinTech Global Co., Ltd.',
      detail: 'บริษัทคู่แข่งด้านการเงินดิจิทัล',
      created_at: new Date('2025-01-12T11:30:00Z'),
      created_by_id: 'user-5555-6666-7777-8888',
      updated_at: new Date('2025-01-20T16:45:00Z'),
      updated_by_id: 'user-5555-6666-7777-8888',
      deleted_at: null,
      deleted_by_id: null,
    },
    {
      id: '55555555-gggg-hhhh-iiii-666666666666',
      project_id: 'aaaa1111-bbbb-2222-cccc-333333333333',
      company: 'Global Tech Solutions',
      detail: 'ให้บริการ Cloud และ Data Center',
      created_at: new Date('2025-01-18T08:15:00Z'),
      created_by_id: 'user-9999-aaaa-bbbb-cccc',
      updated_at: new Date('2025-02-01T10:00:00Z'),
      updated_by_id: 'user-9999-aaaa-bbbb-cccc',
      deleted_at: null,
      deleted_by_id: null,
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
