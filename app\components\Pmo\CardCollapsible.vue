<template>
  <Card
    class="overflow-visible"
    :ui="{
      body: 'lg:p-0  ',
    }"
  >
    <div class="flex items-start justify-between p-4">
      <div>
        <h3 class="text-primary-900 text-xl font-bold dark:text-slate-100">
          {{ props.title ?? 'งานที่ต้องทำ' }}
        </h3>
        <p class="text-md -mt-0.5 text-slate-500 dark:text-slate-400">
          {{ props.subtitle ?? 'Checklist' }}
        </p>
      </div>

      <div class="flex gap-2">
        <Button
          v-if="isIconToolBar"
          variant="outline"
          color="neutral"
          :icon="'mdi:microsoft-sharepoint'"
          class="rounded-lg"
          aria-label="Toggle"
        />
        <Button
          v-if="isIconToolBar"
          variant="outline"
          color="neutral"
          :icon="'weui:setting-outlined'"
          class="rounded-lg"
          aria-label="Toggle"
        />
        <Button
          v-if="!hideCollapsible"
          variant="outline"
          color="neutral"
          :icon="open ? 'ion:chevron-up' : 'ion:chevron-down'"
          class="rounded-lg"
          aria-label="Toggle"
          @click="open = !open"
        />
      </div>
    </div>

    <div
      v-show="open"
      :class="!hidePadding ? 'p-4':''"
    >
      <Separator
        v-if="!noUnderline"
        class="mb-4"
      />
      <slot />
    </div>
  </Card>
</template>

<script setup lang="ts">
// ย่อ/ขยาย
const props = defineProps<{
  title?: string
  subtitle?: string
  hidePadding?: boolean
  hideExplan?: boolean
  noUnderline?: boolean
  isIconToolBar?: boolean
  hideCollapsible?: boolean
}>()

const open = ref(true)
</script>
