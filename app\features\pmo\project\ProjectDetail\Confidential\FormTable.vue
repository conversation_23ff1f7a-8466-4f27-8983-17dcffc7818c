<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไข'+props.title : props.title"
    :description="props.subtitle"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="props.formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="status().isLoading"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  title: string
  subtitle: string
  values?: any | null
  isEditing?: boolean
  status: () => IStatus
  onSubmit: (values: any) => void
  formFields: any
  form: any
}>()

const formData = useForm({
  initialValues: props.values,
  ...props.form,
})

const onSubmit = formData.handleSubmit((values: any) => {
  props.onSubmit(values as any)
})
</script>
