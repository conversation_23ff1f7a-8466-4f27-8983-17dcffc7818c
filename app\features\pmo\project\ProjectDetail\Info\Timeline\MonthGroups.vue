<template>
  <div class="space-y-10">
    <div class="">
      <!-- header -->
      <div class="mt-4 flex justify-between bg-[#F9FAFB] px-3 py-3">
        <div class="flex items-center gap-2 text-gray-700">
          <Icon
            name="f7:doc-plaintext"
            class="text-primary h-5 w-5"
          />
          <span class="font-semibold">เอกสารรับเข้า</span>
        </div>
        <div class="flex items-center gap-2 text-gray-700">
          <Icon
            name="mynaui:arrow-up-circle"
            class="h-5 w-5 text-[#F79009]"
          />
          <span class="font-semibold">เอกสารส่งออก</span>
        </div>
      </div>
      <div class="relative z-10">
        <Timeline
          :items="props.items.map(d => ({
            title: d.date,
            content: d,
            revert: false,
            class:
              d.direction === 'inbound'
                ? 'flex-row-reverse -translate-x-[calc(100%-2rem)] text-right w-[50%] '
                : 'w-[50%]',
          }))"
          orientation="vertical"
          :ui="{
            root: 'flex gap-0 ',
            container: 'relative flex items-center gap-0 translate-y-[calc(50%-1rem)]',
            separator: 'flex-1 rounded-none bg-[#EAECF0] ',
            item: 'group relative flex flex-1 gap-0 ',
            indicator: 'bg-tranparent',

          }"

          class="translate-x-[calc(50%-1rem)]"
        >
          <template #indicator="{ item }">
            <Icon
              name="fa-solid:dot-circle"
              class="h-8 w-8"
              :class="item.content.direction === 'inbound'
                ? 'rounded-full border-4 border-[#ffffff96] bg-blue-500'
                : 'rounded-full border-4 border-[#ffffff96] bg-[#F79009]'"
            />
          </template>

          <template #title="{ item }">
            <div
              v-if="item.content.contentType === 'day'"
              class="h-[150px] min-h-[150px] pt-6"
              :class="item.content.direction === 'inbound'
                ? 'absolute left-[100%] flex h-[100%] w-full items-center justify-start text-left'
                : 'absolute left-[-100%] flex h-[100%] w-full items-center justify-end text-right'"
            >
              <div
                class="contents-center mt-2 gap-1"
                :class="item.content.direction === 'inbound' ? 'pl-3' : 'pr-3'"
              >
                <div
                  class="text-xl font-semibold"
                  :class="item.content.direction === 'inbound' ? 'text-left' : 'text-right'"
                >
                  {{ item.content.date ? TimeHelper.thaiFormat(item.content.date, 'd') :"N/A" }}
                </div>
                <div
                  class="text-md font-semibold text-gray-400"
                  :class="item.content.direction !== 'inbound' ? 'text-right' : 'text-left'"
                >
                  {{ item.content.date ? TimeHelper.thaiFormat(item.content.date, 'MMM') :"N/A" }}
                </div>
              </div>
            </div>
            <div
              v-else
              class="flex h-[200px] min-h-[200px] items-center justify-start"
            >
              <div class="absolute top-[80px] z-10 mx-auto my-3 w-fit -translate-x-[calc(100%-2rem)] rounded-lg border-1 border-[#EAECF0] bg-[#F9FAFB] px-4 py-3 text-sm font-medium text-gray-700">
                {{ item.content.title }}
              </div>
            </div>
          </template>

          <template #description="{ item }">
            <div
              v-if="item.content.contentType === 'day'"
              :class="item.content.direction === 'inbound' ? 'left-[-50%] mr-3' :'ml-3'"
            >
              <DocCard :doc="item.content" />
            </div>
            <div v-else />
          </template>
        </Timeline>
      </div>
      <div class="relative z-1">
        <slot />
      </div>
    </div>
  </div>
</template>

<script lang="ts"  setup>
import DocCard from './DocCard.vue'

const props = defineProps<{
  items: any[]
  dotColor?: string
}>()
</script>
