<template>
  <StatusBox
    :status="checklist.fetch.status"
    :data="checklist.fetch.items"
  >
    <Table

      class="w-full"
      :options="tableOptions"
      :ui="{
        th: 'text-[#222222] bg-white text-xs font-normal',
      }"
      @pageChange="checklist.fetchPageChange"
      @search="checklist.fetchSearch"
    >
      <template #file-cell="{ row }">
        <div
          class="flex items-center gap-2"
        >
          <Checkbox
            v-if="!readonly"
            :model-value="row.original.is_checked"
            class="mt-0.5 w-full"
            :disabled="!!readonly
              || !permission.hasPermission(PROJECT_TAB_KEY[tab as PROJECT_TAB_TYPE], PMO_PERMISSION.MODIFY)"
            :label="row.original.detail"
            :ui="{
              label: row.original.is_checked
                ? 'line-through text-slate-400 dark:text-slate-500'
                : 'text-slate-700 dark:text-slate-200',
            }"
            @update:model-value="onToggle(it)"
          />
          <div
            v-else
            class="flex items-center gap-1"
          >
            <Icon
              :class="row.original.is_checked ? 'text-success' : 'text-[#667085]'"
              class="size-5"
              :name="row.original.is_checked
                ?'material-symbols:check-circle-outline-rounded'
                : 'material-symbols:cancel-outline'"
            />
            <div class="text-sm font-medium text-slate-700 dark:text-slate-200">
              {{ row.original.detail }}
            </div>
          </div>
        </div>
      </template>
      <template #assignee_id-cell="{ row }">
        <AvatarProfile
          v-if="row.original.assignee"
          :item="row.original.assignee"
        />
        <div
          v-else
          class="text-muted text-xs"
        >
          ยังไม่มีผู้รับผิดชอบ
        </div>
      </template>
      <template #updated_by-cell="{ row }">
        <div
          class="w-full text-right text-xs text-slate-400"
        >
          <div v-if="row.original.is_checked">
            เช็คเมื่อ  {{ TimeHelper.displayDateTime(row.original.updated_at) }}
            <span v-if="row.original.updated_by"> โดย {{ row.original.updated_by.display_name }}</span>
          </div>
        </div>
      </template>
      <template #actions-cell="{ row }">
        <div
          v-if="!readonly && permission.hasPermission(PROJECT_TAB_KEY[tab as PROJECT_TAB_TYPE], PMO_PERMISSION.MODIFY)"
          class="flex shrink-0 items-center gap-2"
        >
          <Button
            color="neutral"
            variant="outline"
            icon="mage:pen"
            class="rounded-lg"
            aria-label="Edit"
            @click="onEdit(row.original)"
          />
          <Button
            color="error"
            variant="outline"
            icon="i-heroicons-trash"
            class="rounded-lg"
            aria-label="Delete"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
    <div
      v-if="!readonly && permission.hasPermission(PROJECT_TAB_KEY[tab as PROJECT_TAB_TYPE], PMO_PERMISSION.MODIFY)"
      class="flex justify-end pt-2"
    >
      <Button
        color="primary"
        icon="i-heroicons-plus"
        class="rounded-xl"
        @click="onAdd"
      >
        เพิ่มรายการ
      </Button>
    </div>
  </StatusBox>
</template>

<script setup lang="ts">
import { useProjectChecklistItemLoader } from '~/loaders/pmo/project'
import Form from './FormChecklist.vue'
import type { PROJECT_TAB_TYPE } from '#imports'

const props = defineProps<{
  tab: TabKey
  projectId: string
  readonly?: boolean
}>()

const noti = useNotification()
const checklist = useProjectChecklistItemLoader(props.projectId as string)
const overlay = useOverlay()
const dialog = useDialog()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const permission = useProjectPermission()

const onToggle = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการอัพเดท',
    description: `คุณต้องการอัพเดท checklist "${values.detail}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    checklist.updateRun(values.id, {
      data: {
        is_checked: !values.is_checked,
      },
    })
  })
}

const onAdd = () => {
  addModal.open({
    status: () => checklist.add.status,
    onSubmit: (values: ITemplateDocument) => {
      checklist.addRun({
        data: {
          ...values,
          tab_key: props.tab,
        },
      })
    },
  })
}

const onEdit = (_checklist: any) => {
  editModal.open({
    isEditing: true,
    values: _checklist,
    status: () => checklist.update.status,
    onSubmit: (values: ITemplateDocument) => {
      checklist.updateRun(_checklist.id, {
        data: values,
      })
    },
  })
}

const onDelete = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบ Checklist "${values.detail}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    checklist.deleteRun(values.id)
  })
}

const tableOptions = useTable({
  options: {
    isRouteChange: true,
    isHidePagination: true,
  },
  repo: checklist,
  columns: () => [
    {
      accessorKey: 'file',
      header: 'รายการ',
      meta: {
        class: {
          th: 'w-2/3',
          td: 'w-2/3',
        },
      },
    },
    {
      accessorKey: 'assignee_id',
      header: 'ผู้รับผิดชอบ',
      meta: {
        class: {
          th: 'w-full',
          td: 'w-full',
        },
      },
    },
    {
      accessorKey: 'updated_by',
      header: '',
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

useWatchTrue(
  () => checklist.add.status.isSuccess,
  () => {
    addModal.close()

    noti.success({
      title: 'เพิ่มงานที่ต้องทำสำเร็จ',
      description: 'คุณได้เพิ่มงานที่ต้องทำเรียบร้อยแล้ว',
    })

    checklist.fetchPage(1, '', {
      params: {
        tab_key: props.tab,
      },
    })
  },
)

useWatchTrue(
  () => checklist.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มงานที่ต้องทำไม่สำเร็จ',
      description: StringHelper.getError(checklist.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มงานที่ต้องทำ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => checklist.update.status.isSuccess,
  () => {
    editModal.close()
    dialog.close()

    noti.success({
      title: 'แก้ไขงานที่ต้องทำสำเร็จ',
      description: 'คุณได้แก้ไขงานที่ต้องทำเรียบร้อยแล้ว',
    })

    checklist.fetchPage(1, '', {
      params: {
        tab_key: props.tab,
      },
    })
  },
)

useWatchTrue(
  () => checklist.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'แก้ไขงานที่ต้องทำไม่สำเร็จ',
      description: StringHelper.getError(checklist.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขงานที่ต้องทำ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => checklist.delete.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'ลบงานที่ต้องทำสำเร็จ',
      description: 'คุณได้ลบงานที่ต้องทำเรียบร้อยแล้ว',
    })

    checklist.fetchPage(1, '', {
      params: {
        tab_key: props.tab,
      },
    })
  },
)

useWatchTrue(
  () => checklist.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบงานที่ต้องทำไม่สำเร็จ',
      description: StringHelper.getError(checklist.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบงานที่ต้องทำ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

watch(
  () => props.tab,
  (newChannel, oldChannel) => {
    if (newChannel !== oldChannel) {
      checklist.fetchPage(1, '', {
        params: {
          tab_key: props.tab,
        },
      })
    }
  },
  {
    immediate: true,
  },
)
</script>
