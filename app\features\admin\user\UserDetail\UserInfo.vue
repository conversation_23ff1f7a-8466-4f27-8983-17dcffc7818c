<template>
  <Form
    @submit="onSubmit"
  >
    <div class="mb-4 flex flex-col justify-between gap-4 md:mb-6 md:flex-row lg:mb-9">
      <div class="aspect-square h-fit overflow-hidden rounded-lg">
        <img
          :src="profile?.avatar_url || '/profile_mock.png'"
          class="h-full w-full object-cover"
          :alt="profile?.avatar_url"
        />
      </div>
      <FormFields
        class="w-full max-w-[516px]"
        :options="fieldForm"
      />
    </div>

    <FormFields
      class="grid w-full gap-4 md:grid-cols-2"
      :options="fieldRole"
    />
    <Separator class="mt-4 mb-5" />
    <div class="flex justify-end gap-4">
      <Button
        label="Save Changes"
        type="submit"
        :disabled="!form.meta.value.dirty"
      />
    </div>
  </Form>
</template>

<script lang="ts" setup>
import { useTeamPageLoader } from '~/loaders/admin/team'
import { useUserPageLoader } from '~/loaders/admin/user'
import { format } from 'date-fns'

const props = defineProps<{
  profile: IUser
}>()

const user = useUserPageLoader()
const noti = useNotification()
const team = useTeamPageLoader()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุอีเมล')), ''),
    full_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเต็ม')), ''),
    display_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเล่น')), ''),
    team_code: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกทีม')), ''),
    position: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุตำแหน่ง')), ''),
    joined_date: v.nullish(v.pipe(v.string())),
  })),
  initialValues: props.profile,
})

const fieldForm = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'อีเมล (Email)',
      name: 'email',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเต็ม (Full Name)',
      name: 'full_name',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเล่น (Display Name)',
      name: 'display_name',
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันที่เริ่มทำงาน (Joined Date)',
      name: 'joined_date',
      maxDate: new Date(),
    },
  },
])

const fieldRole = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ทีม (Team)',
      name: 'team_code',
      loading: team.fetch.status.isLoading,
      options: ArrayHelper.toOptions(team.fetch.items, 'code', 'name'),
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ตำแหน่ง (Position)',
      name: 'position',
    },
  },
])

onMounted(() => {
  team.fetchPage(1, '', {
    params: {
      limit: 100,
    },
  })
})

const onSubmit = form.handleSubmit((values) => {
  user.updateRun(props.profile!.id, {
    data: {
      ...values,
      joined_date: format(new Date(values.joined_date), 'yyyy-MM-dd'),
    },
  })
})

useWatchTrue(() => user.update.status.isSuccess, () => {
  noti.success({
    title: 'อัพเดทข้อมูลสำเร็จ',
    description: `ข้อมูลของคุณ ${user.update.item?.display_name} ถูกอัพเดทเรียบร้อยแล้ว`,
  })
})

useWatchTrue(() => user.update.status.isError, () => {
  noti.error({
    title: 'อัพเดทข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(user.update.status.errorData, 'เกิดข้อผิดพลาดในการส่งคำขอ กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
