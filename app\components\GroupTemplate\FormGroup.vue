<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขกลุ่มเอกสาร' : 'เพิ่มกลุ่มเอกสาร'"
    description="Document Group"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    group_name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'กลุ่มเอกสาร',
      name: 'group_name',
      placeholder: 'ระบุกลุ่มเอกสาร',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as any)
})
</script>
