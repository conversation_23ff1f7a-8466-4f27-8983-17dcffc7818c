<template>
  <NuxtLayout>
    <CheckinDashboard />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import CheckinDashboard from '~/features/clockinAdmin/CheckinDashboard/index.vue'

definePageMeta({
  layout: 'clockin-admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.adminClockin.checkinDashboard.permissions,
  },
})

useSeoMeta({
  title: routes.adminClockin.checkinDashboard.label,
})

useApp().definePage({
  title: routes.adminClockin.checkinDashboard.label,
  breadcrumbs: [routes.adminClockin.checkinDashboard],
  sub_title: 'Checkin Dashboard',
})
</script>
