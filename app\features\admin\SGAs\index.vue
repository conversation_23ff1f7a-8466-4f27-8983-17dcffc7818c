<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่ม SGA
      </Button>
    </TeleportSafe>
    <FormFields
      class="
         mb-4 grid w-full gap-4
        lg:grid-cols-2
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="sgas.fetchPageChange"
      @search="sgas.fetchSearch"
    >
      <template #type-team="{ row }">
        <Badge
          variant="soft"
        >
          {{ row.original.team }}
        </Badge>
      </template>

      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useSGAPageLoader } from '~/loaders/admin/sga'
import Form from '../SGAs/Form.vue'
import { watchDebounced } from '@vueuse/core'

const sgas = useSGAPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const route = useRoute()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหา SGA',
    },
  },

])

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: sgas,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'w-2/3',
        },
      },
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

sgas.fetchSetLoading()
onMounted(() => {
  sgas.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const onEdit = (values: ISGA) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => sgas.update.status,
    onSubmit: (payload: ISGA) => {
      sgas.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  addModal.open({
    status: () => sgas.add.status,
    onSubmit: (payload: ISGA) => {
      sgas.addRun({
        data: payload,
      })
    },
  })
}

const onDelete = (values: ISGA) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบ SGA "${values.name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    sgas.deleteRun(values.id)
  })
}

watchDebounced(form.values, (values) => {
  sgas.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

useWatchTrue(
  () => sgas.update.status.isSuccess,
  () => {
    editModal.close()
    sgas.fetchPage()

    noti.success({
      title: 'แก้ไขSGAสำเร็จ',
      description: 'คุณได้แก้ไขSGAเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => sgas.update.status.isError,
  () => {
    editModal.close()
    dialog.close()
    noti.error({
      title: 'แก้ไขSGAไม่สำเร็จ',
      description: StringHelper.getError(sgas.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขSGA กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => sgas.delete.status.isSuccess,
  () => {
    editModal.close()
    sgas.fetchPage()
    dialog.close()
    noti.success({
      title: 'ลบSGAสำเร็จ',
      description: 'คุณได้ลบSGAเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => sgas.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบSGAไม่สำเร็จ',
      description: StringHelper.getError(sgas.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบSGA กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => sgas.add.status.isSuccess,
  () => {
    addModal.close()
    sgas.fetchPage()
    dialog.close()
    noti.success({
      title: 'เพิ่มSGAสำเร็จ',
      description: 'คุณได้เพิ่มSGAเรียบร้อยแล้ว',
    })

    dialog.close()
  },
)

useWatchTrue(
  () => sgas.add.status.isError,
  () => {
    addModal.close()
    dialog.close()
    noti.error({
      title: 'เพิ่มSGAไม่สำเร็จ',
      description: StringHelper.getError(sgas.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มSGA กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
