export const useUserPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IUser>({
    baseURL: '/users',
    getBaseRequestOptions: options.auth,
  })
}

export const useUserAccessLevelUpdateLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IUser>({
    method: 'PUT',
    url: '/users/:id/access-level',
    getRequestOptions: options.auth,
  })
}
