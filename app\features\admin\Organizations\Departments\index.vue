<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่มกรม
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="
         grid w-full gap-4
        lg:grid-cols-4
      "
    />
    <Table
      :options="tableOptions"
      @pageChange="department.fetchPageChange"
      @search="department.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { watchDebounced } from '@vueuse/core'
import Form from './Form.vue'
import { useOrgDepartmentPageLoader, useOrgDepartmentAdminPageLoader, useOrgMinistryPageLoader } from '~/loaders/admin/organizations'

const department = useOrgDepartmentPageLoader()
const departmentAdmin = useOrgDepartmentAdminPageLoader()
const ministry = useOrgMinistryPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
    ministry_id: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
  })),
})

ministry.fetchSetLoading()
department.fetchSetLoading()
onMounted(() => {
  department.fetchPage()
  ministry.fetchPage(1, '', {
    params: {
      limit: 10000,
    },
  })
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหากรม',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'ministry_id',
      placeholder: 'เลือกกระทรวง',
      options: ArrayHelper.toOptions(ministry.fetch.items, 'id', 'name_th'),
      loading: ministry.fetch.status.isLoading,
      clearable: true,
    },
  },
])

watchDebounced(form.values, (values) => {
  department.fetchSearch(values.q || '', {
    params: {
      ministry_id: values.ministry_id || undefined,
    },
  })
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<IDepartment>({
  repo: department,
  columns: () => [
    {
      accessorKey: 'name_th',
      header: 'ชื่อกรม (ภาษาไทย)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'name_en',
      header: 'ชื่อกรม (ภาษาอังกฤษ)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

const onEdit = (values: IDepartment) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => department.update.status,
    onSubmit: (payload: IDepartment) => {
      departmentAdmin.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  addModal.open({
    status: () => department.add.status,
    onSubmit: (payload: IDepartment) => {
      departmentAdmin.addRun({
        data: payload,
      })
    },
  })
}

const onDelete = (values: IDepartment) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบกรม "${values.name_th}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    departmentAdmin.deleteRun(values.id)
  })
}

useWatchTrue(
  () => departmentAdmin.update.status.isSuccess,
  () => {
    editModal.close()
    department.fetchPage()

    noti.success({
      title: 'แก้ไขกรมสำเร็จ',
      description: 'คุณได้แก้ไขกรมเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => departmentAdmin.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขกรมไม่สำเร็จ',
      description: StringHelper.getError(departmentAdmin.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขกรม กรุณาลองใหม่อีกครั้ง กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => departmentAdmin.delete.status.isSuccess,
  () => {
    noti.success({
      title: 'ลบกรมสำเร็จ',
      description: 'คุณได้ลบกรมเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => departmentAdmin.delete.status.isError,
  () => {
    noti.error({
      title: 'ลบกรมไม่สำเร็จ',
      description: StringHelper.getError(departmentAdmin.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบกรม กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => departmentAdmin.add.status.isSuccess,
  () => {
    addModal.close()
    department.fetchPage()

    noti.success({
      title: 'เพิ่มกรมสำเร็จ',
      description: 'คุณได้เพิ่มกรมเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => departmentAdmin.add.status.isError,
  () => {
    dialog.close()

    noti.error({
      title: 'เพิ่มกรมไม่สำเร็จ',
      description: StringHelper.getError(departmentAdmin.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มกรม กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
