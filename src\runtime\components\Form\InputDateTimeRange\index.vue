<template>
  <FieldWrapper v-bind="wrapperProps">
    <Datepicker
      ref="datepicker"
      v-model="innerValueRef"
      :teleport="teleport"
      :disabled="wrapperProps.disabled"
      :cancel-text="appConfig.core?.locale === 'th' ? 'ยกเลิก' : 'Cancel'"
      :select-text="appConfig.core?.locale === 'th' ? 'ตกลง' : 'Select'"
      :locale="appConfig.core?.locale"
      :format="format"
      :enable-time-picker="!disabledTime"
      :placeholder="wrapperProps.placeholder"
      :min-date="minDate"
      :max-date="maxDate"
      :min-time="minTime"
      :max-time="maxTime"
      :start-time="startTime"
      :multi-calendars="!isDisabledMultiCalendar"
      :required="required"
      time-picker-inline
      range
      :flow="['calendar', 'time']"
      @update:model-value="onInput"
    >
      <template
        v-if="appConfig.core?.is_thai_year"
        #year="{ value }"
      >
        {{ value + 543 }}
      </template>
      <template
        v-if="appConfig.core?.is_thai_year"
        #year-overlay-value="{ value }"
      >
        {{ value + 543 }}
      </template>
      <template #dp-input="{ value: innerValue }">
        <Input
          :trailing-icon="innerValue ? undefined : 'i-heroicons-calendar-days'"
          type="text"
          :model-value="innerValue"
          :placeholder="wrapperProps.placeholder"
          :readonly="true"
          :ui="{
            base: 'cursor-pointer select-none',
            trailingIcon: 'cursor-pointer',
          }"
        />
      </template>
      <template #clear-icon="{ clear }">
        <Icon
          :name="clearIcon"
          :class="theme.clearIcon({
            class: [ui?.clearIcon],
          })"
          @click.stop="clear"
        />
      </template>
    </Datepicker>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import Datepicker from '@vuepic/vue-datepicker'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { TimeHelper, ref, useFieldHOC, useAppConfig, computed, useUiConfig, watch, onMounted } from '#imports'
import '@vuepic/vue-datepicker/dist/main.css'

import type { IDateTimeRangeResponse, IDateTimeRangeFieldProps } from './date_range_time_field.types'
import { dateTimeTheme } from '#core/theme/dateTime'

const props = withDefaults(defineProps<IDateTimeRangeFieldProps>(), {
  clearIcon: 'ph:x-circle-fill',
  teleport: false,
})

const appConfig = useAppConfig()

const theme = computed(() => useUiConfig(dateTimeTheme, 'dateTime')())

const {
  value, wrapperProps,
} = useFieldHOC<IDateTimeRangeResponse | undefined>(props)

const innerValueRef = ref<Date[]>([])

// Initialize innerValueRef with current form value or default value
const initializeValue = () => {
  const currentValue = value.value

  if (currentValue) {
    const start = typeof currentValue.start === 'string' ? new Date(currentValue.start) : currentValue.start
    const end = typeof currentValue.end === 'string' ? new Date(currentValue.end) : currentValue.end

    innerValueRef.value = [start, end]
  } else {
    innerValueRef.value = []
  }
}

// Watch for changes in form value to sync with datepicker
watch(value, (newValue) => {
  if (newValue && (newValue.start || newValue.end)) {
    const start = typeof newValue.start === 'string' ? new Date(newValue.start) : newValue.start
    const end = typeof newValue.end === 'string' ? new Date(newValue.end) : newValue.end

    innerValueRef.value = [start, end]
  } else if (!newValue) {
    innerValueRef.value = []
  }
}, {
  immediate: false,
})

onMounted(() => {
  initializeValue()
})

const format = (date: Date[]) => {
  if (props.disabledTime) {
    return date.length > 0
      ? TimeHelper.displayDate(date[0]) + ' - ' + TimeHelper.displayDate(date[1] ?? date[0])
      : ''
  }

  return date.length > 0
    ? TimeHelper.displayDateTime(date[0]) + ' - ' + TimeHelper.displayDateTime(date[1] ?? date[0])
    : ''
}

const onInput = (_value: Date[]) => {
  if (_value === null || _value === undefined) {
    value.value = undefined

    return
  }

  if (props.disabledTime && !props.isReturnISO) {
    value.value = {
      start: TimeHelper.getDateFormTime(_value[0]),
      end: TimeHelper.getDateFormTime(_value[1] || _value[0]),
    } as IDateTimeRangeResponse
  } else {
    value.value = {
      start: _value[0],
      end: _value[1] || _value[0],
    } as IDateTimeRangeResponse
  }
}
</script>
