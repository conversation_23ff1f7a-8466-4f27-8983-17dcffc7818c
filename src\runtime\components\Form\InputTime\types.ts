import type {
  <PERSON>ieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ITimeOption {
  hours?: number | string
  minutes?: number | string
  seconds?: number | string
}

export interface ITimeFieldProps extends IFieldProps {
  clearIcon?: string
  minTime?: ITimeOption
  maxTime?: ITimeOption
  startTime?: ITimeOption
  format?: string
  enableSeconds?: boolean
  teleport?: boolean | string | HTMLElement
}

export type ITimeField = IFormFieldBase<
  INPUT_TYPES.TIME,
  ITimeFieldProps,
  {
    change: (value: string) => void
  }
>
