export class MaskaHelper {
  static thaiLetter = () => {
    return {
      mask: 'T',
      maskTokens: {
        T: {
          pattern: /[\u0E00-\u0E7F\s]/,
          repeated: true,
        },
      },
    }
  }

  static englishLetter = () => {
    return {
      mask: 'T',
      maskTokens: {
        T: {
          pattern: /[a-z\s]/i,
          repeated: true,
        },
      },
    }
  }

  static thaiIdCard = () => {
    return {
      mask: '#############',
      maskTokens: {
        '#': {
          pattern: /[0-9\n]/,
        },
      },
    }
  }

  static postCode = () => {
    return {
      mask: '#####',
      maskTokens: {
        '#': {
          pattern: /[0-9\n]/,
        },
      },
    }
  }

  static eofficeTax = () => {
    return {
      mask: 'TIN-#############',
      maskTokens: {
        '#': {
          pattern: /\d/,
        },
      },
    }
  }
}
