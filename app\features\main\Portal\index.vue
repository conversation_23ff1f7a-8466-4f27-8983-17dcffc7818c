<template>
  <!-- <div class="absolute top-20 left-0 flex h-[72px] w-screen items-center border border-[#D0D5DD] bg-[#FCFCFD]">
    <Container class="flex items-center gap-4">
      <div class="grid h-12 w-12 place-items-center rounded-lg border border-gray-100">
        <Icon
          class="size-6"
          name="mingcute:announcement-line"
        />
      </div>
      <div>
        <strong>Please submit the expenses reimbursement for 16 Jun - 15 Jul 25 via HunanOS.</strong> 16 Wed 2025
      </div>
      <Button
        variant="outline"
        class="ml-auto"
        color="neutral"
        :to="routes.announcements.to"
      >
        See All
      </Button>
    </Container>
  </div> -->
  <div class="mt-[60px] flex-1">
    <div
      v-if="apps.length"
      class="mb-12"
    >
      <h2 class="mb-4 text-3xl font-semibold">
        My Apps
      </h2>
      <div class="grid gap-8 md:grid-cols-3">
        <Button
          v-for="app in apps"
          :key="app.name"
          :to="app.to"
          variant="link"
          class="flex h-[172px] items-center justify-center rounded-lg bg-white p-6 shadow"
        >
          <img
            v-if="app.logo"
            :src="app.logo"
            :alt="app.label"
            class="h-[50px] w-[50px]"
          />
          <div class="text-3xl font-semibold text-[#353D59]">
            {{ app.label }}
          </div>
        </Button>
      </div>
    </div>
    <div v-if="adminApps.length">
      <h2 class="mb-4 text-3xl font-semibold">
        Admin
      </h2>
      <div class="grid gap-8 md:grid-cols-3">
        <Button
          v-for="admin in adminApps"
          :key="admin.name"
          :to="admin.to"
          class="flex h-[172px] items-center justify-center rounded-lg bg-white p-6 shadow"

          variant="link"
        >
          <img
            v-if="admin.logo"
            :src="admin.logo"
            :alt="admin.label"
            class="h-[50px] w-[50px]"
          />
          <div class="text-3xl font-semibold text-[#353D59]">
            {{ admin.label }}
          </div>
        </Button>
      </div>
    </div>
  </div>
  <TeleportSafe to="#page-footer">
    <div class="relative bg-[#353D59]">
      <img
        src="/cover/login.png"
        class="absolute right-21 bottom-0 w-1/2 md:w-1/5"
        alt="login"
      />
      <div class="mx-auto flex w-full max-w-7xl px-6 py-6">
        <p class="text-gray-400">
          © Copyright 2025 Finema
        </p>
      </div>
    </div>
  </TeleportSafe>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'

const auth = useAuth()
const apps = computed(() => [
  {
    name: 'clockin',
    logo: '/admin/clock-in-logo.png',
    label: 'Clock-In',
    to: routes.clockin.home.to,
  },
  {
    name: 'timesheet',
    logo: '/admin/timesheet-logo.png',
    label: 'Timesheet',
    to: routes.timesheet.home.to,
  },
  ...(auth.hasPermission(UserModule.PMO, Permission.ADMIN, Permission.USER, Permission.SUPER)
    ? [{
      name: 'pmo',
      logo: '/admin/pmo-logo.png',
      label: 'PMO',
      to: routes.pmo.home.to,
    }]
    : []),
])

const adminApps = computed(() => [
  ...(auth.hasPermission(UserModule.CHECKIN, Permission.ADMIN)
    ? [{
      name: 'clockin-admin',
      logo: '/admin/clock-in-admin-logo.png',
      label: 'Clock-In',
      to: routes.adminClockin.checkinDashboard.to,
    }]
    : []),
  ...(auth.hasPermission(UserModule.TIMESHEET, Permission.ADMIN)
    ? [{
      name: 'timesheet-admin',
      logo: '/admin/timesheet-admin-logo.png',
      label: 'Timesheet',
      to: routes.adminTimesheet.summaryReport.to,
    }]
    : []),

  ...(auth.hasPermission(UserModule.SETTING, Permission.SUPER)
    ? [{
      name: 'super-admin',
      logo: '/admin/super-admin-logo.png',
      label: 'Super Admin',
      to: routes.admin.users.to,
    }]
    : []),
])
</script>
