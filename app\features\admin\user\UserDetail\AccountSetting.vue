<template>
  <div class="flex justify-between gap-2">
    <div class="text-sm">
      <div class="font-bold">
        เปลี่ยนแปลงสถานะการใช้งานบัญชี
      </div>
      การปิดสถานะการใช้งาน เป็นการยกเลิกการใช้งานบัญชีชั่วคราว
      <br />ผู้ใช้งานจะไม่สามารถเข้าใช้งานระบบได้ แต่ข้อมูลและสถิติต่าง ๆ จะยังถูกคำนวณอยู่
    </div>
    <Switch
      :model-value="isActive"
      :label="isActive ? 'Active' : 'Inactive'"
      @update:modelValue="() => onSwitch()"
    />
  </div>
  <Separator class="mt-7 mb-5" />
  <div class="flex justify-between gap-2">
    <div class="text-sm">
      <div class="mb-4 font-bold">
        ลบบัญชีผู้ใช้งานถาวร
      </div>
      การลบข้อมูลจะไม่สามารถเรียกคืนบัญชีและข้อมูลของผู้ใช้งานกลับมาได้อีก
    </div>
    <Button
      icon="octicon:circle-slash"
      class="h-10"
      color="error"
      label="ลบบัญชีผู้ใช้งาน"
    />
  </div>
  <Separator class="mt-7 mb-5" />
</template>

<script lang="ts" setup>
import { useUserPageLoader } from '~/loaders/admin/user'

const props = defineProps<{
  profile: IUser
}>()

const isActive = ref(props.profile.is_active)
const user = useUserPageLoader()
const noti = useNotification()
const dialog = useDialog()

const onSwitch = () => {
  dialog.confirm({
    title: 'เปลี่ยนแปลงสถานะการใช้งานบัญชี',
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    user.updateRun(props.profile!.id, {
      data: {
        is_active: !isActive.value,
      },
    })
  })
}

useWatchTrue(() => user.update.status.isSuccess, () => {
  isActive.value = !isActive.value
  dialog.close()
  noti.success({
    title: 'อัพเดทข้อมูลสำเร็จ',
    description: 'ข้อมูลของคุณถูกอัพเดทเรียบร้อยแล้ว',
  })
})

useWatchTrue(() => user.update.status.isError, () => {
  dialog.close()

  noti.error({
    title: 'อัพเดทข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(user.update.status.errorData, 'เกิดข้อผิดพลาดในการส่งคำขอ กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
