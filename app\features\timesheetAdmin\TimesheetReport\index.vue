<template>
  <div class="mb-6 flex flex-col gap-4">
    <form>
      <div
        class="
            grid gap-x-10 gap-y-5
          "
      >
        <FormFields :options="formFields" />
      </div>
      <div class="mt-4">
        <Button
          size="xl"
          class="w-fit"
          @click="search"
        >
          Search
        </Button>
      </div>
    </form>
    <Table
      v-if="timesheet.fetch.status.isLoaded"
      v-model:column-pinning="columnPinning"
      :options="tableOptions"
      @pageChange="timesheet.fetchPage"
      @search="timesheet.fetchSearch"
    >
      <template #name-cell="{ row }">
        <AvatarProfile :item="row.original" />
      </template>
      <template
        v-for="day in daysInMonth"
        #[`day-${day}-cell`]="{ row }"
        :key="day"
      >
        <span
          class="flex h-full w-full items-center justify-end pr-2"
        >
          {{ row.original[`day-${day}`].toFixed(1) }}
        </span>
      </template>
      <template #total-cell="{ row }">
        <div
          class="flex h-full w-full items-center justify-end pr-2"
        >
          {{ row.original.total.toFixed(1) + '/' + totalTrackedTiming }}
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { INPUT_TYPES } from '#core/components/Form/types'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import { useAdminTimeSheetPageLoader } from '~/loaders/admin/timesheet'
import { useTeamPageLoader } from '~/loaders/admin/team'
import {
  startOfMonth, endOfMonth, format, getDaysInMonth,
  isSaturday,
  isSunday,
} from 'date-fns'
import { COLUMN_TYPES, type TableColumn } from '#core/components/Table/types'
import { useBreakpoints } from '@vueuse/core'

const team = useTeamPageLoader()
const companyHolidays = useHolidaysPageLoader()
const timesheet = useAdminTimeSheetPageLoader()
const currentDate = ref(new Date())
const breakpoints = useBreakpoints({
  mobile: 0,
  desktop: 1024,
})

const isMobile = breakpoints.smaller('desktop')

const columnPinning = computed(() => ({
  left: isMobile.value ? [] : ['name'],
  right: isMobile.value ? [] : ['total'],
}))

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    date: v.nullish(v.object({
      year: v.number(),
      month: v.number(),
    })),
    team_code: v.optional(v.array(v.string()), []),
  })),
  initialValues: {
    team_code: [],
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT_MULTIPLE,
    class: 'lg:col-span-1',
    props: {
      name: 'team_code',
      label: 'Team',
      placeholder: 'All Team',
      clearable: true,

      options: team.fetch.items.map((item) => {
        return {
          value: item.code,
          label: item.name,
        }
      }),
    },
  },
])

companyHolidays.fetchSetLoading()
team.fetchSetLoading()
timesheet.fetchSetLoading()
onMounted(() => {
  companyHolidays.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })

  team.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const groupByTimesheetDateItems = computed(() => {
  const result: Record<
    string,
    {
      display_name: string
      team: ITeam
      avatar_url?: string
      detail: Array<{ date: string
        totalTime: number }>
    }
  > = {}

  for (const item of timesheet.fetch.items) {
    const display_name
      = item.user.display_name || item.user.email.replace('@finema.co', '').toUpperCase()

    const avatar_url = item.user.avatar_url
    const team = item.user?.team
    const date = item.date
    const time = item.timing || 0

    if (!result[display_name]) {
      result[display_name] = {
        display_name,
        avatar_url,
        team,
        detail: [],
      }
    }

    const dayEntry = result[display_name].detail.find((d) => d.date === date)

    if (dayEntry) {
      dayEntry.totalTime += time
    } else {
      result[display_name].detail.push({
        date,
        totalTime: time,
      })
    }
  }

  return Object.values(result)
})

const tableRows = computed(() =>
  groupByTimesheetDateItems.value.map((person) => ({
    ...person,
    ...Object.fromEntries(
      daysInMonth.value.map((d) => [`day-${d}`, getNumericTimingByDate(person.detail, d)]),
    ),
    total: getTotal(person.detail),
  })),
)

const getNumericTimingByDate = (details: any[], day: number): number => {
  const targetDate = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), day)

  const targetDateStr = format(targetDate, 'yyyy-MM-dd')
  const entry = details.find((d) => format(d.date, 'yyyy-MM-dd') === targetDateStr)

  return entry ? entry.totalTime : 0
}

const getTotal = (detail: Array<{ totalTime: number }>) => {
  return detail.reduce((sum, entry) => sum + entry.totalTime, 0)
}

const daysInMonth = computed(() => {
  const days = getDaysInMonth(new Date(currentDate.value))

  return Array.from({
    length: days,
  }, (_, i) => i + 1)
})

const holidaysList = computed(() => companyHolidays?.fetch.items?.map((h) => h.date) ?? [])

const totalWorkingDays = computed(() => {
  const year = Number(currentDate.value.getFullYear())
  const month = Number(currentDate.value.getMonth())

  return useWorkingDayInMonth(year, month, holidaysList.value)
})

const totalTrackedTiming = computed(() => totalWorkingDays.value * 8)
const tableOptions = useTable<ITimesheet>({
  repo: timesheet,
  transformItems: () => tableRows.value as any[],
  options: {
    isHidePagination: false,
  },
  columns: () => {
    const baseColumns: TableColumn<ITimesheet>[] = [
      {
        accessorKey: 'name',
        header: 'Name',
        type: COLUMN_TYPES.TEXT,
      },
    ]

    if (daysInMonth.value) {
      daysInMonth.value.forEach((u) => {
        baseColumns.push({
          accessorKey: `day-${u}`,
          header: () => h('div', [`${u}`]),
          type: COLUMN_TYPES.TEXT,
          meta: {
            class: {
              td: (row: any) => {
                const value = row.getValue(`day-${u}`) ?? 0

                if (value > 0 && value < 8) return 'bg-warning text-right'
                if (value >= 8) return 'bg-success-300 text-right'
                if (isHoliday(u) || isWeekend(u)) return 'bg-[#EAECF0] text-right'
                if (value === 0) return 'bg-error-300 text-white text-right'

                return 'text-right'
              },
              th: 'text-center',
            },
          },
        } as TableColumn<ITimesheet>)
      })
    }

    baseColumns.push({
      accessorKey: 'total',
      header: 'Total',
      type: COLUMN_TYPES.TEXT,
      cell: ({
        row,
      }) => {
        return row.original.total.toFixed(1) + '/' + totalTrackedTiming.value
      },
      meta: {
        class: {
          td: (row: any) => {
            const value = row.getValue(`total`) ?? 0
            if (value >= totalTrackedTiming.value) return 'bg-success text-white'
            if (value < totalTrackedTiming.value) return 'bg-warning'

            return ''
          },
          th: 'text-center',
        },
      },
    } as TableColumn<any>)

    return baseColumns
  },
})

const isWeekend = (day: number): boolean => {
  const date = `${currentDate.value.getFullYear()}-${currentDate.value.getMonth() + 1}-${day}`

  return isSaturday(date) || isSunday(date)
}

const isHoliday = (day: number): boolean => {
  const month = String(currentDate.value.getMonth() + 1).padStart(2, '0')
  const date = String(day).padStart(2, '0')
  const targetDate = `${currentDate.value.getFullYear()}-${month}-${date}`

  return companyHolidays.fetch.items?.some((d) => format(d.date, 'yyyy-MM-dd') === targetDate) ?? false
}

const search = () => {
  timesheet.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      team_code: Array.isArray(form.values.team_code) && form.values.team_code.length > 0
        ? form.values.team_code.join(',')
        : undefined,
    },
  })
}

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
}, {
  deep: true,
})
</script>
