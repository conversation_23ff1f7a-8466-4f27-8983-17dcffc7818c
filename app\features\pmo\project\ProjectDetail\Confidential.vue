<template>
  <Card
    :ui="
      {
        body: 'bg-[#EAECF0]',
      }
    "
  >
    <div class="flex items-center gap-2 pb-3">
      <Icon
        name="mage:users"
        class="h-5 w-5 text-slate-500"
      />
      <h3 class="text-md font-semibold text-slate-700 dark:text-slate-200">
        {{ tabLabel }} Collaborators
      </h3>
    </div>

    <div class="flex items-start gap-4">
      <Loader :loading="confidential.fetch.status.isLoading">
        <template #loading>
          <Skeleton
            v-for="i in Math.floor(Math.random() * 4) + 3"
            :key="i"
            class="size-12 rounded-full"
          />
        </template>
        <div
          v-for="m in confidential.fetch.items"
          :key="m.id"
          class="relative mb-6 flex flex-col items-center"
        >
          <DropdownMenu
            :items="groupActions(m)"
            :disabled="!permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
          >
            <Button
              variant="ghost"
              color="neutral"
              class="p-0"
            >
              <Avatar
                :src="m.user.avatar_url"
                :alt="m.user.display_name"
                size="3xl"
                :ui="{
                  image: 'h-full w-full object-cover border-2 border-[#ffffff]',
                }"
                :chip="(():any => {
                  if (m.user.access_level.pmo === Permission.SUPER) {
                    return {
                      inset: true,
                      color: 'error',
                      position: 'top-left',
                    }
                  }
                  if (m.user.access_level.pmo === Permission.ADMIN) {
                    return {
                      inset: true,
                      color: 'info',
                      position: 'top-left',
                    }
                  }
                  return null
                })()"
              />
            </Button>
            <template #edit-leading>
              <div class="flex h-5 w-5 items-center justify-center">
                <img
                  src="/pmo/readonly.png"
                  alt="readonly"
                  class="w-3"
                />
              </div>
            </template>
            <template #main-leading>
              <div class="flex h-5 w-5 items-center justify-center">
                <img
                  src="/pmo/crown.png"
                  alt="readonly"
                  class="w-4"
                />
              </div>
            </template>
          </DropdownMenu>
          <div class="absolute bottom-[-23px] items-center justify-center justify-items-center text-center">
            <div class="">
              <PmoRole :role="(m?.[`${tab}_permission` as keyof ICollaborators] as PMO_PERMISSION)" />
              <div class="truncate text-xs text-slate-600 dark:text-slate-300">
                {{ m?.user.display_name }}
              </div>
            </div>
          </div>
          <div
            v-if="m?.[`${tab}_main` as keyof ICollaborators] === true "
            class="absolute top-[-8px]"
          >
            <img
              src="/pmo/crown.png"
              alt="readonly"
              class="w-4"
            />
          </div>
        </div>
        <Avatar
          v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
          icon="ant-design:user-add-outlined"
          size="3xl"
          alt="add"
          :ui="
            {
              root: 'bg-[#1570EF]  border-2 border-[#ffffff] cursor-pointer',
              icon: 'text-[#ffffff] ',
              image: 'h-full w-full rounded-[inherit] object-cover border-2 border-[#ffffff]',
            }
          "
          @click="onAdd"
        />
      </Loader>
    </div>
  </Card>
</template>

<script lang="ts" setup>
import ConfidentialFormModal from './ConfidentialFormModal.vue'
import type { DropdownMenuItem } from '@nuxt/ui'
import { PROJECT_TAB_KEY_TO_TYPE, type TabKey } from '~/constants/projectTab'
import { usePmoCollaboratorsByProjectIdPageLoader } from '~/loaders/pmo/project'

const props = defineProps<{
  tab: TabKey
  projectId: string
  tabLabel: TabLabel
}>()

const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const addModal = overlay.create(ConfidentialFormModal)
const confidential = usePmoCollaboratorsByProjectIdPageLoader(props.projectId)
const permission = useProjectPermission()

confidential.fetchSetLoading()
onMounted(() => {
  confidential.fetchPage(1, '' as string, {
    params: {
      limit: 999,
      tab_key: PROJECT_TAB_KEY_TO_TYPE[props.tab],
    },
  })
})

const groupActions = (value: ICollaborators): DropdownMenuItem[] => {
  return [
    {
      label:
    (value?.[`${props.tab}_permission` as keyof ICollaborators] as PMO_PERMISSION)
    === PMO_PERMISSION.MODIFY
      ? 'Make as Read Only'
      : 'Make as Modify',
      class: 'text-[#344054]',
      slot: 'edit' as const,

      onSelect() {
        confidential.updateRun(value.id, {
          data: {
            [`${props.tab}_permission`]:
          value?.[`${props.tab}_permission` as keyof ICollaborators]
          === PMO_PERMISSION.MODIFY
            ? PMO_PERMISSION.READONLY
            : PMO_PERMISSION.MODIFY,
          },
        })
      },
    },
    {
      label:
 (value?.[`${props.tab}_main` as keyof ICollaborators])
 !== true
   ? 'Make as Main Responsible'
   : 'Remove as Main Responsible',
      slot: 'main' as const,
      class: 'text-[#344054]',
      onSelect() {
        const isMain = value?.[`${props.tab}_main` as keyof ICollaborators] === true
        const actionText = isMain ? 'ยกเลิกสิทธิ์ Main Responsibility' : 'ให้สิทธิ์ Main Responsibility'

        dialog.confirm({
          title: 'ยืนยันการเปลี่ยนแปลง',
          description: `คุณต้องการ${actionText} \nกับผู้ใช้ "${value?.user?.display_name}" หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          confidential.updateRun(value.id, {
            data: {
              [`${props.tab}_main`]:
          value?.[`${props.tab}_main` as keyof ICollaborators]
          !== true,
            },
          })
        })
      },
    },
    {
      label: 'Delete Member',
      icon: 'i-heroicons-trash-20-solid',
      class: 'text-error',
      color: 'error',
      onSelect() {
        dialog.confirm({
          title: 'ยืนยันการลบ',
          description: `คุณต้องการลบสมาชิกโครงการ "${value.user.display_name}" หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          confidential.deleteRun(value.id)
        })
      },
    },
  ]
}

const onAdd = () => {
  addModal.open({
    tab: props.tab,
    projectId: props.projectId,
    currentMembers: confidential.fetch.items,
    status: () => confidential.add.status,
    onSubmit: (values: ICollaborators & { display_name: string }) => {
      if (values?.[`${props.tab}_main` as keyof ICollaborators] === true) {
        dialog.confirm({
          title: 'ยืนยันการเปลี่ยนแปลง',
          description: `คุณต้องการให้สิทธิ์ Main Responsibility \n กับผู้ใช้ "${values.display_name}" หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          confidential.addRun({
            data: {
              [`${props.tab}_permission`]: values?.[`${props.tab}_permission` as keyof ICollaborators],
              [`${props.tab}_main`]: values?.[`${props.tab}_main` as keyof ICollaborators],
              user_id: values.user_id,
            },
          })
        })
      } else {
        confidential.addRun({
          data: {
            [`${props.tab}_permission`]: values?.[`${props.tab}_permission` as keyof ICollaborators],
            [`${props.tab}_main`]: values?.[`${props.tab}_main` as keyof ICollaborators],
            user_id: values.user_id,
          },
        })
      }
    },
  })
}

useWatchTrue(
  () => confidential.add.status.isSuccess,
  () => {
    addModal.close()
    dialog.close()
    confidential.fetchPage(1, '' as string, {
      params: {
        limit: 999,
        tab_key: PROJECT_TAB_KEY_TO_TYPE[props.tab],
      },
    })

    noti.success({
      title: 'เพิ่มสมาชิกโครงการสำเร็จ',
      description: 'คุณได้เพิ่มสมาชิกโครงการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => confidential.add.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'เพิ่มสมาชิกโครงการไม่สำเร็จ',
      description: StringHelper.getError(confidential.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มสมาชิกโครงการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => confidential.update.status.isSuccess,
  () => {
    addModal.close()
    dialog.close()
    confidential.fetchPage(1, '' as string, {
      params: {
        limit: 999,
        tab_key: PROJECT_TAB_KEY_TO_TYPE[props.tab],
      },
    })

    noti.success({
      title: 'แก้ไขสมาชิกโครงการสำเร็จ',
      description: 'คุณได้แก้ไขสมาชิกโครงการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => confidential.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'แก้ไขสมาชิกโครงการไม่สำเร็จ',
      description: StringHelper.getError(confidential.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขสมาชิกโครงการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => confidential.delete.status.isSuccess,
  () => {
    dialog.close()
    confidential.fetchPage(1, '' as string, {
      params: {
        limit: 999,
        tab_key: PROJECT_TAB_KEY_TO_TYPE[props.tab],
      },
    })

    noti.success({
      title: 'ลบสมาชิกโครงการสำเร็จ',
      description: 'คุณได้ลบสมาชิกโครงการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => confidential.delete.status.isError,
  () => {
    dialog.close()

    noti.error({
      title: 'ลบสมาชิกโครงการไม่สำเร็จ',
      description: StringHelper.getError(confidential.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบสมาชิกโครงการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
