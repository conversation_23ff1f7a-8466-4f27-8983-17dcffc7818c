import { describe, it, expect } from 'vitest'
import { ColorHelper } from './ColorHelper'

describe('ColorHelper', () => {
  describe('getUsageStatsColor', () => {
    it('should return "error" for percentages greater than 80', () => {
      expect(ColorHelper.getUsageStatsColor(81)).toBe('error')
      expect(ColorHelper.getUsageStatsColor(90)).toBe('error')
      expect(ColorHelper.getUsageStatsColor(95)).toBe('error')
      expect(ColorHelper.getUsageStatsColor(100)).toBe('error')
    })

    it('should return "warning" for percentages between 71 and 80 (inclusive)', () => {
      expect(ColorHelper.getUsageStatsColor(71)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(75)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(80)).toBe('warning')
    })

    it('should return "success" for percentages 70 and below', () => {
      expect(ColorHelper.getUsageStatsColor(0)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(25)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(50)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(70)).toBe('success')
    })

    it('should handle edge cases correctly', () => {
      // Boundary values
      expect(ColorHelper.getUsageStatsColor(70)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(70.1)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(80)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(80.1)).toBe('error')
    })

    it('should handle decimal values correctly', () => {
      expect(ColorHelper.getUsageStatsColor(69.9)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(70.5)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(79.9)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(80.5)).toBe('error')
    })

    it('should handle negative values', () => {
      expect(ColorHelper.getUsageStatsColor(-10)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(-1)).toBe('success')
    })

    it('should handle values over 100%', () => {
      expect(ColorHelper.getUsageStatsColor(150)).toBe('error')
      expect(ColorHelper.getUsageStatsColor(200)).toBe('error')
    })

    it('should return correct color for real-world usage scenarios', () => {
      // Low usage scenarios
      expect(ColorHelper.getUsageStatsColor(10)).toBe('success') // Very low usage
      expect(ColorHelper.getUsageStatsColor(45)).toBe('success') // Normal usage
      expect(ColorHelper.getUsageStatsColor(65)).toBe('success') // Moderate usage

      // Warning scenarios
      expect(ColorHelper.getUsageStatsColor(72)).toBe('warning') // Getting high
      expect(ColorHelper.getUsageStatsColor(78)).toBe('warning') // High usage

      // Critical scenarios
      expect(ColorHelper.getUsageStatsColor(85)).toBe('error') // Very high usage
      expect(ColorHelper.getUsageStatsColor(95)).toBe('error') // Critical usage
    })

    it('should maintain consistent return type', () => {
      const validReturnValues = ['success', 'warning', 'error'] as const

      // Test a range of values to ensure all returns are valid
      const testValues = [0, 25, 50, 70, 71, 75, 80, 81, 90, 100]

      testValues.forEach((value) => {
        const result = ColorHelper.getUsageStatsColor(value)

        expect(validReturnValues).toContain(result)
        expect(typeof result).toBe('string')
      })
    })

    it('should work with usage stats integration', () => {
      // Simulate real usage scenarios with mock data
      const mockUsageStats = [
        {
          name: 'CPU',
          usage: 45,
        },
        {
          name: 'Memory',
          usage: 75,
        },
        {
          name: 'Storage',
          usage: 85,
        },
        {
          name: 'Network',
          usage: 30,
        },
      ]

      const results = mockUsageStats.map((stat) => ({
        ...stat,
        color: ColorHelper.getUsageStatsColor(stat.usage),
      }))

      expect(results[0].color).toBe('success') // CPU: 45%
      expect(results[1].color).toBe('warning') // Memory: 75%
      expect(results[2].color).toBe('error') // Storage: 85%
      expect(results[3].color).toBe('success') // Network: 30%
    })

    it('should handle floating point precision correctly', () => {
      // Test values that might cause floating point precision issues
      expect(ColorHelper.getUsageStatsColor(70.0)).toBe('success')
      expect(ColorHelper.getUsageStatsColor(70.00001)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(80.0)).toBe('warning')
      expect(ColorHelper.getUsageStatsColor(80.00001)).toBe('error')
    })

    it('should work with dashboard component integration', () => {
      // Simulate how it might be used in a Vue component
      const componentData = {
        diskUsage: 82,
        memoryUsage: 65,
        cpuUsage: 73,
      }

      const diskColor = ColorHelper.getUsageStatsColor(componentData.diskUsage)
      const memoryColor = ColorHelper.getUsageStatsColor(componentData.memoryUsage)
      const cpuColor = ColorHelper.getUsageStatsColor(componentData.cpuUsage)

      expect(diskColor).toBe('error') // 82% -> error
      expect(memoryColor).toBe('success') // 65% -> success
      expect(cpuColor).toBe('warning') // 73% -> warning
    })
  })

  describe('ColorHelper class structure', () => {
    it('should be a class with static methods', () => {
      expect(typeof ColorHelper).toBe('function')
      expect(typeof ColorHelper.getUsageStatsColor).toBe('function')
    })

    it('should not require instantiation', () => {
      // Should work without creating an instance
      expect(() => ColorHelper.getUsageStatsColor(50)).not.toThrow()
    })

    it('should have the correct method signature', () => {
      const method = ColorHelper.getUsageStatsColor

      expect(method.length).toBe(1) // Should accept 1 parameter
    })
  })
})
