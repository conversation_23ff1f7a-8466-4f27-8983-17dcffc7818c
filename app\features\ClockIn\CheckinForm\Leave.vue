<template>
  <div class="w-full rounded-lg border-[#EAECF0] bg-[#F9FAFB] p-5">
    <div class="flex justify-between">
      <div class="mb-3 flex items-center gap-2 font-bold">
        <img
          :src="iconImages?.[LEAVE_TYPE.ANNUAL]"
          alt="icon"
          class="size-6"
        />
        Leave
      </div>
      <ButtonActionIcon
        size="md"
        icon="material-symbols:close-rounded"
        @click="$emit('close')"
      />
    </div>
    <FormFields
      class="mt-3 grid gap-x-3 md:grid-cols-2"
      :options="formLeave"
    />
  </div>
</template>

<script lang="ts" setup>
import type { FormContext } from 'vee-validate'

defineEmits<{
  (e: 'close'): void
}>()

const {
  form,
  isEdit,
} = defineProps<{
  form: FormContext<Partial<any>>
  isEdit?: boolean
}>()

const formLeave = createFormFields(() => [

  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'Type of leaving',
      name: 'leave_type',
      required: true,
      options: LEAVE_OPTIONS,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'Leave period',
      name: 'leave_period',
      required: true,
      options: (form.values.type.length > 1 || form.values.checkin?.length > 1
        ? PERIOD_OPTIONS.filter((opt) => opt.value !== PERIOD.FULL_DAY)
        : PERIOD_OPTIONS),
    },
  },
  {
    class: 'lg:col-span-2',
    type: INPUT_TYPES.DATE_RANGE,
    isHide: form.values.leave_period !== PERIOD.MANY_DAYS,
    props: {
      label: 'From - To',
      name: 'date_range',
      required: true,
    },
  },
])
</script>
