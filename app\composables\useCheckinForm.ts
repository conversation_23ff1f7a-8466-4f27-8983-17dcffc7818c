import { format, eachDayOfInterval, parseISO } from 'date-fns'

export const useCheckinForm = () => {
  const officeAkvSchema = {
    office_akv_period: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Period')), ''),
  }

  const officeHqSchema = {
    office_hq_period: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Period')), ''),
  }

  const officeWFHSchema = {
    office_wfh_period: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Period')), ''),
  }

  const onsiteSchema = {
    checkin: v.array(
      v.object({
        location_onsite: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Location')), ''),
        onsite_period: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Period')), ''),
      }),
    ),
  }

  const businessTripSchema = {
    location_business_trip_onsite: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Location')), ''),
    business_period: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Period')), ''),
  }

  const leaveSchema = {
    leave_type: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Leave Type')), ''),
    leave_period: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือก Period')), ''),
    date_range: v.nullish(
      v.object({
        start: v.union([v.date(), v.string()]),
        end: v.union([v.date(), v.string()]),
      }),
    ),
  }

  const transformToRequest = (values: any): any => {
    const getDatesFromRange = (range: { start: string
      end: string }) => {
      if (!range?.start || !range?.end) return []

      return eachDayOfInterval({
        start: parseISO(range.start),
        end: parseISO(range.end),
      }).map((d) => format(d, 'yyyy-MM-dd'))
    }

    const result = values.type.flatMap((t: CheckinType) => {
      // --- Office HQ ---
      if (t === CHECKIN.OFFICE_HQ) {
        return [{
          type: t,
          period: values.office_hq_period || PERIOD.FULL_DAY,
          location: '',
          remarks: '',
          date: values.date || TimeHelper.getCurrentDate(),
        }]
      }

      // --- Office AKV ---
      if (t === CHECKIN.OFFICE_AKV) {
        return [{
          type: t,
          period: values.office_akv_period || PERIOD.FULL_DAY,
          location: '',
          remarks: '',
          date: values.date || TimeHelper.getCurrentDate(),
        }]
      }

      if (t === CHECKIN.WFH) {
        return [{
          type: t,
          period: values.office_wfh_period || PERIOD.FULL_DAY,
          location: '',
          remarks: '',
          date: values.date || TimeHelper.getCurrentDate(),
        }]
      }

      // --- Onsite ---
      if (t === CHECKIN.ONSITE) {
        return values.checkin.map((c) => ({
          type: t,
          period: c.onsite_period || PERIOD.FULL_DAY,
          location: c.location_onsite || '',
          remarks: '',
          date: values.date || TimeHelper.getCurrentDate(),
        }))
      }

      // --- Business Trip ---
      if (t === CHECKIN.BUSINESS_TRIP) {
        const dates = values.date_range?.start && values.date_range?.end
          ? getDatesFromRange(values.date_range)
          : [values.date || TimeHelper.getCurrentDate()]

        return dates.map((date) => ({
          type: t,
          period: values.business_period || PERIOD.FULL_DAY,
          location: values.location_business_trip_onsite || '',
          remarks: '',
          date: new Date(date).toISOString(),
        }))
      }

      // --- Default (WFH, Annual Leave, etc.) ---
      const dates = values.date_range?.start && values.date_range?.end
        ? getDatesFromRange(values.date_range)
        : [values.date || TimeHelper.getCurrentDate()]

      return dates.map((date) => ({
        type: CHECKIN.LEAVE,
        period: values.leave_period === PERIOD.MANY_DAYS ? PERIOD.FULL_DAY : values.leave_period,
        location: '',
        remarks: '',
        leave_type: values.leave_type,
        date: new Date(date).toISOString(),
      }))
    })

    return {
      items: result,
    }
  }

  interface OriginalFormat {
    office_hq_period?: string
    office_akv_period?: string
    checkin?: {
      onsite_period?: string
      location_onsite?: string
    }[]
    leave_period?: string
    location_business_trip_onsite?: string
    business_period?: string
    leave_type?: string
    type: string[]
  }

  const convertBackToCheckinForm = (json: { [key: string]: { type: string
    period?: string
    location?: string
    leave_type?: string } }): OriginalFormat => {
    const result: OriginalFormat = {
      type: json.type || [],
    }

    // หา period ของ OFFICE_HQ
    const hq = Object.values(json).find(
      (item: any) => item.type === CHECKIN.OFFICE_HQ,
    )

    if (hq) {
      result.office_hq_period = hq?.period
    }

    // หา period ของ OFFICE_AKV
    const akv = Object.values(json).find(
      (item: any) => item.type === CHECKIN.OFFICE_AKV,
    )

    if (akv) {
      result.office_akv_period = akv?.period
    }

    // ถ้ามี ONSITE
    const onsiteItems = Object.values(json).filter(
      (item: any) => item.type === CHECKIN.ONSITE,
    )

    if (onsiteItems.length) {
      result.checkin = onsiteItems.map((c: any) => ({
        onsite_period: c.period,
        location_onsite: c.location,
      }))
    }

    // ถ้ามี Business Trip
    const bt = Object.values(json).find(
      (item: any) => item.type === CHECKIN.BUSINESS_TRIP,
    )

    if (bt) {
      result.location_business_trip_onsite = bt?.location
      result.business_period = bt?.period
    }

    const leave = Object.values(json).find(
      (item: any) => item.type === CHECKIN.LEAVE,
    )

    if (leave) {
      result.leave_period = leave?.period
      result.leave_type = leave?.leave_type
    }

    return result
  }

  const sendSlackMsg = async (ICheckinItem: ICheckinItem[], isEdit?: boolean) => {
    const orderCheckinItem = ICheckinItem.sort((a, b) => PERIOD_ORDER[a.period] - PERIOD_ORDER[b.period])
    const auth = useAuth()

    const name = auth.me.value?.display_name || auth.me.value?.full_name
    const checkinTime = format(new Date(), 'HH:mm:ss')
    const editMode = isEdit ? '(edited)' : ''
    const checkinDate = orderCheckinItem[0]?.date ? parseISO(orderCheckinItem[0].date) : null
    const isCurrentCheckin = checkinDate
      ? checkinDate.getDate() === new Date().getDate()
      : false

    const locsString = orderCheckinItem.map((payload) => {
      if (
        payload.type === CHECKIN.ONSITE
        || payload.type === CHECKIN.BUSINESS_TRIP
      ) {
        return `${checkinIcons[payload.type]} ${CHECKIN_LABEL[payload.type as CheckinType]} (${payload.location}) (${PERIOD_LABEL[payload.period as PERIOD]})`
      } else if (payload.type === CHECKIN.LEAVE) {
        return `${checkinIcons[payload?.leave_type || payload.type]} ${CHECKIN_LABEL[payload.leave_type as CheckinType]} (${PERIOD_LABEL[payload.period as PERIOD]})`
      }

      return `${checkinIcons[payload.type]} ${CHECKIN_LABEL[payload.type as CheckinType]} (${PERIOD_LABEL[payload.period as PERIOD]})`
    })

    if (isCurrentCheckin) {
      const text = `${name} - ${locsString.join(' / ')} ---- at ${checkinTime} ${editMode}`

      await $fetch('/api/slack/notify', {
        method: 'POST',
        body: {
          text,
        },
      })
    }
  }

  return {
    officeAkvSchema,
    officeHqSchema,
    officeWFHSchema,
    onsiteSchema,
    businessTripSchema,
    leaveSchema,
    sendSlackMsg,
    transformToCheckinRequest: transformToRequest,
    convertBackToCheckinForm,
  }
}
