<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="เปลี่ยนสถานะโครงการ"
    description="Change Project Status"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields
          :options="formBudget"
        />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty ||status().isLoading"
            type="submit"
          >
            บันทึก
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void
}>()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    status: v.nullish(v.pipe(v.string())),
  })),
  initialValues: props.values,

})

const formBudget = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'status',
      label: 'Status',
      options: PROJECT_STATUS_OPTIONS,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as any)
})
</script>
