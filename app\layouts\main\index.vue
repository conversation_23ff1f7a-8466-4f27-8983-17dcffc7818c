<template>
  <div class="overflow-hidden pt-[80px]">
    <div class="flex min-h-[calc(100vh-80px)] flex-col">
      <Navbar />
      <main
        class="
        mx-auto w-full max-w-7xl flex-1 px-4 pb-20
        2xl:max-w-7/10
      "
      >
        <div
          v-if="app.pageMeta.title"
          class="
          mb-4 flex flex-col justify-between gap-1 md:mb-6 md:gap-4
          lg:flex-row
          lg:items-center
        "
        >
          <div class="flex flex-1 flex-col">
            <h1

              class="
              text-3xl font-bold wrap-break-word
              lg:max-w-2/3
            "
              :title="app.pageMeta.title"
            >
              {{ app.pageMeta.title }}
              <span id="page-title-extra" />
            </h1>

            <div id="page-subtitle" />
          <!-- <p
                v-if="app.pageMeta.sub_title"
                class="text-sm text-gray-400"
              >
                {{ app.pageMeta.sub_title }}
              </p> -->
          </div>
          <div id="page-header" />
        </div>
        <slot />
      </main>
      <div id="page-footer" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import Navbar from '~/layouts/main/components/Navbar.vue'

const app = useApp()
</script>

<style>

</style>
