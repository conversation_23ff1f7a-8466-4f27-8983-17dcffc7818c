export interface IDocument {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  tab_key: string
  group_id: string
  name: string
  sharepoint_url: string
  date: string
  type: string
  file_id: string
  created_by_id: string
  updated_by: IUser
  updated_by_id: string
  deleted_by_id: string | null
  group: IDocumentGroup
  file: IFile
  updated_by: IUser
  created_by: IUser
}

export interface IGroup {
  id: string
  detail: string
  tab_key: string
  group_id: string
  created_at: string
  updated_at: string
}

export type IDocumentRequest = Omit<IDocument, 'file'> & {
  file_id: string
}

export interface IUploadedFile {
  id: string
  created_at: string
  updated_at: string
  name: string
  path: string
  url: string
  size: number
  type: string
  checksum: string
  app: string
  created_by_id: string
}

export interface IFile {
  id: string
  created_at: string
  updated_at: string
  name: string
  path: string
  url: string
  extension?: string
  size: number
  type: string
  checksum: string
  app: string
  created_by_id: string
}

export interface IDocumentGroup {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  tab_key: string
  group_name: string
  sharepoint_url: string
  created_by_id: string
  updated_by: IUser
  updated_by_id: string
  deleted_by_id: string | null
}
