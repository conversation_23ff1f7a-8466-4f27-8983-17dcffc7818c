<template>
  <NuxtLayout>
    <SGAs />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import SGAs from '~/features/admin/SGAs/index.vue'

definePageMeta({
  layout: 'admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.admin.sgas.permissions,
  },
})

useSeoMeta({
  title: routes.admin.sgas.label,
})

useApp().definePage({
  title: routes.admin.sgas.label,
  breadcrumbs: [routes.admin.sgas],
  sub_title: 'Manage SGA',
})
</script>
