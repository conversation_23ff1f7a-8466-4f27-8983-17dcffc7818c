<template>
  <div class="my-8 w-full">
    <Breadcrumb
      :items="breadcrumbItems"
      class="mb-8"
    />
    <div class="flex flex-col justify-between lg:flex-row lg:items-end">
      <div>
        <h1 class="text-primary mb-4 flex items-center text-3xl font-bold">
          <Icon
            name="heroicons-outline:check-circle"
            class="mr-2 size-10"
          />
          All Checked-in today
        </h1>
        <h2 class="mb-4 font-bold">
          Where are team working ?
        </h2>
      </div>
      <FormFields :options="formFields" />
    </div>
  </div>
  <div class="flex w-full flex-col">
    <div
      v-for="(people, type) in groupedCheckins"
      :key="type"
    >
      <GroupSection
        v-if="people.length > 0"
        :type="type"
        :people="people"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CHECKIN } from '~/constants/checkin'
import { LEAVE_TYPE } from '~/constants/leave'
import GroupSection from './GroupSection.vue'
import { useCheckinsTodayLoader } from '~/loaders/admin/checkin'
import { useUserPageLoader } from '~/loaders/admin/user'

const checkins = useCheckinsTodayLoader()
const members = useUserPageLoader()
const breadcrumbItems = [
  routes.clockin.home,
  routes.clockin.today,
]

onMounted(() => {
  checkins.run({
    params: {
      date: TimeHelper.getCurrentDate(),
    },
  })

  members.fetchPage(1, '', {
    params: {
      limit: 999,
      is_active: true,
    },
  })
})

const groupedCheckins = computed(() => {
  const groups: Record<string, any[]> = {
    [CHECKIN.OFFICE_HQ]: [],
    [CHECKIN.OFFICE_AKV]: [],
    [CHECKIN.ONSITE]: [],
    [CHECKIN.WFH]: [],
    [LEAVE_TYPE.SICK]: [],
    [LEAVE_TYPE.MENSTRUAL]: [],
    [LEAVE_TYPE.ANNUAL]: [],
    [LEAVE_TYPE.BUSINESS]: [],
    [LEAVE_TYPE.BIRTHDAY]: [],
    [LEAVE_TYPE.ORDINATION]: [],
    not_checked_in: [],
  }

  if (!checkins.items.value) return groups

  const keyword = form.values.search?.trim().toLowerCase()

  const isMatch = (person: any) => {
    const profile = person.user || {}

    return (
      profile.display_name?.toLowerCase().includes(keyword)
      || profile.nickname?.toLowerCase().includes(keyword)
      || profile.email?.toLowerCase().includes(keyword)
    )
  }

  for (const item of checkins.items.value) {
    const type = item.type === CHECKIN.LEAVE ? item.leave_type || item.type : item.type

    if (!keyword || isMatch(item)) {
      if (groups[type]) {
        groups[type].push(item)
      }
    }
  }

  const checkedInEmails = new Set(checkins.items.value.map((item) => item.user?.email?.toLowerCase()))

  if (Array.isArray(members?.fetch.items)) {
    groups.not_checked_in = members?.fetch.items
      .filter((member) => {
        const email = member.email?.toLowerCase()
        const generatedEmail = member.display_name.toLowerCase() + '@finema.co'
        const match = !checkedInEmails.has(email) && !checkedInEmails.has(generatedEmail)

        return match && (!keyword || member.display_name?.toLowerCase().includes(keyword))
      })
      .map((member) => ({
        user: {
          ...member,
          email: member.email || member.display_name + '@finema.co',
        },
      }))
  }

  return groups
})

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    search: v.nullish(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      label: 'ค้นหาด้วยชื่อ/ชื่อเล่น/อีเมล',
      name: 'search',
      placeholder: 'ค้นหาด้วยชื่อ/ชื่อเล่น/อีเมล',
    },
  },
])
</script>
