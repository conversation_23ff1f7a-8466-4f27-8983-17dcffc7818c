export const useTimeSheetPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<ITimesheet>({
    baseURL: '/timesheets',
    getBaseRequestOptions: options.auth,
  })
}

export const useAdminTimeSheetPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<ITimesheet>({
    baseURL: '/admin/timesheets',
    getBaseRequestOptions: options.auth,
  })
}

export const useAdminTimeSheetSummaryReportListLoader = () => {
  const options = useRequestOptions()

  return useListLoader<ITimesheetSummaryReport>({
    url: '/admin/timesheets/summary-report',
    getRequestOptions: options.auth,
  })
}

// admin/timesheets/summary-report
