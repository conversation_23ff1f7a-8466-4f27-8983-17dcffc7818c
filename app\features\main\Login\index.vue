<template>
  <div class="flex min-h-screen flex-col overflow-hidden">
    <div class="mx-auto flex min-h-full max-w-xl flex-1 flex-col items-center justify-center gap-20 px-6">
      <img
        src="/logo.png"
        alt="logo"
      />
      <Button
        color="white"
        block
        class="mx-12 max-w-[370px] shadow-xs"
        icon="logos:slack-icon"
        @click="auth.login()"
      >
        Sign in with Slack
      </Button>
    </div>

    <div class="relative bg-[#353D59]">
      <img
        src="/cover/login.png"
        class="absolute -right-5 bottom-0 w-1/2 md:w-2/5"
        alt=""
      />
      <div class="mx-auto flex w-full max-w-7xl px-6 py-6">
        <p class="text-gray-400">
          © Copyright 2025 Finema
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const auth = useAuth()
const route = useRoute()
const noti = useNotification()

onMounted(() => {
  if (route.query.error) {
    noti.error({
      description: route.query.error as string,
    })
  }
})
</script>
