<template>
  <div
    :class="[
      'grid w-full gap-4',
      {
        'lg:grid-cols-2': !vertical,
      },
    ]"
  >
    <div
      v-for="item in items"
      :key="item.label"
      :class="[
        'lg:flex',
        {
          'flex-col': !inline,
          'flex-row': inline,
        },
        item.customClass,

      ]"
    >
      <p
        :class="[
          'mb-1',
          {
            'mb-2 block text-sm font-bold text-black': !inline,
            'mr-2 font-bold': inline,
          },
        ]"
      >
        {{ item.label }}
      </p>
      <component
        :is="item.component"
        v-if="item.component"
        v-bind="item.props"
      />
      <slot
        v-else-if="item.key && $slots[`${item.key}-item`]"
        :name="`${item.key}-item`"
        :row="item"
        :item="item"
        :value="item.value"
        :label="item.label"
      />
      <div
        v-else
        class="whitespace-pre-line text-gray-900"
      >
        <span
          v-if="shouldTruncateText(item)"
          v-show="!expandedItems[item.label]"
        >
          {{ truncateText(item.value || '-', item.max || 0) }}
          <button
            class="
              text-info-600 ml-1 cursor-pointer text-sm
              hover:text-info-800
            "
            @click="toggleExpanded(item.label)"
          >
            เพิ่มเติม
          </button>
        </span>
        <span
          v-if="shouldTruncateText(item)"
          v-show="expandedItems[item.label]"
        >
          {{ item.value || '-' }}
          <button
            class="
              text-info-600 ml-1 cursor-pointer text-sm
              hover:text-info-800
            "
            @click="toggleExpanded(item.label)"
          >
            แสดงน้อยลง
          </button>
        </span>
        <span v-if="item.type === TYPE_INFO_ITEM.BOOLEAN">
          <Icon
            class="size-5"
            :class="item.value ? 'text-success' : 'text-error'"
            :name="item.value
              ? 'material-symbols:check-circle-outline-rounded'
              : 'material-symbols:cancel-outline-rounded'"
          />
        </span>
        <span v-else-if="!shouldTruncateText(item)">
          {{ getValue(item) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

defineProps<{
  items: Array<{
    label: string
    value?: any
    component?: any
    props?: Record<string, any>
    max?: number
    key?: string
    type?: TYPE_INFO_ITEM
    customClass?: string

  }>
  vertical?: boolean
  inline?: boolean
}>()

const expandedItems = reactive<Record<string, boolean>>({})

const shouldTruncateText = (item: any): boolean => {
  return item.max && item.value && item.value.length > item.max
}

const truncateText = (text: string, maxLength: number): string => {
  if (!maxLength || text.length <= maxLength) return text

  return text.slice(0, maxLength) + '...'
}

const toggleExpanded = (label: string): void => {
  expandedItems[label] = !expandedItems[label]
}

const getValue = (item: any): string => {
  if (item.type === 'date') {
    return item.value
      ? new Date(item.value).toLocaleDateString('en', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })
      : '-'
  }

  if (item.value === undefined || item.value === null) {
    return '-'
  }

  if (typeof item.value === 'number') {
    if (item.type === 'currency') {
      return NumberHelper.toCurrency(item.value)
    }

    return NumberHelper.withFixed(item.value)
  }

  if (item.type === 'currency') {
    return NumberHelper.toCurrency(item.value)
  }

  if (item.type === 'number') {
    return NumberHelper.withFixed(item.value)
  }

  return item.value || '-'
}
</script>
