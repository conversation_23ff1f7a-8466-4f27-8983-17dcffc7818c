<template>
  <Container>
    <div class="my-8">
      <div class="mb-1 text-3xl">
        บัญชีของฉัน
      </div>
      <div class="text-[#475467]">
        My Account
      </div>
    </div>
    <Form @submit="onSubmit">
      <Card>
        <div class="mb-5 font-bold">
          ข้อมูลส่วนตัว (Personal Info.)
        </div>
        <div class="flex flex-col gap-5 md:flex-row md:gap-[40px] lg:gap-[80px]">
          <img
            :src="auth.me.value?.avatar_url"
            class="rounded-lg md:h-[230px] md:w-[230px] lg:h-[300px] lg:w-[300px]"
            alt="profile_mock"
          />

          <FormFields
            class="w-full"
            :options="fieldForm"
          />
          <!-- <Separator class="my-5" /> -->
          <!-- <div class="flex justify-end">
            <Button
              label="ตกลง"
              type="submit"
            />
          </div> -->
        </div>
      </Card>
      <Card class="mt-6">
        <div class="mb-5 font-bold">
          ข้อมูลทีม (Team Info.)
        </div>
        <FormFields

          class="grid w-full gap-4 md:grid-cols-2"
          :options="fieldRole"
        />
      </Card>
      <Separator class="my-6" />
      <div class="flex justify-end gap-4">
        <Button
          label="Cancel"
          variant="outline"
          color="neutral"
          :to="routes.home.to"
        />
        <Button
          :loading="auth.updateMe.status.value.isLoading"
          label="Save Changes"
          :disabled="!form.meta.value.dirty"
          type="submit"
        />
      </div>
    </Form>
  </Container>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'
import { useTeamPageLoader } from '~/loaders/admin/team'
import { format } from 'date-fns'

const auth = useAuth()
const noti = useNotification()
const team = useTeamPageLoader()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุอีเมล')), ''),
    full_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเต็ม')), ''),
    display_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเล่น')), ''),
    team_code: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกทีม')), ''),
    joined_date: v.nullish(v.pipe(v.string())),
    position: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุตำแหน่ง')), ''),
  })),
  initialValues: auth.me.value,
})

const fieldForm = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'อีเมล (Email)',
      name: 'email',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเต็ม (Full Name)',
      name: 'full_name',
      disabled: true,
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเล่น (Display Name)',
      name: 'display_name',
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันที่เริ่มทำงาน (Joined Date)',
      name: 'joined_date',
      maxDate: new Date(),
    },
  },
])

const fieldRole = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ทีม (Team)',
      name: 'team_code',
      loading: team.fetch.status.isLoading,
      options: ArrayHelper.toOptions(team.fetch.items, 'code', 'name'),
    },
  },

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ตำแหน่ง (Position)',
      name: 'position',
    },
  },
])

onMounted(() => {
  team.fetchPage(1, '', {
    params: {
      limit: 100,
    },
  })
})

const onSubmit = form.handleSubmit((values) => {
  auth.updateMe.run({
    data: {
      ...values,
      joined_date: format(new Date(values.joined_date), 'yyyy-MM-dd'),
    },
  })
})

useWatchTrue(() => auth.updateMe.status.value.isSuccess, () => {
  noti.success({
    title: 'อัพเดทข้อมูลสำเร็จ',
    description: 'ข้อมูลของคุณถูกอัพเดทเรียบร้อยแล้ว',
  })
})

useWatchTrue(() => auth.updateMe.status.value.isError, () => {
  noti.success({
    title: 'อัพเดทข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(auth.updateMe.status.value.errorData, 'ข้อมูลของคุณไม่สามารถอัพเดทได้'),

  })
})
</script>
