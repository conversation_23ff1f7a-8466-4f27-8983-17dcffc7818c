export const useAuth = () => {
  const {
    auth,
  } = useRequestOptions()

  const token = useCookie('token', {
    path: '/',
    maxAge: 60 * 60 * 24 * 365,
  })

  const isAuthenticated = computed(() => !!token.value)
  const me = defineStore('auth.me', () => {
    const value = ref<IUser | null>(null)

    const set = (user: IUser | null) => {
      value.value = user
    }

    return {
      value,
      set,
    }
  })()

  const fetchMe = (() => {
    return useObjectLoader<IUser>({
      method: 'GET',
      url: '/me',
      getRequestOptions: auth,
    })
  })()

  const updateMe = (() => {
    return useObjectLoader<IUser>({
      method: 'PUT',
      url: '/me',
      getRequestOptions: auth,
    })
  })()

  const login = async () => {
    const config = useRuntimeConfig()

    window.location.href = config.public.baseAPI + '/auth/slack-login'
  }

  useWatchTrue(() => fetchMe.status.value.isSuccess, () => {
    me.set(fetchMe.data.value)
  })

  useWatchTrue(() => fetchMe.status.value.isError, () => {
    token.value = undefined
    me.set(null)
  })

  const hasPermission = (module: UserModule, ...permission: Permission[]) => {
    if (!me.value?.access_level) {
      return false
    }

    return permission.includes(me.value.access_level[module])
  }

  const isSuperAdmin = computed(() => {
    return me.value?.access_level?.setting === Permission.SUPER
  })

  const menusNavbar = computed(() => [
    {
      label: 'Clock-In',
      icon: '/admin/clock-in-logo.png',
      to: routes.clockin.home.to,
    },
    {
      label: 'Timesheet',
      icon: '/admin/timesheet-logo.png',
      to: routes.timesheet.home.to,
    },
    ...(hasPermission(UserModule.PMO, Permission.ADMIN, Permission.USER, Permission.SUPER)
      ? [{
        label: 'PMO',
        icon: '/admin/pmo-logo.png',
        to: routes.pmo.home.to,
      }]
      : []),
    ...(hasPermission(UserModule.CHECKIN, Permission.ADMIN)
      ? [{
        label: 'Clock-In Admin',
        icon: '/admin/clock-in-admin-logo.png',
        to: routes.adminClockin.checkinDashboard.to,
      }]
      : []),
    ...(hasPermission(UserModule.TIMESHEET, Permission.ADMIN)
      ? [{
        label: 'Timesheet Admin',
        icon: '/admin/timesheet-admin-logo.png',
        to: routes.adminTimesheet.summaryReport.to,
      }]
      : []),

    ...(hasPermission(UserModule.SETTING, Permission.SUPER)
      ? [{
        label: 'Super Admin',
        icon: '/admin/super-admin-logo.png',
        to: routes.admin.users.to,
      }]
      : []),
  ])

  return {
    token,
    login,
    isAuthenticated,
    me,
    fetchMe,
    updateMe,
    hasPermission,
    isSuperAdmin,
    menusNavbar,

  }
}
