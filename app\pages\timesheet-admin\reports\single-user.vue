<template>
  <NuxtLayout>
    <SingleUserReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import SingleUserReport from '~/features/timesheetAdmin/SingleUserReport/index.vue'

definePageMeta({
  layout: 'timesheet-admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.adminTimesheet.singleUserReport.permissions,
  },
})

useSeoMeta({
  title: routes.adminTimesheet.singleUserReport.label,
})

useApp().definePage({
  title: routes.adminTimesheet.singleUserReport.label,
  breadcrumbs: [routes.adminTimesheet.singleUserReport],
  sub_title: 'Single User Report',
})
</script>
