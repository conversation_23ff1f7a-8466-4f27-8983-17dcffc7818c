import * as XLSX from 'xlsx-js-style'

// ประเภทข้อมูลสำหรับ Excel
export interface CellData {
  value: string | number
  style?: any
}

export interface RowData {
  cells: (string | number | CellData)[]
  style?: any
}

export interface ExcelData {
  headers: (string | CellData)[]
  rows: (RowData | (string | number | CellData)[])[]
}

export interface ExcelStyleOptions {
  headerStyle?: {
    font?: {
      bold?: boolean
      color?: { rgb?: string }
      size?: number
      name?: string
    }
    fill?: {
      fgColor?: { rgb?: string }
      patternType?: string
    }
    alignment?: {
      horizontal?: 'left' | 'center' | 'right'
      vertical?: 'top' | 'center' | 'bottom'
      wrapText?: boolean
    }
    border?: {
      top?: { style: string
        color: { rgb: string } }
      bottom?: { style: string
        color: { rgb: string } }
      left?: { style: string
        color: { rgb: string } }
      right?: { style: string
        color: { rgb: string } }
    }
  }
  dataStyle?: {
    font?: {
      size?: number
      name?: string
      color?: { rgb?: string }
    }
    alignment?: {
      horizontal?: 'left' | 'center' | 'right'
      vertical?: 'top' | 'center' | 'bottom'
      wrapText?: boolean
    }
    border?: {
      top?: { style: string
        color: { rgb: string } }
      bottom?: { style: string
        color: { rgb: string } }
      left?: { style: string
        color: { rgb: string } }
      right?: { style: string
        color: { rgb: string } }
    }
  }
  columnWidth?: number
  autoFitColumns?: boolean
}

interface SheetData {
  name: string
  data: ExcelData
  styleOptions?: ExcelStyleOptions
}

export class ExcelExporter {
  private defaultStyleOptions: ExcelStyleOptions = {
    headerStyle: {
      font: {
        bold: true,
        color: {
          rgb: 'FFFFFF',
        },
        size: 12,
        name: 'Calibri',
      },
      fill: {
        fgColor: {
          rgb: '4F81BD',
        },
        patternType: 'solid',
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
        wrapText: false,
      },
      border: {
        top: {
          style: 'thin',
          color: {
            rgb: '000000',
          },
        },
        bottom: {
          style: 'thin',
          color: {
            rgb: '000000',
          },
        },
        left: {
          style: 'thin',
          color: {
            rgb: '000000',
          },
        },
        right: {
          style: 'thin',
          color: {
            rgb: '000000',
          },
        },
      },
    },
    dataStyle: {
      font: {
        size: 11,
        name: 'Calibri',
        color: {
          rgb: '000000',
        },
      },
      alignment: {
        horizontal: 'left',
        vertical: 'center',
      },
      border: {
        top: {
          style: 'thin',
          color: {
            rgb: 'CCCCCC',
          },
        },
        bottom: {
          style: 'thin',
          color: {
            rgb: 'CCCCCC',
          },
        },
        left: {
          style: 'thin',
          color: {
            rgb: 'CCCCCC',
          },
        },
        right: {
          style: 'thin',
          color: {
            rgb: 'CCCCCC',
          },
        },
      },
    },
    columnWidth: 20,
    autoFitColumns: false,
  }

  constructor(private styleOptions: ExcelStyleOptions = {}) {
    this.styleOptions = this.mergeStyles(this.defaultStyleOptions, styleOptions)
  }

  private mergeStyles(defaultStyle: ExcelStyleOptions, customStyle: ExcelStyleOptions): ExcelStyleOptions {
    return {
      ...defaultStyle,
      ...customStyle,
      headerStyle: {
        ...defaultStyle.headerStyle,
        ...customStyle.headerStyle,
        font: {
          ...defaultStyle.headerStyle?.font,
          ...customStyle.headerStyle?.font,
        },
        fill: {
          ...defaultStyle.headerStyle?.fill,
          ...customStyle.headerStyle?.fill,
        },
        alignment: {
          ...defaultStyle.headerStyle?.alignment,
          ...customStyle.headerStyle?.alignment,
        },
        border: {
          ...defaultStyle.headerStyle?.border,
          ...customStyle.headerStyle?.border,
        },
      },
      dataStyle: {
        ...defaultStyle.dataStyle,
        ...customStyle.dataStyle,
        font: {
          ...defaultStyle.dataStyle?.font,
          ...customStyle.dataStyle?.font,
        },
        alignment: {
          ...defaultStyle.dataStyle?.alignment,
          ...customStyle.dataStyle?.alignment,
        },
        border: {
          ...defaultStyle.dataStyle?.border,
          ...customStyle.dataStyle?.border,
        },
      },
    }
  }

  private normalizeHeaders(headers: (string | CellData)[]): string[] {
    return headers.map((header) =>
      typeof header === 'string' ? header : String(header.value),
    )
  }

  private normalizeRows(rows: (RowData | (string | number | CellData)[])[]): (string | number)[][] {
    return rows.map((row) => {
      const cells = Array.isArray(row) ? row : row.cells

      return cells.map((cell) =>
        typeof cell === 'object' && 'value' in cell ? cell.value : cell as string | number,
      )
    })
  }

  private calculateColumnWidth(data: ExcelData): number[] {
    const normalizedHeaders = this.normalizeHeaders(data.headers)
    const normalizedRows = this.normalizeRows(data.rows)
    const widths: number[] = []

    // คำนวณจากหัวตาราง
    normalizedHeaders.forEach((header, index) => {
      widths[index] = Math.max(widths[index] || 0, String(header).length)
    })

    // คำนวณจากข้อมูล
    normalizedRows.forEach((row) => {
      row.forEach((cell, index) => {
        const cellLength = String(cell || '').length

        widths[index] = Math.max(widths[index] || 0, cellLength)
      })
    })

    // เพิ่มขนาดเล็กน้อยเพื่อ padding และจำกัดขนาดสูงสุด
    return widths.map((width) => Math.min(Math.max(width * 1.2, 10), 50))
  }

  // สร้าง worksheet จากข้อมูล
  private createWorksheet(data: ExcelData, styleOptions?: ExcelStyleOptions): XLSX.WorkSheet {
    const normalizedHeaders = this.normalizeHeaders(data.headers)
    const normalizedRows = this.normalizeRows(data.rows)
    const sheetData = [normalizedHeaders, ...normalizedRows]
    const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

    // ใช้ style options ที่ส่งเข้ามา หรือใช้ default
    const currentStyle = this.mergeStyles(this.styleOptions, styleOptions || {})

    if (worksheet['!ref']) {
      const range = XLSX.utils.decode_range(worksheet['!ref'])

      // Style header row
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const headerCell = worksheet[XLSX.utils.encode_cell({
          r: 0,
          c: C,
        })]

        if (headerCell) {
          // ใช้ style จาก header cell ถ้ามี หรือใช้ default header style
          const headerData = data.headers[C]

          if (typeof headerData === 'object' && 'style' in headerData && headerData.style) {
            headerCell.s = {
              ...currentStyle.headerStyle,
              ...headerData.style,
            }
          } else if (currentStyle.headerStyle) {
            headerCell.s = currentStyle.headerStyle
          }
        }
      }

      // Style data rows
      for (let R = range.s.r + 1; R <= range.e.r; ++R) {
        const rowIndex = R - 1 // index ของ row ในข้อมูล (ไม่นับ header)
        const rowData = data.rows[rowIndex]
        let rowStyle = null

        // ตรวจสอบว่า row มี style หรือไม่
        if (rowData && !Array.isArray(rowData) && 'style' in rowData) {
          rowStyle = rowData.style
        }

        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cell = worksheet[XLSX.utils.encode_cell({
            r: R,
            c: C,
          })]

          if (cell) {
            let cellStyle = currentStyle.dataStyle || {}

            // ใช้ row style ถ้ามี
            if (rowStyle) {
              cellStyle = {
                ...cellStyle,
                ...rowStyle,
              }
            }

            // ใช้ cell style ถ้ามี (มี priority สูงสุด)
            if (rowData) {
              const cells = Array.isArray(rowData) ? rowData : rowData.cells
              const cellData = cells[C]

              if (typeof cellData === 'object' && 'style' in cellData && cellData.style) {
                cellStyle = {
                  ...cellStyle,
                  ...cellData.style,
                }
              }
            }

            cell.s = cellStyle
          }
        }
      }
    }

    // กำหนดความกว้างคอลัมน์
    if (currentStyle.autoFitColumns) {
      const calculatedWidths = this.calculateColumnWidth(data)

      worksheet['!cols'] = calculatedWidths.map((width) => ({
        wch: width,
      }))
    } else if (currentStyle.columnWidth) {
      worksheet['!cols'] = normalizedHeaders.map(() => ({
        wch: currentStyle.columnWidth,
      }))
    }

    return worksheet
  }

  // สร้าง workbook จากข้อมูลเดียว
  createWorkbook(
    data: ExcelData,
    sheetName: string = 'Sheet1',
    styleOptions?: ExcelStyleOptions,
  ): XLSX.WorkBook {
    const worksheet = this.createWorksheet(data, styleOptions)

    return {
      SheetNames: [sheetName],
      Sheets: {
        [sheetName]: worksheet,
      },
    }
  }

  // สร้าง workbook แบบหลาย sheet
  createMultiSheetWorkbook(sheets: SheetData[]): XLSX.WorkBook {
    const workbook: XLSX.WorkBook = {
      SheetNames: [],
      Sheets: {},
    }

    sheets.forEach(({
      name, data, styleOptions,
    }) => {
      workbook.SheetNames.push(name)
      workbook.Sheets[name] = this.createWorksheet(data, styleOptions)
    })

    return workbook
  }

  // ดาวน์โหลด workbook
  download(workbook: XLSX.WorkBook, filename: string = 'export.xlsx'): void {
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    })

    const blob = new Blob([wbout], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')

    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // แปลง workbook เป็น buffer (สำหรับส่งไปยัง API)
  toBuffer(workbook: XLSX.WorkBook): ArrayBuffer {
    return XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
    })
  }

  // ฟังก์ชันรวม: สร้างและดาวน์โหลดแบบ single sheet
  export(
    data: ExcelData,
    filename: string = 'export.xlsx',
    sheetName: string = 'Sheet1',
    styleOptions?: ExcelStyleOptions,
  ): void {
    const workbook = this.createWorkbook(data, sheetName, styleOptions)

    this.download(workbook, filename)
  }

  // ฟังก์ชันรวม: สร้างและดาวน์โหลดแบบ multi sheet
  exportMultiSheet(sheets: SheetData[], filename: string = 'export.xlsx'): void {
    const workbook = this.createMultiSheetWorkbook(sheets)

    this.download(workbook, filename)
  }

  // อัปเดต default style options
  setDefaultStyle(options: ExcelStyleOptions): ExcelExporter {
    this.styleOptions = this.mergeStyles(this.styleOptions, options)

    return this
  }

  // รีเซ็ต style options เป็นค่าเริ่มต้น
  resetStyle(): ExcelExporter {
    this.styleOptions = {
      ...this.defaultStyleOptions,
    }

    return this
  }
}

// Factory function
export const createExcelExporter = (styleOptions?: ExcelStyleOptions): ExcelExporter => {
  return new ExcelExporter(styleOptions)
}

// Predefined styles สำหรับใช้งานง่าย
export const ExcelStyles = {
  modern: {
    headerStyle: {
      font: {
        bold: true,
        color: {
          rgb: 'FFFFFF',
        },
        size: 12,
      },
      fill: {
        fgColor: {
          rgb: '2E86AB',
        },
        patternType: 'solid',
      },
      alignment: {
        horizontal: 'center' as const,
        vertical: 'center' as const,
      },
    },
    dataStyle: {
      font: {
        size: 11,
        color: {
          rgb: '333333',
        },
      },
      alignment: {
        horizontal: 'left' as const,
        vertical: 'center' as const,
      },
    },
    autoFitColumns: true,
  },

  classic: {
    headerStyle: {
      font: {
        bold: true,
        color: {
          rgb: '000000',
        },
        size: 12,
      },
      fill: {
        fgColor: {
          rgb: 'D9D9D9',
        },
        patternType: 'solid',
      },
      alignment: {
        horizontal: 'center' as const,
        vertical: 'center' as const,
      },
    },
    columnWidth: 15,
  },

  colorful: {
    headerStyle: {
      font: {
        bold: true,
        color: {
          rgb: 'FFFFFF',
        },
        size: 12,
      },
      fill: {
        fgColor: {
          rgb: 'FF6B6B',
        },
        patternType: 'solid',
      },
      alignment: {
        horizontal: 'center' as const,
        vertical: 'center' as const,
      },
    },
    dataStyle: {
      alignment: {
        horizontal: 'center' as const,
        vertical: 'center' as const,
      },
    },
    autoFitColumns: true,
  },
}

// Utility functions สำหรับใช้งานแบบเร็ว
export class QuickExcel {
  // ส่งออกแบบง่าย
  static export(
    headers: (string | CellData)[],
    rows: (RowData | (string | number | CellData)[])[],
    filename: string = 'data.xlsx',
    sheetName: string = 'Sheet1',
    style: keyof typeof ExcelStyles = 'modern',
  ): void {
    const exporter = new ExcelExporter(ExcelStyles[style])

    exporter.export({
      headers,
      rows,
    }, filename, sheetName)
  }

  // ส่งออกแบบง่าย (backward compatibility)
  static exportSimple(
    headers: string[],
    rows: (string | number)[][],
    filename: string = 'data.xlsx',
    sheetName: string = 'Sheet1',
    style: keyof typeof ExcelStyles = 'modern',
  ): void {
    const exporter = new ExcelExporter(ExcelStyles[style])

    exporter.export({
      headers,
      rows,
    }, filename, sheetName)
  }

  // ส่งออกแบบ multi sheet
  static exportMultiSheet(
    sheets: Array<{
      name: string
      headers: (string | CellData)[]
      rows: (RowData | (string | number | CellData)[][])
      style?: keyof typeof ExcelStyles
    }>,
    filename: string = 'data.xlsx',
  ): void {
    const exporter = new ExcelExporter()
    const sheetData: SheetData[] = sheets.map((sheet) => ({
      name: sheet.name,
      data: {
        headers: sheet.headers,
        rows: sheet.rows,
      },
      styleOptions: sheet.style ? ExcelStyles[sheet.style] : undefined,
    }))

    exporter.exportMultiSheet(sheetData, filename)
  }
}

// Helper functions สำหรับแปลงข้อมูล
export class DataConverter {
  // แปลง array of objects เป็น ExcelData
  static fromObjectArray<T extends Record<string, any>>(
    objects: T[],
    columns?: (keyof T)[],
    rowStyler?: (obj: T, index: number) => any,
  ): ExcelData {
    if (objects.length === 0) {
      return {
        headers: [],
        rows: [],
      }
    }

    const headers = columns
      ? columns.map((col) => String(col))
      : Object.keys(objects[0])

    const rows = objects.map((obj, index) => {
      const cells = headers.map((header) => obj[header] ?? '')

      if (rowStyler) {
        const rowStyle = rowStyler(obj, index)

        return {
          cells,
          style: rowStyle,
        }
      }

      return cells
    })

    return {
      headers,
      rows,
    }
  }

  // แปลง nested object เป็น ExcelData
  static fromNestedObject(
    obj: Record<string, Record<string, string | number>>,
    rowKeyName: string = 'Key',
  ): ExcelData {
    const allColumns = new Set<string>()

    Object.values(obj).forEach((record) => {
      Object.keys(record).forEach((key) => allColumns.add(key))
    })

    const headers = [rowKeyName, ...Array.from(allColumns)]
    const rows = Object.entries(obj).map(([key, record]) => {
      const cells = [key]

      Array.from(allColumns).forEach((col) => {
        cells.push(record[col] ?? 0)
      })

      return cells
    })

    return {
      headers,
      rows,
    }
  }
}

// Helper functions สำหรับสร้าง style
export const CellStyles = {
  // สีพื้นหลัง
  background: (color: string) => ({
    fill: {
      fgColor: {
        rgb: color,
      },
      patternType: 'solid',
    },
  }),

  // สีตัวอักษร
  textColor: (color: string) => ({
    font: {
      color: {
        rgb: color,
      },
    },
  }),

  // ตัวหนา
  bold: () => ({
    font: {
      bold: true,
    },
  }),

  // จัดกึ่งกลาง
  center: () => ({
    alignment: {
      horizontal: 'center',
      vertical: 'center',
    },
  }),

  // เน้นสี (แดง)
  highlight: () => ({
    fill: {
      fgColor: {
        rgb: 'FFFF00',
      },
      patternType: 'solid',
    },
    font: {
      bold: true,
    },
  }),

  // ข้อมูลตัวเลข
  number: () => ({
    alignment: {
      horizontal: 'right',
      vertical: 'center',
    },
  }),

  // สถานะสำเร็จ
  success: () => ({
    fill: {
      fgColor: {
        rgb: 'D4EDDA',
      },
      patternType: 'solid',
    },
    font: {
      color: {
        rgb: '155724',
      },
    },
  }),

  // สถานะเตือน
  warning: () => ({
    fill: {
      fgColor: {
        rgb: 'FFF3CD',
      },
      patternType: 'solid',
    },
    font: {
      color: {
        rgb: '856404',
      },
    },
  }),

  // สถานะผิดพลาด
  error: () => ({
    fill: {
      fgColor: {
        rgb: 'F8D7DA',
      },
      patternType: 'solid',
    },
    font: {
      color: {
        rgb: '721C24',
      },
    },
  }),

  // รวมหลาย style
  combine: (...styles: any[]) => {
    return styles.reduce((combined, style) => {
      return {
        ...combined,
        font: {
          ...combined.font,
          ...style.font,
        },
        fill: {
          ...combined.fill,
          ...style.fill,
        },
        alignment: {
          ...combined.alignment,
          ...style.alignment,
        },
        border: {
          ...combined.border,
          ...style.border,
        },
      }
    }, {})
  },
}
