<template>
  <NuxtLayout>
    <Teams />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import Teams from '~/features/admin/Teams/index.vue'

definePageMeta({
  layout: 'admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.admin.teams.permissions,
  },
})

useSeoMeta({
  title: routes.admin.teams.label,
})

useApp().definePage({
  title: routes.admin.teams.label,
  breadcrumbs: [routes.admin.teams],
  sub_title: 'Team Management',
})
</script>
