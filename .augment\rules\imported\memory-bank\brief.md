---
type: "always_apply"
---

# FineWork Frontend - Project Brief

FineWork is an employee management system frontend built with Nuxt.js 4 and Vue 3. This is a Thai-language application serving Finema company's internal operations.

## Core Requirements

### Primary Features

- **Employee Clock-in System**: Track employee attendance with multiple check-in types (Office HQ, Office AKV, WFH, Onsite, various leave types)
- **Timesheet Management**: Time tracking and reporting for projects and users
- **PMO (Project Management Office) Functions**: Project and team management
- **Admin Portal**: Super admin functionality for user management, holidays, teams, SGAs, and projects

### Authentication

- Slack-based SSO authentication system
- Token-based session management with 1-year cookie expiration
- Role-based access control for different app sections

### User Roles & Access

- **Regular Users**: Access to clock-in, timesheet, and PMO user interfaces
- **Admin Users**: Additional access to admin dashboards and management features
- **Super Admin**: Full system administration capabilities

## Target Audience

Finema company employees and administrators requiring attendance tracking, time management, and project oversight capabilities.

## Technical Constraints

- Must integrate with existing Finema backend API (`finework-api.finema.dev`)
- Thai language interface required
- Multi-role access patterns
- Real-time attendance tracking
- Comprehensive reporting capabilities
