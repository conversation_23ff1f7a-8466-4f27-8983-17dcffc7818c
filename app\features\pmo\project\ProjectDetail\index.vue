<template>
  <TeleportSafe to="#page-subtitle">
    <BadgeStatus
      :value="(project.find.item?.status as PROJECT_STATUS)"
    />
  </TeleportSafe>
  <TeleportSafe to="#page-email">
    <div class="">
      {{ project.find.item?.email }}
    </div>
  </TeleportSafe>
  <TeleportSafe to="#page-tags">
    <div
      v-if="project.find.item?.tags && project.find.item?.tags.length > 0"
      class="flex flex-wrap items-center gap-1.5"
    >
      <Badge
        v-for="tag in project.find.item?.tags"
        :key="tag"
        :label="tag"
        variant="soft"
      />
    </div>
  </TeleportSafe>
  <TeleportSafe to="#page-actions">
    <DropdownMenu
      v-if="auth.hasPermission(UserModule.PMO, Permission.SUPER, Permission.ADMIN)"
      :content="{
        align: 'start',
        side: 'bottom',
      }"
      :items="pageActions"
    >
      <Button
        icon="tabler:settings"
        variant="outline"
        color="neutral"
      />
    </DropdownMenu>
  </TeleportSafe>
  <Tabs
    v-if="project.find.item"
    v-model="activeTab"
    color="neutral"
    variant="link"
    :items="navbar.sidebarPmoHorizontal(project.find.item)"
    class="mb-7 w-full flex-1"
    :ui="auth.hasPermission(UserModule.PMO, Permission.SUPER)
      ? {
        list: 'last-tab-right',
        label: 'text-sm',
      }
      : { label: 'text-sm' }"
    @update:modelValue="(value) => {
      router.push({
        query: { tab: value },
        replace: true,
      })
    }"
  />
  <div
    v-if="activeTab === PROJECT_TAB_TYPE.INFO"
    class="grid grid-cols-3 gap-2 md:grid-cols-4 md:gap-4"
  >
    <div class="col-span-3 h-fit bg-white p-4 md:col-span-1">
      <div class="overflow-x-auto">
        <Tabs
          v-model="sidebarActive"
          :orientation="isMobile ?'horizontal' :'vertical'"
          :content="false"
          :items="sidebar"
          class="w-full min-w-max"
          :ui="{
            list: 'w-full items-start ',
            label: 'text-sm',
          }"
        />
      </div>
    </div>
    <div class="col-span-3">
      <Info
        v-if="sidebarActive === SidebarTab.INFO"
        :project-id="project.find.item?.id || ''"
      />
      <InfoUpdate
        v-if="sidebarActive === SidebarTab.UPDATE"
        :project-id="project.find.item?.id || ''"
      />
      <InfoTimeline
        v-if="sidebarActive === SidebarTab.TIMELINE"
        :project-id="project.find.item?.id || ''"
      />
    </div>
  </div>
  <template v-if="activeTab === PROJECT_TAB_TYPE.CONFIDENTIAL">
    <Confidential
      v-if="permission.hasPermission(PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL], PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY)"
      :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL]"
      :project-id="project.find.item?.id || ''"
      :tab-label="PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.CONFIDENTIAL]"
    />
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
  <template v-if="activeTab === PROJECT_TAB_TYPE.BIDDING">
    <Bidding
      v-if="permission.hasPermission(PROJECT_TAB_KEY[PROJECT_TAB_TYPE.BIDDING], PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY)"
      :project-id="project.find.item?.id || ''"
    />
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
  <template v-if="activeTab === PROJECT_TAB_TYPE.PRESALES">
    <PreSale
      v-if="permission.hasPermission(PROJECT_TAB_KEY[PROJECT_TAB_TYPE.PRESALES], PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY)"
      :project-id="project.find.item?.id || ''"
    />
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
  <template v-if="activeTab === PROJECT_TAB_TYPE.SALES">
    <Sale
      v-if="permission.hasPermission(PROJECT_TAB_KEY[PROJECT_TAB_TYPE.SALES], PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY)"
      :project-id="project.find.item?.id || ''"
    />
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
  <template v-if="activeTab === PROJECT_TAB_TYPE.COLLABORATORS">
    <Collaborators
      v-if="auth.hasPermission(UserModule.PMO, Permission.SUPER, Permission.ADMIN)"
      :project-id="project.find.item?.id || ''"
    />
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
  <template v-if="activeTab === PROJECT_TAB_TYPE.PMO">
    <div v-if="permission.hasPermission(PROJECT_TAB_KEY[PROJECT_TAB_TYPE.PMO], PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY)">
      pmo
    </div>
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
  <template v-if="activeTab === PROJECT_TAB_TYPE.BIZCO">
    <div v-if="permission.hasPermission(PROJECT_TAB_KEY[PROJECT_TAB_TYPE.BIZCO], PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY)">
      bizco
    </div>
    <Card v-else>
      <Empty
        message="ไม่มีสิทธิ์การเข้าถึง"
      />
    </Card>
  </template>
</template>

<script lang="ts" setup>
import Info from '~/features/pmo/project/ProjectDetail/Info/index.vue'
import Bidding from '~/features/pmo/project/ProjectDetail/Bidding/index.vue'
import Confidential from '~/features/pmo/project/ProjectDetail/Confidential/index.vue'
import PreSale from '~/features/pmo/project/ProjectDetail/PreSale/index.vue'
import Sale from '~/features/pmo/project/ProjectDetail/Sale/index.vue'
import InfoTimeline from '~/features/pmo/project/ProjectDetail/Info/Timeline/index.vue'
import InfoUpdate from '~/features/pmo/project/ProjectDetail/Info/update.vue'
import Collaborators from '~/features/pmo/project/ProjectDetail/Collaborators/index.vue'
import type { NavigationMenuItem } from '@nuxt/ui'
import { usePmoProjectsPageLoader } from '~/loaders/pmo/project'
import { useBreakpoints } from '@vueuse/core'
import FormEmail from '~/features/pmo/project/ProjectDetail/Form/FormEmail.vue'
import FormStatus from '~/features/pmo/project/ProjectDetail/Form/FormStatus.vue'
import FormTag from '~/features/pmo/project/ProjectDetail/Form/FormTag.vue'
import FormUrl from '~/features/pmo/project/ProjectDetail/Form/FormUrl.vue'
import FormDelete from '~/features/pmo/project/ProjectDetail/Form/FormDelete.vue'

const breakpoints = useBreakpoints({
  mobile: 0,
  desktop: 1024,
})

const isMobile = breakpoints.smaller('desktop')
enum SidebarTab {
  INFO = 'info',
  TIMELINE = 'timeline',
  UPDATE = 'update',
}
const route = useRoute()
const auth = useAuth()
const sidebarActive = ref<SidebarTab>(SidebarTab.INFO)
const router = useRouter()
const tab = route.query.tab as string
const activeTab = ref(tab || PROJECT_TAB_TYPE.INFO)
const project = usePmoProjectsPageLoader()
const navbar = useProjectPermission()
const overlay = useOverlay()
const formEmail = overlay.create(FormEmail)
const formStatus = overlay.create(FormStatus)
const formTag = overlay.create(FormTag)
const formUrl = overlay.create(FormUrl)
const formDelete = overlay.create(FormDelete)
const dialog = useDialog()
const noti = useNotification()
const permission = useProjectPermission()
const sidebar = ref<NavigationMenuItem[]>([
  {
    label: 'ภาพรวมโครงการ',
    icon: 'lucide:message-square-text',
    value: SidebarTab.INFO,
  },
  {
    label: 'ข้อมูลอัปเดต',
    icon: 'mingcute:presentation-1-line',
    value: SidebarTab.UPDATE,
  },
  {
    label: 'ไทม์ไลน์เอกสาร',
    icon: 'fluent:timeline-24-regular',
    value: SidebarTab.TIMELINE,
  },
])

const pageActions = [
  [{
    label: 'Change Project Status',
    icon: 'mingcute:target-line',
    onSelect: () => formStatus.open({
      values: project.find.item,
      status: () => project.update.status,
      onSubmit: (payload: IPmoProject) => {
        project.updateRun(project.find.item?.id as string, {
          data: payload,
        })
      },
    }),
  }],
  [{
    label: 'Change Project URL',
    icon: 'majesticons:link-line',
    onSelect: () => formUrl.open({
      values: project.find.item,
      status: () => project.update.status,
      onSubmit: (payload: IPmoProject) => {
        project.updateRun(project.find.item?.id as string, {
          data: payload,
        })
      },
    }),
  }],
  [{
    label: 'Change Project Email',
    icon: 'material-symbols:mail-outline-rounded',
    onSelect: () => formEmail.open({
      values: project.find.item,
      status: () => project.update.status,
      onSubmit: (payload: IPmoProject) => {
        project.updateRun(project.find.item?.id as string, {
          data: payload,
        })
      },
    }),
  }],
  [{
    label: 'Change Project Tags',
    icon: 'tabler:tags',
    onSelect: () => formTag.open({
      values: project.find.item,
      status: () => project.update.status,
      onSubmit: (payload: IPmoProject) => {
        project.updateRun(project.find.item?.id as string, {
          data: payload,
        })
      },
    }),
  }],
  [{
    label: 'Delete Project',
    icon: 'i-heroicons-trash',
    color: 'error',
    onSelect: () =>
      formDelete.open({
        values: project.find.item,
        status: () => project.delete.status,
        onSubmit: () => {
          project.deleteRun(project.find.item?.id as string)
        },
      }),
  }],

]

useWatchTrue(
  () => project.update.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'อัพเดทโครงการสำเร็จ',
    })

    formEmail.close()
    formStatus.close()
    formTag.close()
    formUrl.close()

    router.replace(routes.pmo.project.projectBySlug(project.update.item?.slug || '').to)
    project.findRun(project.update.item?.id as string)
  },
)

useWatchTrue(
  () => project.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'อัพเดทโครงการไม่สำเร็จ',
      description: StringHelper.getError(project.update.status.errorData, 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'),
    })

    formEmail.close()
    formStatus.close()
    formTag.close()
    formUrl.close()
    project.findRun(project.update.item?.id as string)
  },
)

useWatchTrue(
  () => project.delete.status.isSuccess,
  () => {
    formDelete.close()
    noti.success({
      title: 'ลบโครงการสำเร็จ',
      description: 'โครงการถูกลบเรียบร้อยแล้ว',
    })

    router.replace(routes.pmo.project.projects.to)
  },
)

useWatchTrue(
  () => project.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบโครงการไม่สำเร็จ',
      description: StringHelper.getError(project.delete.status.errorData, 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>

<style scoped>
:deep(.last-tab-right > button:last-child) {
  margin-left: auto;
}
</style>
