// utils/download.ts
type DownloadOpts = {
  url: string // endpoint
  method?: 'GET' | 'POST' // default: POST
  filename?: string // ชื่อไฟล์ที่อยากได้ (ไม่ต้องใส่ .ext ก็ได้)
  ext?: string // บังคับนามสกุลไฟล์ เช่น 'pdf' | 'xlsx' ฯลฯ
}

const EXT_MIME: Record<string, string> = {
  pdf: 'application/pdf',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  xls: 'application/vnd.ms-excel',
  doc: 'application/msword',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  png: 'image/png',
  bmp: 'image/bmp',
}

const sanitizeName = (s: string) =>
  s.replace(/[\\/:*?"<>|]+/g, ' ').trim() || 'download'

const pickExt = (optsExt?: string, respType?: string, fallback = 'bin') => {
  if (optsExt) return optsExt.replace(/^\./, '').toLowerCase()

  // try from response content-type
  if (respType) {
    const ct = respType.split(';')[0].trim()
    const found = Object.entries(EXT_MIME).find(([, v]) => v === ct)?.[0]
    if (found) return found
  }

  return fallback
}

export const downloadDocument = async (opts: DownloadOpts) => {
  const {
    url,
    method = 'POST',
    filename = 'document',
    ext,
  } = opts

  const headers: Record<string, string> = {}

  const resp = await fetch(url, {
    method,
    headers,
  })

  if (!resp.ok) {
    const text = await resp.text().catch(() => '')
    throw new Error(`Download failed (${resp.status}) ${text || ''}`.trim())
  }

  // ลองอ่าน filename จาก Content-Disposition
  const cd = resp.headers.get('Content-Disposition') || ''
  const match = /filename\*?=(?:UTF-8''|")?([^";]+)/i.exec(cd)
  const serverFilename = match ? decodeURIComponent(match[1]) : null

  const blob = await resp.blob()

  // ตัดสินใจนามสกุล: จาก opts.ext > จาก resp.content-type > fallback
  const resolvedExt = pickExt(ext, resp.headers.get('Content-Type') || undefined, 'bin')
  const safeBase = sanitizeName(serverFilename || filename)
  const finalName = safeBase.toLowerCase().endsWith(`.${resolvedExt}`)
    ? safeBase
    : `${safeBase}.${resolvedExt}`

  const urlObj = window.URL.createObjectURL(blob)

  try {
    const a = document.createElement('a')

    a.href = urlObj
    a.download = finalName
    // iOS/Safari บางครั้งต้อง append ลง DOM
    document.body.appendChild(a)
    a.click()
    a.remove()
  } finally {
    window.URL.revokeObjectURL(urlObj)
  }
}
