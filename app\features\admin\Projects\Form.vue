<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไข Project' : 'เพิ่ม Project'"
    description="Project info."
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <input
          type="submit"
          hidden
        />
      </form>
    </template>
    <template #footer>
      <div class="flex w-full justify-end gap-3">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ยกเลิก
        </Button>
        <Button
          :loading="status().isLoading"
          :disabled="!form.meta.value.dirty"
          @click="onSubmit"
        >
          {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: IProject | null
  status: () => IStatus
  onSubmit: (values: IProject) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อ project')), ''),
    code: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุ project code')), ''),
    description: v.nullish(v.pipe(v.string())),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ Project Name',
      name: 'name',
      placeholder: ' ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ Project Code',
      name: 'code',
      placeholder: ' ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXTAREA,
    props: {
      label: 'Description',
      name: ' ',
      placeholder: '',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as IProject)
})

const generateProjectCode = (name: string): string => {
  return name
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/\s+/g, '_')
    .replace(/([A-Z]+)([A-Z][a-z])/g, '$1-$2')
    .toUpperCase()
}

watch(
  () => form.values.name,
  (newName) => {
    form.setFieldValue('code', generateProjectCode(newName || ''))
  },
)
</script>
