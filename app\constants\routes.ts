import type { NavigationMenuItem } from '@nuxt/ui'

export const routes = {
  home: {
    label: 'หน้าแรก',
    to: '/',
  },
  chooseTeam: {
    label: 'เลือกทีม',
    to: '/choose-team',
  },
  login: {
    label: 'เลือก',
    to: '/login',
  },
  logout: {
    label: 'Log out',
    to: '/api/auth/logout',
  },
  account: {
    profile: {
      label: 'โปรไฟล์',
      to: '/account/profile',
      icon: 'mage:user',
    },
  },

  announcements: {
    label: 'Finema Newsletter',
    to: '/announcements',
    icon: 'mage:announcement',
  },

  adminClockin: {
    checkinDashboard: {
      label: 'ภาพรวมเช็คอิน',
      icon: 'mage:dashboard',
      to: '/clockin-admin',
      permissions: ['clockin:ADMIN'],
    },
    checkinGroupReport: {
      label: 'ภาพรวมตามกลุ่ม',
      icon: 'heroicons-outline:user-group',
      to: '/clockin-admin/checkin-group-report',
      permissions: ['clockin:ADMIN'],
    },
    checkinIndividualReport: {
      label: 'ภาพรวมตามบุคคล',
      icon: 'mage:user',
      to: '/clockin-admin/checkin-individual-report',
      permissions: ['clockin:ADMIN'],
    },
  },
  adminTimesheet: {
    summaryReport: {
      label: 'ภาพรวม',
      icon: 'heroicons-outline:user-group',
      to: '/timesheet-admin/reports/summary',
      permissions: ['timesheet:ADMIN'],
    },
    timesheetReport: {
      label: 'รายงาน',
      icon: 'heroicons-outline:table-cells',
      to: '/timesheet-admin/reports/timesheet',
      permissions: ['timesheet:ADMIN'],
    },
    singleUserReport: {
      label: 'รายงานผู้ใช้งาน',
      icon: 'heroicons-outline:document-chart-bar',
      to: '/timesheet-admin/reports/single-user',
      permissions: ['timesheet:ADMIN'],
    },
    singleProjectReport: {
      label: 'รายงานโครงการ',
      icon: 'heroicons-outline:chart-bar',
      to: '/timesheet-admin/reports/single-project',
      permissions: ['timesheet:ADMIN'],
    },
  },
  admin: {
    users: {
      label: 'จัดการผู้ใช้งาน',
      icon: 'hugeicons:user-circle-02',
      to: '/admin/users',
      permissions: ['setting:SUPER'],
    },
    userById: (id: string, label = '') => ({
      label: label || 'รายละเอียดผู้ใช้งาน',
      to: `/admin/users/${id}`,
      permissions: ['setting:SUPER'],
    }),
    holidays: {
      label: 'จัดการวันหยุดบริษัท',
      icon: 'bi:calendar-date',
      to: '/admin/holidays',
      permissions: ['setting:SUPER'],
    },
    organizations: {
      label: 'จัดการกระทรวง/กรม',
      icon: 'proicons:bank',
      to: '/admin/organizations',
      permissions: ['setting:SUPER'],
    },
    sgas: {
      label: 'จัดการ SGAs',
      icon: 'hugeicons:layout-04',
      to: '/admin/sgas',
      permissions: ['setting:SUPER'],
    },
    teams: {
      label: 'จัดการทีม',
      icon: 'lucide:users',
      to: '/admin/teams',
      permissions: ['setting:SUPER'],
    },
    projects: {
      label: 'จัดการโปรเจค',
      icon: 'heroicons-outline:folder',
      to: '/admin/projects',
      permissions: ['setting:SUPER'],
    },

  },
  clockin: {
    home: {
      label: 'Clock-In',
      icon: 'i-heroicons-outline:location-marker',
      to: '/clockin',
    },
    today: {
      label: 'All Clocked-In Today',
      icon: 'i-heroicons-outline:check-circle',
      to: '/clockin/today',
    },
  },
  timesheet: {
    home: {
      label: 'Timesheet',
      icon: 'mage:dashboard',
      to: '/timesheet',
    },
  },
  pmo: {
    home: {
      label: 'แดชบอร์ด',
      icon: 'mage:dashboard',
      to: '/pmo',
    },
    overview: {
      label: 'ภาพรวมแผนงาน',
      icon: 'heroicons-outline:chart-bar',
      to: '/pmo/overview',
    },
    project: {
      projectBySlug: (slug: string, label = '') => ({
        label: label || 'ข้อมูลโครงการ',
        to: `/pmo/projects/${slug}`,
        permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
      }),
      projects: {
        label: 'จัดการโครงการ',
        to: '/pmo/projects',
        icon: 'ix:project',
        permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
      },
      projectCreate: {
        label: 'สร้างโครงการ',
        to: '/pmo/projects/create',
        permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
      },
    },
    saleWeeklyUpdate: {
      label: 'Sale Weekly Update',
      icon: 'heroicons-outline:document-text',
      to: '/pmo/sale-weekly-update',
      permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
    },
    biddingWeeklyUpdate: {
      label: 'Bidding Weekly Update',
      icon: 'heroicons-outline:document-text',
      to: '/pmo/bidding-weekly-update',
      permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
    },
    documentTemplate: {
      label: 'ตั้งค่าเอกสารต้นแบบ',
      icon: 'material-symbols:settings-cinematic-blur',
      to: '/pmo/document-template',
      permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
    },
    checklistSetting: {
      label: 'ตั้งค่างานที่ต้องทำ',
      icon: 'i-lucide-settings',
      to: '/pmo/checklist-setting',
      permissions: ['pmo:USER', 'pmo:ADMIN', 'pmo:SUPER'],
    },

  },

} as const

export const sidebarAdmin: NavigationMenuItem[] = [
  routes.admin.users,
  routes.admin.holidays,
  routes.admin.organizations,
  routes.admin.sgas,
  routes.admin.projects,
  routes.admin.teams,

]

export const sidebarUser: NavigationMenuItem[] = [

]

export const sidebarAdminClockin: NavigationMenuItem[] = [
  routes.adminClockin.checkinDashboard,
  routes.adminClockin.checkinGroupReport,
  routes.adminClockin.checkinIndividualReport,
]

export const sidebarAdminTimesheet: NavigationMenuItem[] = [
  routes.adminTimesheet.summaryReport,
  routes.adminTimesheet.timesheetReport,
  routes.adminTimesheet.singleUserReport,
  routes.adminTimesheet.singleProjectReport,
]

export const sidebarPmo: NavigationMenuItem[] = [
  routes.pmo.home,
  routes.pmo.overview,
  routes.pmo.project.projects,
  routes.pmo.saleWeeklyUpdate,
  routes.pmo.biddingWeeklyUpdate,
  routes.pmo.documentTemplate,
  routes.pmo.checklistSetting,
]
