# Getting Started with @finema/core

Welcome to the Finema UI Kit! This comprehensive guide will help you get up and running with our enterprise-grade Vue.js component library designed specifically for Nuxt applications.

## What is @finema/core?

@finema/core is a complete UI kit that provides:

- 🎨 **20+ Auto-registered Components** - Forms, tables, dialogs, loaders, and more
- 🔧 **Auto-imported Composables** - Dialog, notifications, form helpers, data loaders
- ✅ **Integrated Validation** - vee-validate + valibot with Thai localization
- 🎭 **Theming System** - Built on @nuxt/ui and Tailwind CSS
- 📦 **Quality-of-life Features** - Lodash auto-imports, masked inputs, and more

## Installation

### Prerequisites

- **Nuxt 3** (Vue 3, Vite)
- **Node.js 22+** (recommended)

### Install the Package

```bash
# npm
npm install @finema/core

# yarn
yarn add @finema/core

# pnpm
pnpm add @finema/core
```

## Configuration

### Basic Setup

Add the module to your `nuxt.config.ts`:

```ts
// nuxt.config.ts
export default defineNuxtConfig({
  modules: [
    '@finema/core'
  ]
})
```

### Optional Configuration

```ts
// nuxt.config.ts
export default defineNuxtConfig({
  modules: [
    '@finema/core'
  ],
  
  // Module options (configKey: "core")
  core: {
    // prefix: 'F', // Example: <F-Form />, <F-FormFields />
  },
  
  // App configuration
  appConfig: {
    core: {
      site_name: 'My App',
      color: '#3B82F6',
      limit_per_page: 20,
      time_zone: 'Asia/Bangkok'
    }
  }
})
```

## Your First Component

Once installed, all components are automatically available. Let's create a simple form:

```vue
<template>
  <div class="max-w-md mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">Contact Form</h1>
    
    <Form @submit="onSubmit">
      <FormFields :options="formFields" />
      
      <div class="mt-6 space-x-4">
        <Button type="submit" :loading="isSubmitting">
          Submit
        </Button>
        <Button variant="outline" @click="resetForm">
          Reset
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup lang="ts">
// Form validation schema using valibot
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.pipe(v.string(), v.minLength(2, 'Name must be at least 2 characters')),
      email: v.pipe(v.string(), v.email('Please enter a valid email')),
      message: v.pipe(v.string(), v.minLength(10, 'Message must be at least 10 characters'))
    })
  ),
  initialValues: {
    name: '',
    email: '',
    message: ''
  }
})

// Form fields configuration
const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'Full Name',
      placeholder: 'Enter your full name',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      placeholder: '<EMAIL>',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXTAREA,
    props: {
      name: 'message',
      label: 'Message',
      placeholder: 'Tell us what you think...',
      rows: 4,
      required: true
    }
  }
])

const isSubmitting = ref(false)
const notification = useNotification()

// Form submission handler
const onSubmit = form.handleSubmit(async (values) => {
  isSubmitting.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    notification.success({
      title: 'Success!',
      description: 'Your message has been sent successfully.'
    })
    
    form.resetForm()
  } catch (error) {
    notification.error({
      title: 'Error',
      description: 'Failed to send message. Please try again.'
    })
  } finally {
    isSubmitting.value = false
  }
}, moveToError)

const resetForm = () => {
  form.resetForm()
  notification.info({
    title: 'Form Reset',
    description: 'All fields have been cleared.'
  })
}
</script>
```

## Key Features Showcase

### Auto-imported Validation

All validation utilities are automatically available:

```vue
<script setup>
// These are auto-imported - no need to import manually!
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      username: v.pipe(v.string(), v.minLength(3)),
      age: v.pipe(v.number(), v.minValue(18)),
      terms: v.literal(true, 'You must accept the terms')
    })
  )
})
</script>
```

### Composables for Common Tasks

```vue
<script setup>
// Auto-imported composables
const dialog = useDialog()
const notification = useNotification()

const confirmDelete = async () => {
  const confirmed = await dialog.confirm({
    title: 'Delete Item',
    description: 'Are you sure you want to delete this item?',
    confirmText: 'Delete',
    cancelText: 'Cancel'
  })
  
  if (confirmed) {
    notification.success({ title: 'Item deleted successfully' })
  }
}
</script>
```

### Lodash Utilities

Most lodash functions are auto-imported with `_` prefix:

```vue
<script setup>
const users = ref([
  { id: 1, name: 'John', active: true },
  { id: 2, name: 'Jane', active: false }
])

// Auto-imported lodash functions
const activeUsers = computed(() => _filter(users.value, 'active'))
const userNames = computed(() => _map(users.value, 'name'))
const groupedUsers = computed(() => _groupBy(users.value, 'active'))
</script>
```

## Next Steps

- 📖 [Component Documentation](./components/) - Detailed component usage and examples
- 🔧 [Composables Guide](./composables/) - Available utilities and helpers  
- 🎨 [Theming Guide](./theming.md) - Customize colors and styling
- 📋 [Form System Guide](./forms.md) - Complete form building tutorial
- 📊 [Table System Guide](./tables.md) - Data display and management
- 🎮 [Playground Examples](../playground/) - Real-world usage patterns

## Quick Reference

### Available Components
- **Forms**: Form, FormFields, FieldWrapper + 15+ input types
- **Data Display**: Table, FlexDeck, Image, Empty
- **Feedback**: Dialog, Loader, Notification
- **Layout**: App, TeleportSafe

### Available Composables  
- **UI**: useDialog, useNotification, useApp
- **Forms**: useForm, useFieldHOC, createFormFields, moveToError
- **Data**: usePageLoader, useListLoader, useObjectLoader, useTable
- **Config**: useCoreConfig, useUiConfig
- **Utils**: useWatch*, useUpload, useFlexDeck

### Auto-imports
- **Validation**: vee-validate, valibot (as `v`)
- **Lodash**: Most functions as `_functionName`
- **Directives**: `v-maska` for input masking

## Common Patterns

### Building Data Tables

```vue
<template>
  <div class="space-y-6">
    <h1 class="text-2xl font-bold">Users</h1>

    <Table
      :options="tableOptions"
      @pageChange="store.fetchPageChange"
      @search="store.fetchSearch"
    />
  </div>
</template>

<script setup lang="ts">
// Define your data store (using usePageLoader)
const store = usePageLoader({
  url: '/api/users',
  transform: (response) => response.data
})

// Configure table with columns
const tableOptions = useTable({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: false
  },
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name'
    },
    {
      accessorKey: 'email',
      header: 'Email',
      type: COLUMN_TYPES.TEXT,
      meta: { max: 30 }
    },
    {
      accessorKey: 'avatar',
      header: 'Avatar',
      type: COLUMN_TYPES.IMAGE
    },
    {
      accessorKey: 'salary',
      header: 'Salary',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: { td: 'text-right', th: 'text-right' }
      }
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      type: COLUMN_TYPES.DATE_TIME
    }
  ]
})

// Load data on mount
onMounted(() => {
  store.fetchPage()
})
</script>
```

### Advanced Form with File Upload

```vue
<template>
  <Form @submit="onSubmit">
    <FormFields :options="advancedFields" />
    <Button type="submit" :loading="isSubmitting">
      Create Profile
    </Button>
  </Form>
</template>

<script setup lang="ts">
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      profile: v.object({
        name: v.string(),
        bio: v.string(),
        avatar: v.nullish(v.object({
          url: v.string(),
          name: v.string(),
          size: v.number()
        })),
        preferences: v.object({
          theme: v.string(),
          notifications: v.boolean()
        })
      })
    })
  )
})

const advancedFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'profile.name',
      label: 'Full Name',
      required: true
    }
  },
  {
    type: INPUT_TYPES.WYSIWYG,
    props: {
      name: 'profile.bio',
      label: 'Biography',
      toolbar: {
        bold: true,
        italic: true,
        bulletList: true,
        link: true
      }
    }
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      name: 'profile.avatar',
      label: 'Profile Picture',
      accept: '.jpg,.png,.gif',
      maxSize: 2048, // 2MB
      uploadPathURL: '/api/upload'
    }
  },
  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'profile.preferences.theme',
      label: 'Theme Preference',
      variant: 'card',
      options: [
        { value: 'light', label: 'Light', description: 'Clean and bright' },
        { value: 'dark', label: 'Dark', description: 'Easy on the eyes' },
        { value: 'auto', label: 'Auto', description: 'Follow system' }
      ]
    }
  },
  {
    type: INPUT_TYPES.TOGGLE,
    props: {
      name: 'profile.preferences.notifications',
      label: 'Email Notifications',
      description: 'Receive updates via email'
    }
  }
])

const isSubmitting = ref(false)

const onSubmit = form.handleSubmit(async (values) => {
  isSubmitting.value = true
  // Handle form submission
  console.log('Form values:', values)
  isSubmitting.value = false
}, moveToError)
</script>
```

### Working with Dialogs and Notifications

```vue
<template>
  <div class="space-y-4">
    <Button @click="showInfoDialog">Show Info</Button>
    <Button @click="showConfirmDialog" color="error">Delete Item</Button>
    <Button @click="showLoadingDialog">Process Data</Button>
  </div>
</template>

<script setup lang="ts">
const dialog = useDialog()
const notification = useNotification()

const showInfoDialog = () => {
  dialog.info({
    title: 'Information',
    description: 'This is an informational message.',
    confirmText: 'Got it'
  })
}

const showConfirmDialog = async () => {
  const confirmed = await dialog.confirm({
    title: 'Confirm Deletion',
    description: 'This action cannot be undone. Are you sure?',
    confirmText: 'Delete',
    cancelText: 'Cancel'
  })

  if (confirmed) {
    notification.success({
      title: 'Deleted',
      description: 'Item has been successfully deleted.'
    })
  }
}

const showLoadingDialog = async () => {
  const loadingDialog = dialog.loading({
    title: 'Processing...',
    description: 'Please wait while we process your request.'
  })

  // Simulate async operation
  await new Promise(resolve => setTimeout(resolve, 3000))

  loadingDialog.close()

  notification.success({
    title: 'Complete',
    description: 'Processing completed successfully!'
  })
}
</script>
```

## Troubleshooting

### Common Issues

**Components not auto-registering?**
- Ensure `@finema/core` is in your `modules` array in `nuxt.config.ts`
- Check that you're using Nuxt 3 (not Nuxt 2)

**Validation not working?**
- Make sure you're using `toTypedSchema()` wrapper around your valibot schema
- Check that field names match between schema and form fields

**Styles not loading?**
- The module automatically includes required CSS
- Ensure Tailwind CSS is properly configured in your project

**TypeScript errors?**
- Run `nuxi prepare` to generate type definitions
- Restart your TypeScript language server

### Getting Help

- 📖 Check the [detailed documentation](./components/)
- 🎮 Browse [playground examples](../playground/) for working code
- 🐛 Report issues on the project repository
- 💬 Ask questions in the team chat

## What's Next?

Now that you have the basics down, explore these advanced topics:

1. **[Form System Deep Dive](./forms.md)** - Master all input types and validation patterns
2. **[Table System Guide](./tables.md)** - Build complex data tables with sorting and filtering
3. **[Theming and Customization](./theming.md)** - Customize the look and feel
4. **[Data Loading Patterns](./data-loading.md)** - Efficient data management with composables
5. **[Advanced Examples](../playground/)** - Real-world implementation patterns

Happy coding with @finema/core! 🚀
