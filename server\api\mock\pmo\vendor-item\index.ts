import { defineEvent<PERSON>and<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '11111111-2222-3333-4444-555555555555',
      project_id: 'aaaa1111-bbbb-2222-cccc-333333333333',
      vendor_name: 'บริษัท ซอฟต์แวร์โซลูชัน จำกัด',
      item_name: 'ระบบจัดการเอกสารอิเล็กทรอนิกส์',
      item_detail: 'แพ็กเกจระบบ ECM พร้อม workflow และ storage 2TB',
      deliver_duration_day: 30,
      is_tor: true,
      is_implementation: true,
      is_training: true,
      is_user_manual: true,
      created_at: new Date('2025-02-01T09:00:00Z'),
      created_by_id: 'abcd1111-2222-3333-4444-555555555555',
      updated_at: new Date('2025-02-10T15:30:00Z'),
      updated_by_id: 'abcd1111-2222-3333-4444-555555555555',
      deleted_at: null,
      deleted_by_id: null,
    },
    {
      id: '66666666-7777-8888-9999-000000000000',
      project_id: 'aaaa1111-bbbb-2222-cccc-333333333333',
      vendor_name: 'FinWork Solutions',
      item_name: 'ระบบ HRM Cloud',
      item_detail: 'บริการ HRM SaaS พร้อมโมดูล Payroll และ Employee Self-service',
      deliver_duration_day: 45,
      is_tor: true,
      is_implementation: true,
      is_training: false,
      is_user_manual: true,
      created_at: new Date('2025-01-20T08:00:00Z'),
      created_by_id: 'efgh2222-3333-4444-5555-666666666666',
      updated_at: new Date('2025-02-05T14:15:00Z'),
      updated_by_id: 'efgh2222-3333-4444-5555-666666666666',
      deleted_at: null,
      deleted_by_id: null,
    },
    {
      id: 'aaaa0000-bbbb-1111-cccc-222222222222',
      project_id: 'aaaa1111-bbbb-2222-cccc-333333333333',
      vendor_name: 'Global Tech Co., Ltd.',
      item_name: 'Data Center Hardware',
      item_detail: 'Server Rack 42U พร้อมเครื่อง Server 4 nodes',
      deliver_duration_day: 60,
      is_tor: true,
      is_implementation: false,
      is_training: false,
      is_user_manual: false,
      created_at: new Date('2025-01-15T10:00:00Z'),
      created_by_id: 'ijkl3333-4444-5555-6666-777777777777',
      updated_at: new Date('2025-02-08T11:45:00Z'),
      updated_by_id: 'ijkl3333-4444-5555-6666-777777777777',
      deleted_at: null,
      deleted_by_id: null,
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
