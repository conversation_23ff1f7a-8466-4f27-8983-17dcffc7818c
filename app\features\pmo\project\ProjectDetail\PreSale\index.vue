<template>
  <div class="space-y-8">
    <Confidential
      :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.PRESALES]"
      :project-id="projectId"
      :tab-label="PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PRESALES]"
    />
    <PmoCardCollapsible no-underline>
      <CheckList
        :tab="PROJECT_TAB_TYPE.PRESALES"
        :project-id="projectId"
      />
    </PmoCardCollapsible>

    <VendorItemInfo :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.PRESALES]" />

    <PmoCardCollapsible
      title="เอกสารเทมเพลต"
      subtitle="Document Template"
    >
      <FileTemplate :tab="PROJECT_TAB_TYPE.PRESALES" />
    </PmoCardCollapsible>
    <GroupTemplateFile
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.PRESALES"
    />

    <PmoCardCollapsible
      title="ข้อมูลอัปเดต Pre-sales"
      sub-title="Comment"
    >
      <Comment
        :project-id="projectId"
        :channel="PROJECT_TAB_TYPE.PRESALES"
      />
    </PmoCardCollapsible>

    <Remark
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.PRESALES"
    />
  </div>
</template>

<script lang="ts" setup>
import CheckList from '~/container/Pmo/CheckList.vue'
import Confidential from '../Confidential.vue'
import Comment from '../Comment.vue'
import Remark from '../Remark.vue'
import VendorItemInfo from './VendorItemInfo.vue'
import FileTemplate from '../FileTemplate.vue'
import { PROJECT_TAB_KEY, PROJECT_TAB_TYPE } from '~/constants/projectTab'

defineProps<{ projectId: string }>()
</script>
