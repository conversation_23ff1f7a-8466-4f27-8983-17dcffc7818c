<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="Bidding History"
    description="view change history"
    :ui="{
      content: 'max-w-2xl',
    }"
  >
    <template #body>
      <div class="space-y-5">
        <div
          v-for="value in history.fetch.items"
          :key="value.bidding_value"
          class="rounded-md bg-[#F2F4F7] p-2"
        >
          <InfoItemList
            class="mt-4"
            :items="((): any => {
              const base = [
                {
                  value: value.bidding_type,
                  label: 'ประเภทจัดซื้อจัดจ้าง',

                },
                {
                  value: value.bidding_value,
                  label: 'มูลค่าประมูลโครงการ',
                  type: TYPE_INFO_ITEM.CURRENCY,
                },
                {
                  value: value.tender_entity,
                  label: 'ชื่อใช้เข้างาน',
                },
              ]

              const extra = [
                {
                  value: value.tender_date,
                  label: 'วันยื่นเสนอราคา',
                  type: TYPE_INFO_ITEM.DATE,
                },
                {
                  value: value.announce_date,
                  label: 'วันที่ประกาศ',
                  type: TYPE_INFO_ITEM.DATE,
                },
              ]

              return props.tab === PROJECT_TAB_TYPE.SALES ? base : [...base, ...extra]
            })() "
          />
          <div class="mt-3 text-xs font-bold">
            update at  {{ TimeHelper.displayDateTime(value.created_at) }}
            <span v-if="value.updated_by"> by {{ value.updated_by.display_name }}</span>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex w-full justify-end">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ปิด
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { useProjectBiddingVersionLoader } from '~/loaders/pmo/project'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  projectId: string
  tab: string
}>()

const history = useProjectBiddingVersionLoader(props.projectId as string)

onMounted(() => {
  history.fetchPage(1, '', {
    params: {
      tab_key: props.tab,
      limit: 999,
    },
  })
})
</script>
