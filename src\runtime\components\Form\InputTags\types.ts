import type {
  <PERSON>ieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface ITagsFieldProps extends IFieldProps {
  leadingIcon?: any
  trailingIcon?: any
  loading?: boolean
  loadingIcon?: any
  icon?: string
  maxLength?: string
  variant?: string
  size?: string
  deleteIcon?: string
}

export type ITagsField = IFormFieldBase<
  INPUT_TYPES.TAGS,
  ITagsFieldProps,
  {
    change?: (value: string[]) => void
    add?: (value: string) => void
    remove?: (value: string) => void
  }
>
