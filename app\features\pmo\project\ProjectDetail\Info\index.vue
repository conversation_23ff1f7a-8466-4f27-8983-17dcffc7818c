<template>
  <div class="space-y-8">
    <PmoCard
      title="งานที่ต้องทำ"
      subtitle="Checklist"
      no-underline
    >
      <div class="overflow-x-auto">
        <Tabs
          v-model="activeTabIndex"
          color="neutral"
          variant="link"
          :ui="{
            label: 'text-sm',
          }"
          :items="[
            // {
            //   value: PROJECT_TAB_TYPE.CONFIDENTIAL,
            //   label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.CONFIDENTIAL],
            // },
            {
              value: PROJECT_TAB_TYPE.SALES,
              label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.SALES],
            },
            {
              value: PROJECT_TAB_TYPE.PRESALES,
              label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PRESALES],
            },
            {
              value: PROJECT_TAB_TYPE.BIDDING,
              label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIDDING],
            },
          ]"
          class="mt-5 w-full min-w-max"
        />
      </div>
      <CheckList
        :tab="activeTabIndex"
        :project-id="projectId"
        readonly
      />
    </PmoCard>
  </div>
</template>

<script lang="ts" setup>
import CheckList from '~/container/Pmo/CheckList.vue'
import { PROJECT_TAB_TYPE } from '~/constants/projectTab'

defineProps<{ projectId: string }>()
const activeTabIndex = ref<PROJECT_TAB_TYPE>(PROJECT_TAB_TYPE.SALES)
</script>
