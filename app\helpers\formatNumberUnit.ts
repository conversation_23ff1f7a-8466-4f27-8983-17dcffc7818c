export const formatNumberUnit = (value: number | string, decimals = 2): string => {
  const num = Number(value)

  if (Number.isNaN(num)) return '0'

  const absValue = Math.abs(num)

  if (absValue >= 1_000_000_000) return (num / 1_000_000_000).toFixed(decimals) + 'B'
  if (absValue >= 1_000_000) return (num / 1_000_000).toFixed(decimals) + 'M'
  if (absValue >= 1_000) return (num / 1_000).toFixed(decimals) + 'K'

  return num.toString()
}
