<template>
  <div class="flex items-center gap-4">
    <Avatar
      v-if="auth.me?.value?.avatar_url"
      :src="auth.me?.value?.avatar_url"
      class="size-30"
    />
    <div>
      <div class="text-2xl font-bold">
        Good afternoon, {{ auth.me?.value?.display_name }}!
      </div>
      <div class="mt-2">
        Current time: <span class="font-bold uppercase">
          {{ currentTime }}
        </span>
      </div>
      <div class="mt-1 text-sm">
        {{ currentDate }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useNow, useDateFormat } from '@vueuse/core'

const auth = useAuth()

const now = useNow({
  interval: 1000,
})

const currentTime = useDateFormat(now, 'hh:mm a')
const currentDate = useDateFormat(now, 'dddd, MMMM D, YYYY')
</script>
