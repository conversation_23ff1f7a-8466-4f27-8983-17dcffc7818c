export enum PMOCommentChannel {
  OVERALL = 'OVERALL',
  CONFIDENTIAL = 'CONFIDENTIAL',
  SALES = 'SALES',
  PRESALES = 'PRESALES',
  BIDDING = 'BIDDING',
  PMO_PROGRESS = 'PMO_PROGRESS',
  PMO_HWSW = 'PMO_HWSW',
  PMO_WARRANTY = 'PMO_WARRANTY',
  PMO_LEGAL = 'PMO_LEGAL',
  BIZCO = 'BIZCO',
}

export const PMOCommentChannelLabel = {
  [PMOCommentChannel.OVERALL]: 'Overall',
  [PMOCommentChannel.CONFIDENTIAL]: 'Confidential',
  [PMOCommentChannel.SALES]: 'Sales',
  [PMOCommentChannel.PRESALES]: 'Pre-Sales',
  [PMOCommentChannel.BIDDING]: 'Bidding',
  [PMOCommentChannel.PMO_PROGRESS]: 'PMO Progress',
  [PMOCommentChannel.PMO_HWSW]: 'PMO HWSW',
  [PMOCommentChannel.PMO_WARRANTY]: 'PMO Warranty',
  [PMOCommentChannel.PMO_LEGAL]: 'PMO Legal',
  [PMOCommentChannel.BIZCO]: 'Bizco',
}

export const PMOCommentChannelKey = {
  [PMOCommentChannel.OVERALL]: 'overall',
  [PMOCommentChannel.CONFIDENTIAL]: 'confidential',
  [PMOCommentChannel.SALES]: 'sales',
  [PMOCommentChannel.PRESALES]: 'presales',
  [PMOCommentChannel.BIDDING]: 'bidding',
  [PMOCommentChannel.PMO_PROGRESS]: 'pmo_progress',
  [PMOCommentChannel.PMO_HWSW]: 'pmo_hwsw',
  [PMOCommentChannel.PMO_WARRANTY]: 'pmo_warranty',
  [PMOCommentChannel.PMO_LEGAL]: 'pmo_legal',
  [PMOCommentChannel.BIZCO]: 'bizco',
}

export const PMOCommentChannelKeyList = Object.values(PMOCommentChannel).map((channel) => ({
  value: channel,
  label: PMOCommentChannelLabel[channel],
  key: PMOCommentChannelKey[channel],
  permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
}))
