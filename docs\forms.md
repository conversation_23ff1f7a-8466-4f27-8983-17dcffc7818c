# Form System Guide

The @finema/core form system provides a comprehensive solution for building complex, validated forms with minimal boilerplate. Built on top of vee-validate and valibot, it offers 15+ input types, automatic validation, and consistent styling.

## Overview

### Key Features
- 🎯 **15+ Input Types** - Text, number, select, date, file upload, WYSIWYG, and more
- ✅ **Automatic Validation** - Built-in validation with valibot schemas
- 🎨 **Consistent Styling** - Unified design system across all inputs
- 📱 **Mobile Optimized** - Touch-friendly inputs and responsive layouts
- ♿ **Accessibility First** - ARIA labels, keyboard navigation, screen reader support
- 🔧 **Extensible** - Custom input types and validation rules

### Core Components
- **Form** - Main form wrapper with submission handling
- **FormFields** - Dynamic field renderer
- **FieldWrapper** - Individual field container with labels and errors
- **Input Components** - 15+ specialized input types

## Basic Form Setup

### 1. Form Schema Definition

```vue
<script setup lang="ts">
// Define validation schema using valibot
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      // Basic fields
      name: v.pipe(v.string(), v.minLength(2, 'Name must be at least 2 characters')),
      email: v.pipe(v.string(), v.email('Please enter a valid email')),
      
      // Optional fields
      phone: v.nullish(v.string()),
      
      // Complex validation
      password: v.pipe(
        v.string(),
        v.minLength(8, 'Password must be at least 8 characters'),
        v.regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
      ),
      
      // Nested objects
      profile: v.object({
        bio: v.nullish(v.string()),
        website: v.nullish(v.pipe(v.string(), v.url('Please enter a valid URL')))
      })
    })
  ),
  initialValues: {
    name: '',
    email: '',
    phone: '',
    password: '',
    profile: {
      bio: '',
      website: ''
    }
  }
})
</script>
```

### 2. Field Configuration

```vue
<script setup lang="ts">
// Create reactive field configuration
const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'Full Name',
      placeholder: 'Enter your full name',
      required: true,
      description: 'This will be displayed on your profile'
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      placeholder: '<EMAIL>',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'phone',
      label: 'Phone Number',
      mask: '###-###-####',
      placeholder: '************'
    }
  }
])
</script>
```

### 3. Form Template

```vue
<template>
  <Form @submit="onSubmit" class="space-y-6">
    <FormFields :options="formFields" />
    
    <div class="flex gap-4">
      <Button type="submit" :loading="isSubmitting">
        Submit
      </Button>
      <Button variant="outline" @click="form.resetForm()">
        Reset
      </Button>
    </div>
  </Form>
</template>
```

### 4. Submission Handling

```vue
<script setup lang="ts">
const isSubmitting = ref(false)
const notification = useNotification()

const onSubmit = form.handleSubmit(async (values) => {
  isSubmitting.value = true
  
  try {
    await $fetch('/api/submit', {
      method: 'POST',
      body: values
    })
    
    notification.success({
      title: 'Success!',
      description: 'Form submitted successfully'
    })
    
    form.resetForm()
  } catch (error) {
    notification.error({
      title: 'Error',
      description: 'Failed to submit form'
    })
  } finally {
    isSubmitting.value = false
  }
}, moveToError) // Automatically scroll to first error
</script>
```

## Input Types Reference

### Text Inputs

#### Basic Text Input
```ts
{
  type: INPUT_TYPES.TEXT,
  props: {
    name: 'username',
    label: 'Username',
    placeholder: 'Enter username',
    type: 'text', // 'text', 'email', 'password', 'url', 'tel'
    required: true
  }
}
```

#### Text with Suggestions
```ts
{
  type: INPUT_TYPES.TEXT,
  props: {
    name: 'city',
    label: 'City',
    suggestions: ['Bangkok', 'Chiang Mai', 'Phuket', 'Pattaya']
  }
}
```

#### Masked Input
```ts
{
  type: INPUT_TYPES.TEXT,
  props: {
    name: 'phone',
    label: 'Phone Number',
    mask: '###-###-####',
    placeholder: '************'
  }
}
```

#### Textarea
```ts
{
  type: INPUT_TYPES.TEXTAREA,
  props: {
    name: 'description',
    label: 'Description',
    rows: 4,
    placeholder: 'Enter description...'
  }
}
```

#### Search Input
```ts
{
  type: INPUT_TYPES.SEARCH,
  props: {
    name: 'search',
    label: 'Search',
    placeholder: 'Search for anything...',
    debounce: 300,
    clearable: true
  },
  on: {
    search: (query: string) => {
      console.log('Search:', query)
    },
    clear: () => {
      console.log('Search cleared')
    }
  }
}
```

### Number Input
```ts
{
  type: INPUT_TYPES.NUMBER,
  props: {
    name: 'age',
    label: 'Age',
    min: 0,
    max: 120,
    step: 1,
    placeholder: 'Enter your age'
  }
}
```

### Selection Inputs

#### Toggle Switch
```ts
{
  type: INPUT_TYPES.TOGGLE,
  props: {
    name: 'notifications',
    label: 'Email Notifications',
    description: 'Receive updates via email'
  }
}
```

#### Checkbox
```ts
{
  type: INPUT_TYPES.CHECKBOX,
  props: {
    name: 'terms',
    label: 'I agree to the terms and conditions',
    required: true
  }
}
```

#### Radio Buttons
```ts
{
  type: INPUT_TYPES.RADIO,
  props: {
    name: 'theme',
    label: 'Theme Preference',
    options: [
      { value: 'light', label: 'Light', description: 'Clean and bright' },
      { value: 'dark', label: 'Dark', description: 'Easy on the eyes' },
      { value: 'auto', label: 'Auto', description: 'Follow system' }
    ]
  }
}
```

#### Radio Cards
```ts
{
  type: INPUT_TYPES.RADIO,
  props: {
    name: 'plan',
    label: 'Subscription Plan',
    variant: 'card',
    options: [
      { value: 'basic', label: 'Basic', description: '$9/month - Perfect for individuals' },
      { value: 'pro', label: 'Pro', description: '$29/month - Best for teams' },
      { value: 'enterprise', label: 'Enterprise', description: 'Custom pricing - For large organizations' }
    ]
  }
}
```

#### Select Dropdown
```ts
{
  type: INPUT_TYPES.SELECT,
  props: {
    name: 'country',
    label: 'Country',
    placeholder: 'Select country',
    clearable: true,
    searchable: true,
    options: [
      { value: 'th', label: 'Thailand', icon: 'i-flag-th' },
      { value: 'us', label: 'United States', icon: 'i-flag-us' },
      { value: 'jp', label: 'Japan', icon: 'i-flag-jp' }
    ]
  }
}
```

#### Multi-Select
```ts
{
  type: INPUT_TYPES.SELECT_MULTIPLE,
  props: {
    name: 'skills',
    label: 'Skills',
    placeholder: 'Select your skills',
    options: [
      { value: 'vue', label: 'Vue.js' },
      { value: 'react', label: 'React' },
      { value: 'angular', label: 'Angular' },
      { value: 'node', label: 'Node.js' }
    ]
  },
  on: {
    search: (query: string) => {
      // Implement dynamic option loading
      console.log('Searching for:', query)
    }
  }
}
```

### Date and Time Inputs

#### Date Picker
```ts
{
  type: INPUT_TYPES.DATE_TIME,
  props: {
    name: 'birthdate',
    label: 'Birth Date',
    disabledTime: true, // Date only
    maxDate: new Date(), // Cannot select future dates
    format: 'dd/MM/yyyy'
  }
}
```

#### Date and Time Picker
```ts
{
  type: INPUT_TYPES.DATE_TIME,
  props: {
    name: 'appointment',
    label: 'Appointment Date & Time',
    minDate: new Date(),
    minTime: '09:00',
    maxTime: '17:00'
  }
}
```

#### Time Picker
```ts
{
  type: INPUT_TYPES.TIME,
  props: {
    name: 'meeting_time',
    label: 'Meeting Time',
    format: 'HH:mm',
    enableSeconds: false
  }
}
```

#### Date Range Picker
```ts
{
  type: INPUT_TYPES.DATE_RANGE,
  props: {
    name: 'vacation_dates',
    label: 'Vacation Dates',
    placeholder: 'Select date range',
    isDisabledMultiCalendar: false
  }
}
```

### File Upload

#### Basic File Upload
```ts
{
  type: INPUT_TYPES.UPLOAD_DROPZONE,
  props: {
    name: 'document',
    label: 'Upload Document',
    accept: '.pdf,.doc,.docx',
    maxSize: 5120, // 5MB in KB
    placeholder: 'Drop files here or click to browse'
  },
  on: {
    change: (file: File | undefined) => {
      console.log('File selected:', file)
    },
    delete: () => {
      console.log('File removed')
    }
  }
}
```

#### Auto-Upload with Progress
```ts
{
  type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
  props: {
    name: 'avatar',
    label: 'Profile Picture',
    accept: '.jpg,.png,.gif',
    maxSize: 2048, // 2MB
    uploadPathURL: '/api/upload',
    requestOptions: {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  },
  on: {
    success: (response: any) => {
      console.log('Upload successful:', response)
    },
    error: (error: any) => {
      console.log('Upload failed:', error)
    }
  }
}
```

### Rich Text Editor

#### WYSIWYG Editor
```ts
{
  type: INPUT_TYPES.WYSIWYG,
  props: {
    name: 'content',
    label: 'Article Content',
    placeholder: 'Start writing...',
    toolbar: {
      bold: true,
      italic: true,
      underline: true,
      strike: false,
      code: true,
      heading: true,
      paragraph: true,
      bulletList: true,
      orderedList: true,
      blockquote: true,
      codeBlock: true,
      horizontalRule: true,
      link: true,
      image: true,
      youtube: true,
      textAlign: true,
      undo: true,
      redo: true
    }
  },
  on: {
    change: (content: string) => {
      console.log('Content changed:', content)
    }
  }
}
```

## Advanced Validation Patterns

### Custom Validation Rules

```ts
// Custom validation function
const isUniqueUsername = async (value: string) => {
  const response = await $fetch(`/api/check-username?username=${value}`)
  return response.isUnique || 'Username is already taken'
}

// Schema with custom validation
const schema = v.object({
  username: v.pipe(
    v.string(),
    v.minLength(3),
    v.custom(isUniqueUsername) // Async validation
  )
})
```

### Conditional Validation

```ts
const schema = v.object({
  hasAddress: v.boolean(),
  address: v.nullish(v.string()),
  city: v.nullish(v.string()),
  zipCode: v.nullish(v.string())
})

// Add conditional validation in computed
const conditionalSchema = computed(() => {
  const baseSchema = {
    hasAddress: v.boolean()
  }

  if (form.values.hasAddress) {
    return v.object({
      ...baseSchema,
      address: v.pipe(v.string(), v.minLength(1, 'Address is required')),
      city: v.pipe(v.string(), v.minLength(1, 'City is required')),
      zipCode: v.pipe(v.string(), v.regex(/^\d{5}$/, 'Invalid zip code'))
    })
  }

  return v.object(baseSchema)
})
```

### Cross-Field Validation

```ts
const schema = v.object({
  password: v.pipe(v.string(), v.minLength(8)),
  confirmPassword: v.string()
}, [
  // Custom validation for the entire object
  v.custom((data) => {
    if (data.password !== data.confirmPassword) {
      return 'Passwords do not match'
    }
    return true
  })
])
```

## Dynamic Forms

### Conditional Fields

```vue
<script setup lang="ts">
const showAdvanced = ref(false)
const userType = ref('individual')

const formFields = createFormFields(() => {
  const baseFields = [
    {
      type: INPUT_TYPES.TEXT,
      props: { name: 'name', label: 'Name', required: true }
    },
    {
      type: INPUT_TYPES.SELECT,
      props: {
        name: 'userType',
        label: 'User Type',
        options: [
          { value: 'individual', label: 'Individual' },
          { value: 'business', label: 'Business' }
        ]
      },
      on: {
        change: (value: string) => {
          userType.value = value
        }
      }
    }
  ]

  // Add conditional fields based on user type
  if (userType.value === 'business') {
    baseFields.push(
      {
        type: INPUT_TYPES.TEXT,
        props: { name: 'companyName', label: 'Company Name', required: true }
      },
      {
        type: INPUT_TYPES.TEXT,
        props: { name: 'taxId', label: 'Tax ID', required: true }
      }
    )
  }

  // Add advanced fields if enabled
  if (showAdvanced.value) {
    baseFields.push(
      {
        type: INPUT_TYPES.TEXTAREA,
        props: { name: 'notes', label: 'Additional Notes' }
      }
    )
  }

  return baseFields
})
</script>

<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />

    <div class="mt-4">
      <Button
        variant="outline"
        @click="showAdvanced = !showAdvanced"
      >
        {{ showAdvanced ? 'Hide' : 'Show' }} Advanced Options
      </Button>
    </div>

    <Button type="submit" class="mt-6">Submit</Button>
  </Form>
</template>
```

### Field Arrays (Repeatable Fields)

```vue
<script setup lang="ts">
const contacts = ref([
  { name: '', email: '', phone: '' }
])

const addContact = () => {
  contacts.value.push({ name: '', email: '', phone: '' })
}

const removeContact = (index: number) => {
  contacts.value.splice(index, 1)
}

const contactFields = computed(() => {
  return contacts.value.flatMap((contact, index) => [
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `contacts.${index}.name`,
        label: `Contact ${index + 1} Name`,
        required: true
      }
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `contacts.${index}.email`,
        label: `Contact ${index + 1} Email`,
        type: 'email',
        required: true
      }
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        name: `contacts.${index}.phone`,
        label: `Contact ${index + 1} Phone`
      }
    }
  ])
})
</script>

<template>
  <Form @submit="onSubmit">
    <div v-for="(contact, index) in contacts" :key="index" class="border rounded-lg p-4 mb-4">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Contact {{ index + 1 }}</h3>
        <Button
          v-if="contacts.length > 1"
          variant="ghost"
          color="red"
          @click="removeContact(index)"
        >
          Remove
        </Button>
      </div>

      <FormFields :options="contactFields.slice(index * 3, (index + 1) * 3)" />
    </div>

    <Button variant="outline" @click="addContact" class="mb-6">
      Add Contact
    </Button>

    <Button type="submit">Submit</Button>
  </Form>
</template>
```

## Multi-Step Forms

```vue
<script setup lang="ts">
const currentStep = ref(1)
const totalSteps = 3

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      // Step 1: Personal Info
      firstName: v.pipe(v.string(), v.minLength(2)),
      lastName: v.pipe(v.string(), v.minLength(2)),
      email: v.pipe(v.string(), v.email()),

      // Step 2: Address
      address: v.pipe(v.string(), v.minLength(5)),
      city: v.pipe(v.string(), v.minLength(2)),
      zipCode: v.pipe(v.string(), v.regex(/^\d{5}$/)),

      // Step 3: Preferences
      newsletter: v.boolean(),
      theme: v.string()
    })
  )
})

const step1Fields = createFormFields(() => [
  { type: INPUT_TYPES.TEXT, props: { name: 'firstName', label: 'First Name', required: true } },
  { type: INPUT_TYPES.TEXT, props: { name: 'lastName', label: 'Last Name', required: true } },
  { type: INPUT_TYPES.TEXT, props: { name: 'email', label: 'Email', type: 'email', required: true } }
])

const step2Fields = createFormFields(() => [
  { type: INPUT_TYPES.TEXT, props: { name: 'address', label: 'Address', required: true } },
  { type: INPUT_TYPES.TEXT, props: { name: 'city', label: 'City', required: true } },
  { type: INPUT_TYPES.TEXT, props: { name: 'zipCode', label: 'Zip Code', required: true } }
])

const step3Fields = createFormFields(() => [
  { type: INPUT_TYPES.TOGGLE, props: { name: 'newsletter', label: 'Subscribe to newsletter' } },
  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'theme',
      label: 'Theme',
      options: [
        { value: 'light', label: 'Light' },
        { value: 'dark', label: 'Dark' }
      ]
    }
  }
])

const validateCurrentStep = async () => {
  const fieldsToValidate = {
    1: ['firstName', 'lastName', 'email'],
    2: ['address', 'city', 'zipCode'],
    3: ['newsletter', 'theme']
  }[currentStep.value]

  const isValid = await form.validate({ names: fieldsToValidate })
  return isValid.valid
}

const nextStep = async () => {
  const isValid = await validateCurrentStep()
  if (isValid && currentStep.value < totalSteps) {
    currentStep.value++
  } else if (!isValid) {
    moveToError(form.errors)
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const onSubmit = form.handleSubmit(async (values) => {
  // Final submission
  console.log('Form submitted:', values)
}, moveToError)
</script>

<template>
  <div class="max-w-2xl mx-auto">
    <!-- Progress indicator -->
    <div class="mb-8">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm font-medium">Step {{ currentStep }} of {{ totalSteps }}</span>
        <span class="text-sm text-gray-500">{{ Math.round((currentStep / totalSteps) * 100) }}% Complete</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        ></div>
      </div>
    </div>

    <Form @submit="onSubmit">
      <!-- Step content -->
      <div v-if="currentStep === 1">
        <h2 class="text-xl font-semibold mb-6">Personal Information</h2>
        <FormFields :options="step1Fields" />
      </div>

      <div v-if="currentStep === 2">
        <h2 class="text-xl font-semibold mb-6">Address Information</h2>
        <FormFields :options="step2Fields" />
      </div>

      <div v-if="currentStep === 3">
        <h2 class="text-xl font-semibold mb-6">Preferences</h2>
        <FormFields :options="step3Fields" />
      </div>

      <!-- Navigation -->
      <div class="flex justify-between mt-8">
        <Button
          v-if="currentStep > 1"
          variant="outline"
          @click="prevStep"
        >
          Previous
        </Button>

        <div class="ml-auto">
          <Button
            v-if="currentStep < totalSteps"
            @click="nextStep"
          >
            Next
          </Button>

          <Button
            v-if="currentStep === totalSteps"
            type="submit"
          >
            Submit
          </Button>
        </div>
      </div>
    </Form>
  </div>
</template>
```

## Form State Management

### Accessing Form State

```vue
<script setup lang="ts">
const form = useForm({ /* ... */ })

// Form values (reactive)
console.log(form.values) // Current form values
console.log(form.initialValues) // Initial values

// Form validation state
console.log(form.errors) // Current errors
console.log(form.meta.valid) // Is form valid
console.log(form.meta.dirty) // Has form been modified
console.log(form.meta.touched) // Has form been interacted with

// Individual field state
const nameField = useField('name')
console.log(nameField.value) // Field value
console.log(nameField.errorMessage) // Field error
console.log(nameField.meta.dirty) // Field dirty state
</script>
```

### Programmatic Form Control

```vue
<script setup lang="ts">
const form = useForm({ /* ... */ })

// Set field values
form.setFieldValue('name', 'John Doe')
form.setValues({ name: 'John', email: '<EMAIL>' })

// Set field errors
form.setFieldError('email', 'Email is already taken')
form.setErrors({ email: 'Invalid email', name: 'Name too short' })

// Reset form
form.resetForm() // Reset to initial values
form.resetForm({ values: { name: 'New Name' } }) // Reset with new values

// Validate form
const result = await form.validate()
console.log(result.valid) // boolean
console.log(result.errors) // error object

// Validate specific fields
await form.validate({ names: ['email', 'name'] })
</script>
```

## Error Handling and User Experience

### Custom Error Display

```vue
<template>
  <Form @submit="onSubmit">
    <!-- Global error summary -->
    <div v-if="Object.keys(form.errors).length > 0" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
      <h3 class="text-red-800 font-medium mb-2">Please fix the following errors:</h3>
      <ul class="text-red-700 text-sm space-y-1">
        <li v-for="(error, field) in form.errors" :key="field">
          {{ getFieldLabel(field) }}: {{ error }}
        </li>
      </ul>
    </div>

    <FormFields :options="formFields" />

    <Button type="submit">Submit</Button>
  </Form>
</template>

<script setup lang="ts">
const getFieldLabel = (fieldName: string) => {
  const labels = {
    name: 'Full Name',
    email: 'Email Address',
    phone: 'Phone Number'
  }
  return labels[fieldName] || fieldName
}
</script>
```

### Loading States and Feedback

```vue
<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />

    <!-- Submit button with loading state -->
    <Button
      type="submit"
      :loading="isSubmitting"
      :disabled="!form.meta.valid || isSubmitting"
      class="w-full"
    >
      {{ isSubmitting ? 'Submitting...' : 'Submit Form' }}
    </Button>

    <!-- Progress indicator for long forms -->
    <div v-if="isSubmitting" class="mt-4">
      <div class="flex items-center text-sm text-gray-600">
        <Icon name="i-heroicons-clock" class="mr-2" />
        Processing your request...
      </div>
    </div>
  </Form>
</template>
```

## Performance Optimization

### Debounced Validation

```vue
<script setup lang="ts">
// Debounce validation for better performance
const debouncedValidation = _debounce(async (field: string, value: any) => {
  await form.validateField(field)
}, 300)

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'username',
      label: 'Username'
    },
    on: {
      input: (value: string) => {
        debouncedValidation('username', value)
      }
    }
  }
])
</script>
```

### Lazy Loading for Large Forms

```vue
<script setup lang="ts">
// Load field configurations lazily
const loadFieldConfig = async (section: string) => {
  const { default: config } = await import(`./form-sections/${section}.ts`)
  return config
}

const currentSection = ref('basic')
const sectionFields = ref([])

watch(currentSection, async (section) => {
  sectionFields.value = await loadFieldConfig(section)
})
</script>
```

## Best Practices

### 1. Form Organization
- Group related fields logically
- Use clear, descriptive labels
- Provide helpful descriptions for complex fields
- Order fields in a logical flow

### 2. Validation Strategy
- Validate on blur for immediate feedback
- Use debounced validation for expensive checks
- Provide clear, actionable error messages
- Validate the entire form before submission

### 3. User Experience
- Show loading states during submission
- Provide success feedback after submission
- Auto-save drafts for long forms
- Use progressive disclosure for complex forms

### 4. Accessibility
- Ensure proper label associations
- Use ARIA attributes for screen readers
- Provide keyboard navigation
- Test with assistive technologies

### 5. Performance
- Use lazy loading for large forms
- Debounce expensive validation
- Minimize re-renders with proper reactivity
- Consider virtual scrolling for very long forms

## Troubleshooting

### Common Issues

**Form not submitting?**
- Check that validation schema is properly defined
- Ensure all required fields are filled
- Verify the submit handler is correctly bound

**Validation not working?**
- Make sure you're using `toTypedSchema()` wrapper
- Check that field names match between schema and form fields
- Verify valibot schema syntax

**Fields not updating?**
- Ensure field names are unique and properly nested
- Check that `createFormFields` is reactive
- Verify v-model bindings in custom components

**Performance issues?**
- Use debounced validation for expensive checks
- Consider lazy loading for large forms
- Optimize re-renders with proper key attributes

### Getting Help

- Check the [component documentation](./components/) for specific input types
- Review [playground examples](../playground/pages/form.vue) for working implementations
- Consult the [vee-validate documentation](https://vee-validate.logaretm.com/v4/) for advanced validation
- See [valibot documentation](https://valibot.dev/) for schema validation patterns

## Related Documentation

- [Form Component](./components/Form.md) - Main form wrapper
- [FormFields Component](./components/FormFields.md) - Dynamic field renderer
- [Input Components](./components/) - Individual input type documentation
- [Validation Guide](./validation.md) - Advanced validation patterns
- [Theming Guide](./theming.md) - Customizing form appearance
```
```
