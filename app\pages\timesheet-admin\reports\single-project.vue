<template>
  <NuxtLayout>
    <SingleProjectReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import SingleProjectReport from '~/features/timesheetAdmin/SingleProjectReport/index.vue'

definePageMeta({
  layout: 'timesheet-admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.adminTimesheet.singleProjectReport.permissions,
  },
})

useSeoMeta({
  title: routes.adminTimesheet.singleProjectReport.label,
})

useApp().definePage({
  title: routes.adminTimesheet.singleProjectReport.label,
  breadcrumbs: [routes.adminTimesheet.singleProjectReport],
  sub_title: 'Single Project Report',
})
</script>
