<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="ตั้งค่า SharePoint Folder"
    description="SharePoint Folder Setting"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty || status().isLoading"
            type="submit"
          >
            บันทึก
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    sharepoint_url: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุ sharepoint url'), v.url('รูปแบบ sharepoint url ไม่ถูกต้อง')), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'SharePoint Link',
      name: 'sharepoint_url',
      placeholder: 'https://finemaco.sharepoint.com/:f:/s/finema/Eus6EmWdR2hNijaIHHd5U2sBMBobVe3TN2j4eI...',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as any)
})
</script>
