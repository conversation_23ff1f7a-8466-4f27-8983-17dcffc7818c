<template>
  <NuxtLayout>
    <BiddingWeeklyUpdate />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import BiddingWeeklyUpdate from '~/features/admin/WeeklyUpdate/BiddingWeeklyUpdate/index.vue'

definePageMeta({
  layout: 'pmo',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.pmo.biddingWeeklyUpdate.permissions,
  },
})

useSeoMeta({
  title: routes.pmo.biddingWeeklyUpdate.label,
})

useApp().definePage({
  title: routes.pmo.biddingWeeklyUpdate.label,
  breadcrumbs: [routes.pmo.biddingWeeklyUpdate],
  sub_title: 'updated weekly by bidding',
})
</script>
