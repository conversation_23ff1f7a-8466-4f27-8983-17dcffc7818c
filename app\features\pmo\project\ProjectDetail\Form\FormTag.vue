<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="เปลี่ยนแท็กโครงการ"
    description="Change Project Tags"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields
          :options="formBudget"
        />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty ||status().isLoading"
            type="submit"
          >
            บันทึก
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void
}>()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    tags: v.array(v.string()),
  })),
  initialValues: props.values,

})

const formBudget = createFormFields(() => [
  {
    type: INPUT_TYPES.TAGS,
    props: {
      label: 'แท็ก (Tags)',
      name: 'tags',
      placeholder: 'เพิ่มแท็ก',
      description: 'กด Enter เพื่อเพิ่มแท็ก',
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as any)
})
</script>
