<template>
  <aside
    :class="[`
      flex h-full flex-col overflow-y-auto bg-white
      transition-all duration-300
    `]"
  >
    <div
      :class="['flex h-[72px] items-center p-5', {
        'justify-center': isCollapsed,
        'justify-between': !isCollapsed,
      }]"
    >
      <NuxtLink :to="routes.home.to">
        <img
          v-if="!isCollapsed"
          src="/logo-mini.png"
          alt="logo_mini_color"
          class="h-[27px]"
        />
      </NuxtLink>

      <PopoverNavbar />
    </div>
    <div class="bg-primary flex items-center justify-between px-7 py-4 font-bold text-white">
      {{ isCollapsed ? '' : 'Super Admin' }}

      <Icon
        v-if="!isCollapsed"
        name="fluent:arrow-circle-left-24-regular"
        class="size-[24px] cursor-pointer"
        @click="$emit('toggle-collapsed')"
      />
      <Icon
        v-else
        name="fluent:arrow-circle-right-24-regular"
        class="size-[24px] cursor-pointer"
        @click="$emit('toggle-collapsed')"
      />
    </div>

    <div class="flex-1 overflow-y-auto p-4">
      <NavigationMenu
        orientation="vertical"
        :items="navigationItems"
        :collapsed="isCollapsed"
        :popover="isCollapsed"
        :tooltip="isCollapsed"
        :ui="{
          list: 'space-y-2 ',
          label: [
            'text-sm font-bold text-gray-500 py-[12px] px-[10px] rounded-lg',
            'hover:text-primary',
          ],
          link: [
            'cursor-pointer text-sm font-bold text-gray-500 px-[10px] rounded-lg gap-3',
            'hover:text-primary',
            'data-active:before:bg-white data-active:before:rounded-lg data-active:text-primary font-semibold',
          ],
          linkLeadingIcon: 'group-data-[state=open]:text-current text-current size-[24px] group-hover:text-primary ',
          childList: 'border-none ms-0 pl-8 bg-gray-100 mt-2 py-1 rounded-lg',
          childLink: 'ps-0',
          childItem: 'ps-0',
        }"
        class="w-full justify-center"
      />
    </div>
    <div
      v-if="isMobile"
      class="border-t border-gray-100 p-3"
    >
      <div class="flex items-center justify-between gap-2">
        <div class="flex min-w-0 flex-1 items-center gap-3">
          <Avatar
            class="border-muted size-[32px] flex-shrink-0 border text-lg"
            icon="ri:user-line"
          />
          <div class="flex min-w-0 flex-1 flex-col">
            <p class="truncate text-sm font-bold">
              {{ auth.me.value?.display_name || auth.me.value?.full_name }}
            </p>
            <p class="text-muted truncate text-xs">
              {{ auth.me.value?.email || '' }}
            </p>
          </div>
        </div>
        <DropdownMenu
          arrow
          size="xl"
          :items="userMenuItems"
          :ui="{
            content: 'w-48',
          }"
        >
          <Button
            icon="ph:dots-three-outline-vertical-bold"
            variant="ghost"
            color="neutral"
            size="xs"
          />
        </DropdownMenu>
      </div>
    </div>
  </aside>
</template>

<script lang="ts" setup>
import type { NavigationMenuItem } from '@nuxt/ui'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { sidebarAdmin } from '~/constants/routes'

interface Props {
  isCollapsed: boolean
  isMobile?: boolean
}

defineEmits<{
  'toggle-collapsed': []
}>()

defineProps<Props>()

const route = useRoute()
const auth = useAuth()
const userMenuItems = [

  {
    label: 'View profile',
    icon: 'i-lucide-user',
    to: routes.account.profile.to,
  },

  {
    label: routes.logout.label,
    icon: 'i-lucide-log-out',
    to: routes.logout.to,
    external: true,
  },
]

const navigationItems = computed<NavigationMenuItem[]>(() => {
  return sidebarAdmin.map((item) => {
    let isAnyChildActive = false
    const mappedChildren = item.children?.map((child) => {
      const isChildCurrentlyActive = route.path === child.to

      if (isChildCurrentlyActive) {
        isAnyChildActive = true
      }

      return {
        active: isChildCurrentlyActive,
        class: 'hover:bg-transparent hover:text-gray-700 hover:font-bold py-2 data-active:before:bg-transparent data-active:text-gray-700 data-active:font-bold',
        icon: '',
        ...child,
      }
    })

    const selfIsActive = item.to ? route.path.startsWith(String(item.to)) : false

    let itemIsActive = selfIsActive || isAnyChildActive // A root item is active if its own link matches OR if any child is active

    if (item.to === '/admin' && route.path !== '/admin') {
      itemIsActive = false // Ensure the root item is not active if the current path is not exactly '/'
    }

    const itemDefaultOpen = item.children ? isAnyChildActive : false

    return {
      ...item,
      active: itemIsActive,
      class: itemIsActive
        ? 'before:bg-primary before:rounded-lg text-white'
        : '',
      defaultOpen: itemDefaultOpen || selfIsActive,
      open: itemDefaultOpen || selfIsActive,
      children: mappedChildren,
      to: mappedChildren ? undefined : item.to,
    }
  })
})
</script>
