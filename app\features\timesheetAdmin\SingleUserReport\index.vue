<template>
  <div class="mb-6 flex flex-col gap-4">
    <form
      @submit="onSubmit"
    >
      <FormFields :options="formFields" />
      <div class="mt-4 flex items-center gap-2">
        <Button
          size="xl"
          class="w-fit"
          type="submit"
          @click="onSubmit"
        >
          Search
        </Button>
        <Button
          v-if="timesheet.fetch.items && timesheet.fetch.items.length > 0"
          label="Export CSV"
          size="xl"
          class="w-fit"
          variant="outline"
          color="neutral"
          icon="proicons:add-square-multiple"
          @click="exportToExcel"
        />
      </div>
    </form>
    <div
      v-if="timesheet.fetch.status.isLoaded"
      class="grid"
    >
      <MonthlyReportProject
        v-if="timesheet && timesheet.fetch.items?.length > 0"
        :date="currentDate"
        :working-tracked-timing="workingTrackedTiming"
        :group-by-project="groupByProject"
        :total-tracked-timing="totalTrackedTiming"
      />
      <Card v-else>
        <Empty />
      </Card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import MonthlyReportProject from '~/container/Timesheet/MonthlyReportProject.vue'
import { startOfMonth, endOfMonth, format } from 'date-fns'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import { useAdminTimeSheetPageLoader } from '~/loaders/admin/timesheet'
import { useWorkingDayInMonth } from '~/composables/useWorkingDayInMonth'
import { useUserPageLoader } from '~/loaders/admin/user'
import { TRACKER_TYPE, TRACKER_TYPE_LABEL } from '~/constants/timesheet'

const currentDate = ref(new Date())
const companyHolidays = useHolidaysPageLoader()
const timesheet = useAdminTimeSheetPageLoader()
const user = useUserPageLoader()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    date: v.nullish(v.object({
      year: v.number(),
      month: v.number(),
    })),
    user_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกพนักงาน')), ''),
  })),
  initialValues: {
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },

})

companyHolidays.fetchSetLoading()
timesheet.fetchSetLoading()
onMounted(() => {
  companyHolidays.fetchPage()
  user.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const workingTrackedTiming = computed(() => {
  const items = timesheet.fetch.items ?? []

  return items.reduce((acc, item) => acc + item.timing, 0)
})

const trackerTypeOrder = [TRACKER_TYPE.INTERNAL,
  TRACKER_TYPE.EXTERNAL,
  TRACKER_TYPE.LEAVE,
  TRACKER_TYPE.SGA,
  TRACKER_TYPE.OT,
  TRACKER_TYPE.INTERNAL]

const orderMap = new Map(trackerTypeOrder.map((key, index) => [key, index]))
const groupByProject = computed(() => {
  const items = timesheet.fetch.items ?? []
  const groupedMap = new Map<string, any>()

  for (const item of items) {
    const typeKey = item.type
    const typeLabel = TRACKER_TYPE_LABEL[typeKey as TRACKER_TYPE] ?? typeKey

    if (!groupedMap.has(typeKey)) {
      groupedMap.set(typeKey, {
        type: typeKey,
        typeLabel,
        children: [],
      })
    }

    let name = 'Unknown'

    if (item.project_code) {
      name = item.project?.name
    } else if (item.sga_id) {
      name = item.sga?.name
    } else if (item.leave_type) {
      name = item.leave_type
    }

    const parent = groupedMap.get(typeKey)
    let child = parent.children.find((c: any) => c.name === name)

    if (!child) {
      child = {
        name,
        items: [],
      }

      parent.children.push(child)
    }

    child.items.push(item)
  }

  return Array.from(groupedMap.values())
    .sort((a, b) => {
      const aOrder = orderMap.get(a.type) ?? Infinity
      const bOrder = orderMap.get(b.type) ?? Infinity

      return aOrder - bOrder
    })
    .map((group) => ({
      ...group,
      children: group.children.map((child: any) => ({
        ...child,
        totalTiming: child.items.reduce((acc: number, item: ITimesheet) => acc + item.timing, 0),
      })),
      totalTiming: group.children.reduce(
        (acc: number, child: any) =>
          acc + child.items.reduce((acc2: number, item: ITimesheet) => acc2 + item.timing, 0),
        0,
      ),
    }))
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'user_id',
      label: 'Member',
      required: true,
      placeholder: 'Select Member',
      loading: !user.fetch.status.isLoaded,
      options: user.fetch.items?.map((item: IUser) => ({
        label: item.display_name,
        value: item.id,
        avatar: {
          src: item.avatar_url,
          alt: item.display_name,
        },
      })) || [],
      searchable: true,
    },
  },
])

const holidaysList = computed(() => companyHolidays?.fetch.items?.map((h) => h.date) ?? [])

const totalWorkingDays = computed(() => {
  return useWorkingDayInMonth(currentDate.value?.getMonth() || 1,
    currentDate.value?.getMonth() || 1 + 1, holidaysList.value)
})

const totalTrackedTiming = computed(() => totalWorkingDays.value * 8)

const onSubmit = form.handleSubmit((values) => {
  timesheet.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      user_id: values.user_id,
    },
  })
})

const convertTimesheetToExcel = (
  timesheetData: any[],
  workingTrackedTiming: number,
  totalTrackedTiming: number,
  date: Date,
): ExcelData => {
  const month = date.getMonth() + 1 // getMonth() returns 0-11
  const year = date.getFullYear()
  const utilizationRate = totalTrackedTiming > 0 ? (workingTrackedTiming / totalTrackedTiming * 100).toFixed(2) : '0.00'

  const rows: (RowData | (string | number | CellData)[])[] = []

  // Header section
  rows.push({
    cells: ['Month Summary Report', '', ''],
    style: CellStyles.combine(
      CellStyles.bold(),
      CellStyles.center(),
      {
        font: {
          size: 16,
          color: {
            rgb: 'FFFFFF',
          },
        },
      },
    ),
  })

  // Month info
  rows.push([
    `Month: ${month}/${year}`,
    '',
    '',
  ])

  // Work time summary header
  rows.push({
    cells: ['=== WORK TIME SUMMARY ===', '', ''],
    style: CellStyles.combine(
      CellStyles.bold(),
      {
        font: {
          size: 12,
          color: {
            rgb: 'FFFFFF',
          },
        },
      },
    ),
  })

  // Summary data
  rows.push([
    'Total work time in the month:',
    `${workingTrackedTiming} hours`,
    '',
  ])

  rows.push([
    'Total available time:',
    `${totalTrackedTiming} hours`,
    '',
  ])

  rows.push([
    'Utilization rate:',
    `${utilizationRate}%`,
    '',
  ])

  rows.push(['', '', '']) // Empty row

  // Project breakdown header
  rows.push({
    cells: ['=== PROJECT BREAKDOWN ===', '', ''],
    style: CellStyles.combine(
      CellStyles.bold(),
      {
        font: {
          size: 12,
          color: {
            rgb: 'FFFFFF',
          },
        },
      },
    ),
  })

  rows.push(['', '', '']) // Empty row

  // Process each type
  timesheetData.forEach((typeGroup) => {
    const totalHours = typeGroup.totalTiming
    const percentage = workingTrackedTiming > 0 ? (totalHours / workingTrackedTiming * 100).toFixed(2) : '0.00'

    // Type header (SGA, ON PROJECT, etc.)
    const typeLabel = typeGroup.typeLabel || typeGroup.type

    rows.push({
      cells: [
        typeLabel,
        `${totalHours} hours`,
        `(${percentage}%)`,
      ],
      style: CellStyles.combine(
        CellStyles.bold(),
        CellStyles.background('4F81BD'),
        CellStyles.textColor('FFFFFF'),
      ),
    })

    // Children items
    if (typeGroup.children) {
      typeGroup.children.forEach((child: any) => {
        const childHours = child.totalTiming
        const childPercentage = totalHours > 0 ? (childHours / totalHours * 100).toFixed(2) : '0.00'

        rows.push([
          `  ${child.name}`, // เว้นวรรคเพื่อแสดงการเป็น sub-item
          `${childHours} hours`,
          `(${childPercentage}%)`,
        ])
      })
    }

    rows.push(['', '', '']) // Empty row after each type
  })

  return {
    headers: ['Category', 'Hours', 'Percentage'],
    rows,
  }
}

// ตัวอย่างการใช้งาน
const exportToExcel = () => {
  const excelData = convertTimesheetToExcel(
    groupByProject.value,
    workingTrackedTiming.value,
    totalTrackedTiming.value,
    currentDate.value,
  )

  // Custom style สำหรับ timesheet report
  const timesheetStyle: ExcelStyleOptions = {
    headerStyle: {
      font: {
        bold: true,
        color: {
          rgb: 'FFFFFF',
        },
        size: 12,
      },
      fill: {
        fgColor: {
          rgb: '2E86AB',
        },
        patternType: 'solid',
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
      },
    },
    dataStyle: {
      font: {
        size: 11,
      },
      alignment: {
        horizontal: 'left',
        vertical: 'center',
      },
    },
    autoFitColumns: true,
  }

  const exporter = new ExcelExporter(timesheetStyle)

  const month = currentDate.value.getMonth() + 1
  const year = currentDate.value.getFullYear()
  const user_name = user.fetch.items?.find((item: any) => item.id === form.values.user_id)?.full_name
  const filename = `${user_name}_timesheet_report_${month}_${year}.xlsx`

  exporter.export(excelData, filename, 'Month Single User Report')
}

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
}, {
  deep: true,
})
</script>
