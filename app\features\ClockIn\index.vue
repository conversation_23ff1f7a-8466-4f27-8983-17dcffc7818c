<template>
  <div class="mt-8">
    <div class="flex items-start justify-between">
      <Button
        class="flex items-center gap-1 px-0 pb-8"
        :to="routes.home.to"
        variant="link"
        color="neutral"
      >
        <Icon
          class="size-7"
          name="ic:baseline-chevron-left"
        />
        <div class="flex items-center gap-2">
          <img
            src="/admin/clock-in-logo.png"
            alt="Clock-In"
            class="h-[50px] w-[50px]"
          />
          <div class="text-3xl font-semibold text-[#353D59]">
            Clock-In
          </div>
        </div>
      </Button>
      <Button
        color="neutral"
        icon="lucide:users"
        size="xl"
        class="rounded-md"
        label="All Checked-in today"
        variant="outline"
        :to="routes.clockin.today.to"
      />
    </div>
    <Card>
      <ProfileWithTime />
    </Card>
    <Card
      :ui="{
        root: 'overflow-visible',
      }"
      class="mt-4"
    >
      <div class="flex items-center gap-1 text-2xl font-bold">
        <Icon
          name="tabler:clock-check"
          class="size-6"
        />
        Clock-In
      </div>
      <Loader :loading="isCheckinToday.fetch.status.isLoading">
        <ClockinForm
          v-if="checkinToday.length === 0"
          @success="fetchCheckin"
        />
        <ClockinComplete
          v-else
          :checkins="checkinToday"
          @edit="onEditModal(checkinToday)"
        />
      </Loader>
    </Card>

    <div class="flex items-center gap-1 pt-8 text-2xl font-bold">
      <Icon
        name="fluent:history-32-filled"
        class="size-6"
      />
      Clock-In History
    </div>
    <div class="flex items-center gap-2">
      <div>🤗</div>
      <div class="mt-2 font-semibold text-[#344054]">
        Check-in other day by clicking the date on the calendar
      </div>
    </div>
    <div class="mt-5 grid grid-cols-1 gap-6 lg:grid-cols-3">
      <div class="space-y-3 lg:col-span-1">
        <Card>
          <VueDatePicker
            v-model="currentDate"
            :enable-time-picker="false"
            inline
            auto-apply
            class="mx-auto"
            :markers="markers"
            :month-change-on-scroll="false"
            @update:model-value="handleDateChange"
            @update-month-year="onUpdateMonthYear"
          />
        </Card>
        <div class="grid grid-cols-2 gap-2">
          <Card
            v-for="checkin in statusCheckin"
            :key="checkin.label"
          >
            <div
              class="flex items-center gap-3"
            >
              <img
                :src="iconImages?.[checkin.type]"
                alt="icon"
                class="size-7"
              />
              <div class="text-xs">
                <div class="text-lg font-bold">
                  {{ checkin.value }}
                </div>
                {{ checkin.label }}
              </div>
            </div>
          </Card>
        </div>
      </div>
      <Card class="lg:col-span-2">
        <div class="text-lg font-bold">
          {{ format(new Date(currentDate), "MMMM yyyy") }}
        </div>
        <Loader :loading="checkin.fetch.status.isLoading">
          <template v-if="Object.keys(groupCheckin).length > 0">
            <div
              v-for="(checkins, date) in groupCheckin"
              :key="date"
              :data-date="date"
              class="border-b border-[#D0D5DD] py-4"
            >
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium">
                    {{ format(new Date(date), "EEEE, MMMM d") }}
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <div
                      v-for="(checkin, idx) in checkins"
                      :key="checkin.id"
                      class="flex items-center gap-2"
                    >
                      <img
                        :src="useGetIconCheckin(checkin)"
                        :alt="useGetIconCheckin(checkin)"
                        class="size-5"
                      />
                      <div class="text-lg font-bold">
                        {{ CHECKIN_LABEL[checkin.type === CHECKIN.LEAVE
                          ? checkin.leave_type as CheckinType || checkin.type as CheckinType
                          :checkin.type as CheckinType]
                          || checkin.type }} {{ checkin.type === CHECKIN.ONSITE ? '- ' + checkin.location :'' }}
                        {{ checkins.length> 1 ? `(${PERIOD_LABEL[checkin.period as PERIOD]})` : '' }}
                      </div>
                      <div
                        v-if="idx + 1 < checkins.length"
                        class="text-xl leading-5 font-bold"
                      >
                        /
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="checkins.length && checkins[0]?.date&& checkins[0]?.first_checkin_at"
                    class="mt-1 text-xs font-medium text-[#667085]"
                  >
                    Clocked-in at {{ TimeHelper.getDateTimeFormTime(new Date(checkins[0]?.first_checkin_at)) }}
                    <Badge
                      v-if=" isLate(checkins[0], checkins[0].user?.team?.working_start_at)"
                      variant="soft"
                      color="warning"
                      size="md"
                      class="ml-1 rounded-full border font-bold"
                    >
                      Late
                    </Badge>
                    <Badge
                      v-else-if=" new Date(checkins[0]?.date) > new Date(checkins[0]?.first_checkin_at)"
                      variant="soft"
                      size="md"
                      class="ml-1 rounded-full border font-bold"
                    >
                      Early
                    </Badge>
                  </div>
                </div>
                <Button
                  label="Edit"
                  icon="mage:pen"
                  variant="outline"
                  color="neutral"
                  class="h-9"
                  @click="onEditModal(checkins)"
                />
              </div>
            </div>
          </template>
          <Empty
            v-else
            message="There is no data this month."
          />
        </Loader>
      </Card>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ClockinForm from './ClockinForm.vue'
import VueDatePicker from '@vuepic/vue-datepicker'
import ClockinComplete from './ClockinComplete.vue'
import ProfileWithTime from '~/container/ProfileWithTime.vue'
import FormModal from './FormModal.vue'
import { useCheckInsPageLoader } from '~/loaders/admin/checkin'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import { startOfMonth, endOfMonth, format, parseISO, isValid, isSameDay } from 'date-fns'

const currentDate = ref(new Date())
const noti = useNotification()
const overlay = useOverlay()
const openModal = overlay.create(FormModal)
const checkin = useCheckInsPageLoader()
const isCheckinToday = useCheckInsPageLoader()
const holiday = useHolidaysPageLoader()
const isEdit = ref(false)
const {
  useGetIconCheckin,
} = useCheckinUtils()

const {
  convertBackToCheckinForm,
  sendSlackMsg,
} = useCheckinForm()

holiday.fetchSetLoading()
checkin.fetchSetLoading()
onMounted(() => {
  holiday.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })

  isCheckinToday.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(new Date()), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(new Date()), 'yyyy-MM-dd'),
    },
  })

  fetchCheckin()
})

const fetchCheckin = () => {
  checkin.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
    },
  })
}

const markers = computed(() => {
  const result: any[] = []

  if (groupCheckin.value) {
    Object.entries(groupCheckin.value).forEach(([date, checkins]: [string, any[]]) => {
      result.push({
        date: new Date(date),
        type: 'line',
        color: !isLate(checkins[0], checkins[0].user?.team?.working_start_at) ? '#079455' : '#eab308',
      })
    })
  }

  if (holiday.fetch.items) {
    result.push(
      ...holiday.fetch.items.map((holiday: any) => ({
        date: new Date(holiday.date),
        type: 'line',
        color: '#A5A5A5',
        tooltip: [{
          text: `หยุด ${holiday.name}`,
          color: '#A5A5A5',
        }],
      })),
    )
  }

  return result
})

const statusCheckin = computed(() => {
  const counts = checkin.fetch.items.filter((item) => !item.is_unused).reduce((acc, item) => {
    const type = item.type
    if (!acc[type]) acc[type] = 0
    acc[type]++

    return acc
  }, {} as Record<string, number>)

  return [
    {
      value: counts[CHECKIN.OFFICE_HQ] || 0,
      label: CHECKIN_LABEL[CHECKIN.OFFICE_HQ],
      icon: iconImages?.[CHECKIN.OFFICE_HQ],
      type: CHECKIN.OFFICE_HQ,
    },
    {
      value: counts[CHECKIN.OFFICE_AKV] || 0,
      label: CHECKIN_LABEL[CHECKIN.OFFICE_AKV],
      icon: iconImages?.[CHECKIN.OFFICE_AKV],
      type: CHECKIN.OFFICE_AKV,
    },
    {
      value: counts[CHECKIN.WFH] || 0,
      label: CHECKIN_LABEL[CHECKIN.WFH],
      icon: iconImages?.[CHECKIN.WFH],
      type: CHECKIN.WFH,
    },
    {
      value: counts[CHECKIN.ONSITE] || 0,
      label: CHECKIN_LABEL[CHECKIN.ONSITE],
      icon: iconImages?.[CHECKIN.ONSITE],
      type: CHECKIN.ONSITE,
    },
    {
      value: counts[CHECKIN.BUSINESS_TRIP] || 0,
      label: CHECKIN_LABEL[CHECKIN.BUSINESS_TRIP],
      icon: iconImages?.[CHECKIN.BUSINESS_TRIP],
      type: CHECKIN.BUSINESS_TRIP,
    },
    {
      value: counts[CHECKIN.LEAVE] || 0,
      label: CHECKIN_LABEL[CHECKIN.LEAVE],
      icon: iconImages?.[LEAVE_TYPE.ANNUAL],
      type: LEAVE_TYPE.ANNUAL,
    },
  ]
})

const checkinToday = computed(() => {
  return isCheckinToday.fetch.items
    .filter((item) => {
      if (item.is_unused || !item?.date) return false

      const d = parseISO(item.date)
      if (!isValid(d)) return false

      return format(d, 'yyyy-MM-dd') === TimeHelper.getCurrentDate()
    })
    .sort((a, b) => PERIOD_ORDER[a.period] - PERIOD_ORDER[b.period])
})

const groupCheckin = computed(() => {
  const grouped = checkin.fetch.items
    .filter((item) => !item.is_unused)
    .reduce((acc, item) => {
      const date = TimeHelper.displayDate(item.date)

      if (!acc[date]) acc[date] = []

      acc[date].push(item)

      return acc
    }, {} as Record<string, ICheckinItem[]>)

  return (Object.entries(grouped) as [string, ICheckinItem[]][])
    .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
    .reduce((acc, [date, items]) => {
      acc[date] = items.sort((x, y) => PERIOD_ORDER[x.period] - PERIOD_ORDER[y.period])

      return acc
    }, {} as Record<string, ICheckinItem[]>)
})

const onUpdateMonthYear = ({
  month, year,
}: { month: number
  year: number }) => {
  currentDate.value = new Date(year, month, 1)
  fetchCheckin()
}

const handleDateChange = async (newDate: string) => {
  const selectedDate = TimeHelper.displayDate(newDate)

  const element = document.querySelector(`[data-date="${selectedDate}"]`)

  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  } else {
    onAddModal(newDate)
  }
}

const onEditModal = (data: any) => {
  isEdit.value = true
  const values = {
    ...data,
    type: Array.from(new Set(data.map((item: ICheckinItem) => item.type))),
  }

  const convertData = convertBackToCheckinForm(values)

  openModal.open({
    values: {
      ...convertData,
      date: data.length > 0 ? new Date(data[0].date).toISOString() : new Date().toISOString(),
    },
    onEdit: true,
    status: () => checkin.add.status,
    onSubmit: (values: ITimesheet) => {
      checkin.addRun({
        data: values,
      })
    },
  })
}

const onAddModal = (date: string) => {
  const checkinDate = new Date(date)

  isEdit.value = false
  openModal.open({
    values: {
      date: new Date(date).toISOString(),
      type: checkinDate > new Date() ? [CHECKIN.LEAVE] : [],
    },
    status: () => checkin.add.status,
    onSubmit: (values: ITimesheet) => {
      checkin.addRun({
        data: values,
      })
    },
  })
}

const isLate = (checkin: any, teamStartAt?: string) => {
  const date = new Date(checkin.date)
  const firstCheckinAt = new Date(checkin.first_checkin_at)

  if (!isSameDay(date, firstCheckinAt)) {
    if (date > firstCheckinAt) {
      return false
    }

    return true
  }

  if (teamStartAt) {
    const [h, m, s] = teamStartAt.split(':').map(Number)
    const teamStart = new Date(date)

    teamStart.setHours(h, m || 0, s || 0, 0)

    const teamStartPlus1h = new Date(teamStart.getTime() + 60 * 60 * 1000)

    if (firstCheckinAt >= teamStartPlus1h) {
      return true
    }
  }

  return false
}

useWatchTrue(
  () => checkin.add.status.isSuccess,
  () => {
    noti.success({
      title: 'Clock-In successfully',
      description: 'You have clocked in successfully.',
    })

    openModal.close()
    fetchCheckin()

    sendSlackMsg(checkin.add.item as any, isEdit.value)
  },
)

useWatchTrue(
  () => checkin.add.status.isError,
  () => {
    openModal.close()
    noti.error({
      title: 'Clock-In failed',
      description: StringHelper.getError(checkin.add.status.errorData, 'An error occurred while saving changes, please try again'),
    })
  },
)
</script>
