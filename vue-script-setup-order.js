/* eslint-disable */

export default {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'enforce order of items in <script setup>',
    },
    fixable: 'code',
    schema: [],
    messages: {
      wrongOrder:
        'Move \'{{name}}\' before \'{{prev}}\' to follow order: variables > refs > computed > functions > watch > lifecycle.',
    },
  },
  create(context) {
    const orderMap = {
      variable: 1,
      ref: 2,
      computed: 3,
      function: 4,
      watch: 5,
      lifecycle: 6,
    }

    function getCategory(node) {
      if (node.type === 'VariableDeclaration') {
        if (node.declarations.some((d) => d.init?.callee?.name === 'ref')) return 'ref'
        if (node.declarations.some((d) => d.init?.callee?.name === 'computed')) return 'computed'

        return 'variable'
      }

      if (node.type === 'FunctionDeclaration') return 'function'

      if (node.type === 'ExpressionStatement') {
        const callee = node.expression?.callee?.name
        if (callee === 'watch' || callee === 'watchEffect') return 'watch'

        if (
          callee
          && callee.startsWith('on')
          && callee[2] === callee[2].toUpperCase()
        ) {
          return 'lifecycle'
        }
      }

      return null
    }

    return {
      Program(node) {
        if (!context.getFilename().endsWith('.vue')) return

        const body = node.body
        const categorized = body
          .map((stmt) => ({
            stmt,
            cat: getCategory(stmt),
          }))
          .filter((x) => x.cat)

        // Build sorted order
        const sorted = [...categorized].sort(
          (a, b) => orderMap[a.cat] - orderMap[b.cat],
        )

        categorized.forEach((item, idx) => {
          if (item.cat !== sorted[idx].cat) {
            context.report({
              node: item.stmt,
              messageId: 'wrongOrder',
              data: {
                name: item.cat,
                prev: sorted[idx].cat,
              },
              fix(fixer) {
                // Reorder by replacing whole block
                const sourceCode = context.sourceCode
                const textBlocks = categorized.map((c) =>
                  sourceCode.getText(c.stmt),
                )

                const sortedText = sorted.map((c) =>
                  sourceCode.getText(c.stmt),
                )

                return fixer.replaceTextRange(
                  [
                    categorized[0].stmt.range[0],
                    categorized[categorized.length - 1].stmt.range[1],
                  ],
                  sortedText.join('\n\n'),
                )
              },
            })
          }
        })
      },
    }
  },
}
