export const enum userStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export const USER_STATUS_LABEL = {
  [userStatus.ACTIVE]: 'Active',
  [userStatus.INACTIVE]: 'Inactive',
}

export const USER_STATUS_OPTIONS = Object.entries(USER_STATUS_LABEL).map(([value, label]) => ({
  value,
  label,
}))

export enum TYPE_INFO_ITEM {
  TEXT = 'text',
  NUMBER = 'number',
  CURRENCY = 'currency',
  DATE = 'date',
  BOOLEAN = 'boolean',
}
