<template>
  <form
    class="space-y-6"
    @submit="onSubmit"
  >
    <Card>
      <div class="mb-4">
        <h4
          class="text-xl font-bold wrap-break-word lg:max-w-2/3"
          title="ข้อมูลโครงการ"
        >
          ข้อมูลโครงการ
          <span id="page-title-extra" />
        </h4>

        <div id="page-subtitle">
          <p class="text-md text-[#475467]">
            Project Info
          </p>
        </div>
      </div>
      <FormFields :options="formFields" />
      <div class="my-5 font-semibold">
        เชื่อมต่อโครงการสำหรับ Timesheet
      </div>
      <div class="grid md:gap-5 lg:grid-cols-2 lg:gap-10">
        <div
          class="rounded-md border p-5"
          :class="form.values.type === 'OLD' ? 'border-primary' : 'border-[#D0D5DD]'"
        >
          <FormFields :options="formOldProjectFields" />
        </div>

        <div
          class="rounded-md border p-5"
          :class="form.values.type === 'NEW' ? 'border-primary' : 'border-[#D0D5DD]'"
        >
          <FormFields :options="formNewProjectFields" />
        </div>
      </div>
    </Card>
    <Card
      :ui="{
        root: '!bg-[#EAECF0]',
      }"
    >
      <div class="mb-4">
        <h4
          class="text-xl font-bold wrap-break-word lg:max-w-2/3"
          title="ข้อมูลโครงการ"
        >
          URL โครงการ
          <span id="page-title-extra" />
        </h4>
      </div>
      <FormField
        label="URL โครงการ (Project Slug) *"
        :error="form.submitCount.value > 0 || checkSlug.data.value ? form.errors.value.slug : ''"
      >
        <div class="relative flex items-center">
          <span class="bg-primary flex h-11 items-center rounded-l-md px-3 py-2 text-[16px] leading-4 text-white">
            https://work.finema.co/pmo/
          </span>
          <Input
            v-model="form.values.slug"
            :mask="MaskaHelper.englishLetter()"
            type="text"
            placeholder="URL โครงการ (Project Slug)"
            :ui="{
              base: ' !rounded-l-none',
            }"
            @update:modelValue="(value) => form.setFieldValue('slug', value) "
          />
          <Badge
            v-if="available"
            variant="soft"
            label="Available"
            color="success"
            icon="material-symbols:check-circle-outline-rounded"
            class="absolute right-4 rounded-full border-1"
          />
          <Badge
            v-else-if="available === false"
            variant="soft"
            label="Not Available"
            color="error"
            class="absolute right-4 rounded-full border-1"
            icon="material-symbols:cancel-outline-rounded"
          />
        </div>
      </FormField>
    </Card>

    <div class="flex justify-between">
      <Button
        type="button"
        variant="outline"
        icon="mingcute:left-line"
      >
        กลับ
      </Button>
      <Button
        type="submit"
      >
        สร้าง
      </Button>
    </div>

    <div class="mt-4 flex justify-end gap-3" />
  </form>
</template>

<script lang="ts" setup>
import { INPUT_TYPES } from '#core/components/Form/types'
import { useProjectsPageLoader } from '~/loaders/admin/project'
import { usePmoProjectsPageLoader, usePmoProjectCheckSlugPageLoader } from '~/loaders/pmo/project'
import { watchDebounced } from '@vueuse/core'

const projectList = useProjectsPageLoader()
const project = usePmoProjectsPageLoader()
const checkSlug = usePmoProjectCheckSlugPageLoader()
const dialog = useDialog()
const noti = useNotification()
const router = useRouter()
const init = ref(false)
const available = ref<boolean | null>(null)

const oldUsageSchema = {
  project_id: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
}

const form = useForm({
  validationSchema: computed(() => {
    const rules: any = {
      name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      email: v.nullish(v.pipe(v.string(), v.email())),
      tags: v.array(v.string()),
      slug: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    }

    if (init.value) {
      if (form.values.type === 'OLD') {
        Object.assign(rules, oldUsageSchema)
      }
    }

    return toTypedSchema(v.object(rules))
  }),
  initialValues: {
    type: 'OLD',
  },
})

onMounted(() => {
  projectList.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })

  init.value = true
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อโครงการ',
      name: 'name',
      placeholder: 'กรุณากรอกชื่อชื่อโครงการ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.EMAIL,
    props: {
      label: 'อีเมล',
      name: 'email',
      placeholder: 'กรุณากรอกอีเมล',
    },
  },
  {
    type: INPUT_TYPES.TAGS,
    props: {
      label: 'แท็ก (Tags)',
      name: 'tags',
      placeholder: 'เพิ่มแท็ก',
      description: 'กด Enter เพื่อเพิ่มแท็ก',
    },
  },
])

const formOldProjectFields = createFormFields(() => [

  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'type',
      options: [
        {
          value: 'OLD',
          label: 'เลือกจากโครงการที่มีอยู่แล้ว',
        },
      ],
    },

  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ชื่อโครงการ',
      name: 'project_id',
      options: projectList.fetch.items
        .map((item) => {
          const isDisabled = item.pmo

          return {
            label: isDisabled
              ? `${item.name} (ถูกใช้งานแล้ว)`
              : item.name,
            value: item.id,
            disabled: isDisabled,
          }
        })
        .sort((a, b) => {
          if (a.disabled === b.disabled) return 0

          return a.disabled ? 1 : -1
        }),

      required: true,
    },
  },
])

const formNewProjectFields = createFormFields(() => [

  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'type',
      options: [
        {
          value: 'NEW',
          label: 'สร้างโครงการใหม่สำหรับ Timesheet',
        },
      ],
    },
    on: {
      change: () => {
        form.setFieldValue('project_id', '')
      },
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  if (available.value === false) {
    return
  }

  dialog.confirm({
    title: 'ยืนยันการสร้าง',
    description: `คุณต้องการสร้างโปรเจค "${values.name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    project.addRun({
      data: {
        ...values,
        status: PROJECT_STATUS.DRAFT,
      },
    })
  })
})

watchDebounced(
  () => form.values.slug,
  (value) => {
    if (value === '') {
      available.value = false

      return
    }

    checkSlug.run({
      data: {
        slug: value,
      },
    })
  },
  {
    debounce: 600,
  },
)

useWatchTrue(
  () => checkSlug.status.value.isSuccess,
  () => {
    if (!checkSlug.data.value?.is_available) {
      available.value = false
    } else {
      available.value = true
    }
  },
)

useWatchTrue(
  () => checkSlug.status.value.isError,
  () => {
    available.value = false
  },
)

useWatchTrue(
  () => project.add.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'เพิ่มโปรเจคสำเร็จ',
      description: 'คุณได้เพิ่มโปรเจคเรียบร้อยแล้ว',
    })

    router.push(routes.pmo.project.projectBySlug(project.add.item?.slug || '').to)
  },
)

useWatchTrue(
  () => project.add.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'เพิ่มโปรเจคไม่สำเร็จ',
      description: StringHelper.getError(project.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มโปรเจค กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
