export const useTimesheetItems = (timesheets: () => ITimesheet[]) => {
  const groupByProjectItems = computed(() => {
    const grouped: Record<string, any> = {}

    timesheets()?.forEach((item) => {
      const name = item.user?.display_name || 'Unknown'

      if (!grouped[name]) {
        grouped[name] = {
          ...item,
          timing: 0,
        } // clone item ไว้
      }

      grouped[name].timing += item.timing
    })

    const projectTrackers = Object.values(grouped)
    const total = projectTrackers.reduce((sum: number, val: any) => sum + val.timing, 0)

    return projectTrackers.map((item: any) => ({
      ...item,
      timing: item.timing.toFixed(2),
      percent: total > 0 ? ((item.timing / total) * 100).toFixed(2) : '0.00',
    }))
  })

  const groupByTimesheetDateItems = computed(() => {
    const result: Record<
      string,
      {
        name: string
        team: string
        avatar_url?: string
        detail: Array<{ date: string
          totalTime: number }>
      }
    > = {}

    for (const item of timesheets()) {
      const name
        = item.user.display_name || item.user.email.replace('@finema.co', '').toUpperCase()

      const avatar_url = item.user.avatar_url
      const team = item.user.team as ITeam
      const date = item.date
      const time = item.timing || 0

      if (!result[name]) {
        result[name] = {
          name,
          avatar_url,
          team,
          detail: [],
        }
      }

      const dayEntry = result[name].detail.find((d) => d.date === date)

      if (dayEntry) {
        dayEntry.totalTime += time
      } else {
        result[name].detail.push({
          date,
          totalTime: time,
        })
      }
    }

    return Object.values(result)
  })

  return {
    groupByProjectItems,
    groupByTimesheetDateItems,
  }
}
