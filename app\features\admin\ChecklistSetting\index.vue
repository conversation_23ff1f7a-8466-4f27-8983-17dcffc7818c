<template>
  <div class="space-y-4">
    <div class="overflow-x-auto">
      <Tabs
        v-model="activeTabIndex"
        color="neutral"
        variant="link"
        :items="items"
        class="w-full min-w-max"
      />
    </div>
    <div>
      <TeleportSafe to="#page-header">
        <Button
          trailing-icon="basil:plus-solid"
          @click="addChecklist"
        >
          เพิ่มรายการ
        </Button>
      </TeleportSafe>

      <FlexDeck
        :options="tableOptions"
        container-class="space-y-4"
        @pageChange="checklist.fetchPageChange"
        @search="checklist.fetchSearch"
      >
        <template #default="{ row }: { row: ITemplateChecklistItem }">
          <Card>
            <div class="flex items-center justify-between">
              <div class="text-sm font-medium">
                {{ row.detail }}
              </div>
              <div class="flex justify-end gap-3">
                <ButtonActionIcon
                  variant="outline"
                  icon="mage:pen"
                  color="neutral"
                  @click="editchecklist(row)"
                />
                <ButtonActionIcon
                  variant="outline"
                  icon="prime:trash"
                  color="error"
                  @click="onDelete(row)"
                />
              </div>
            </div>
          </Card>
        </template>
      </FlexDeck>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TabsItem } from '@nuxt/ui'
import Form from './Form.vue'
import { useTemplateChecklistItemPageLoader } from '~/loaders/pmo/template'
import { PROJECT_TAB_LABEL, PROJECT_TAB_TYPE } from '~/constants/projectTab'

const checklist = useTemplateChecklistItemPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const activeTabIndex = ref<PROJECT_TAB_TYPE>(PROJECT_TAB_TYPE.SALES)

const items = ref<TabsItem[]>([
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.SALES],
    value: PROJECT_TAB_TYPE.SALES,
  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PRESALES],
    value: PROJECT_TAB_TYPE.PRESALES,

  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIDDING],
    value: PROJECT_TAB_TYPE.BIDDING,
  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PMO],
    value: PROJECT_TAB_TYPE.PMO,
  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIZCO],
    value: PROJECT_TAB_TYPE.BIZCO,
  },
])

const tableOptions = useFlexDeck<ITemplateChecklistItem>({
  repo: checklist,
})

checklist.fetchSetLoading()
onMounted(() => {
  checklist.fetchPage(1, '', {
    params: {
      tab_key: activeTabIndex.value,
    },
  })
})

const editchecklist = (_checklist: ITemplateChecklistItem) => {
  editModal.open({
    isEditing: true,
    values: _checklist,
    status: () => checklist.update.status,
    onSubmit: (values: ITemplateChecklistItem) => {
      checklist.updateRun(_checklist.id, {
        data: values,
      })
    },
  })
}

const addChecklist = () => {
  addModal.open({
    status: () => checklist.add.status,
    onSubmit: (values: ITemplateChecklistItem) => {
      checklist.addRun({
        data: {
          ...values,
          tab_key: activeTabIndex.value,
        },
      })
    },
  })
}

const onDelete = (values: ITemplateChecklistItem) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบรายการ "${values.detail}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    checklist.deleteRun(values.id)
  })
}

useWatchTrue(
  () => checklist.update.status.isSuccess,
  () => {
    editModal.close()
    checklist.fetchPage(1, '', {
      params: {
        tab_key: activeTabIndex.value,
      },
    })

    noti.success({
      title: 'แก้ไขรายการสำเร็จ',
      description: 'คุณได้แก้ไขรายการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => checklist.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขรายการไม่สำเร็จ',
      description: StringHelper.getError(checklist.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขรายการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => checklist.delete.status.isSuccess,
  () => {
    dialog.close()
    checklist.fetchPage(1, '', {
      params: {
        tab_key: activeTabIndex.value,
      },
    })

    noti.success({
      title: 'ลบรายการสำเร็จ',
      description: 'คุณได้ลบรายการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => checklist.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบรายการไม่สำเร็จ',
      description: StringHelper.getError(checklist.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบรายการ กรุณาลองใหม่อีกครั้ง'),

    })
  },
)

useWatchTrue(
  () => checklist.add.status.isSuccess,
  () => {
    addModal.close()
    checklist.fetchPage(1, '', {
      params: {
        tab_key: activeTabIndex.value,
      },
    })

    noti.success({
      title: 'เพิ่มรายการสำเร็จ',
      description: 'คุณได้เพิ่มรายการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => checklist.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มรายการไม่สำเร็จ',
      description: StringHelper.getError(checklist.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มรายการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

watch(() => activeTabIndex.value, () => {
  checklist.fetchPage(1, '', {
    params: {
      tab_key: activeTabIndex.value,
    },
  })
})
</script>
