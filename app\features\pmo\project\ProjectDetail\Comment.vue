<template>
  <StatusBox
    :status="comments.fetch.status"
    :data="comments.fetch.items"
  >
    <div class="space-y-2">
      <template v-if="comments.fetch.items?.length">
        <div
          v-if="comments.fetch.options.currentPage < comments.fetch.options.totalPage"
          class="flex flex-col items-center justify-center"
        >
          <Button
            variant="outline"
            color="neutral"
            :label="`เรียกดู ${comments.fetch.options.totalCount - comments.fetch.items.length} ข้อความก่อนหน้านี้`"
            :loading="comments.fetch.statusNextPage.isLoading"
            icon="mi:arrow-up"
            @click="comments.fetchNextPage()"
          />
        </div>
        <div
          v-for="c in reverseCommentItems"
          :key="c.id"
          class="rounded-xl bg-[#F9FAFB] p-4 dark:bg-slate-800/40"
        >
          <div class="flex gap-3">
            <div class="flex-1">
              <div class="flex items-start justify-between">
                <div class="flex items-end gap-1">
                  <AvatarProfile
                    :updated-at="c.updated_at || c.created_at"
                    :item="c.user"
                    hide-team
                  />
                  <div
                    v-if="c.updated_by_id"
                    class="text-xs text-slate-500"
                  >
                    (มีการแก้ไข)
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <Badge
                    v-if="c.is_client_flag"
                    variant="outline"
                    class="rounded-full text-[#363F72] ring-[#D5D9EB]"
                    label="คำตอบจากลูกค้า"
                  />
                  <DropdownMenu
                    :content="{
                      align: 'start',
                      side: 'bottom',
                      sideOffset: 8,
                    }"
                    :items="auth.me.value?.id === c.user_id
                      ? [
                        [{
                          label: 'แก้ไข',
                          icon: 'i-heroicons-pencil-square',
                          onSelect: () => onOpenEdit(c),
                        }],
                        ...(c.updated_by_id
                          ? [
                            [{
                              label: 'ประวัติ',
                              icon: 'basil:history-solid',
                              onSelect: () => onOpenHistory(c),
                            }],

                          ]
                          : []),
                        [{
                          label: 'ลบ',
                          icon: 'i-heroicons-trash',
                          onSelect: () => onDelete(c),
                        }],
                      ]
                      : (c.updated_by_id
                        ? [
                          [{
                            label: 'ประวัติ',
                            icon: 'basil:history-solid',
                            onSelect: () => onOpenHistory(c),
                          }],

                        ]
                        : []
                      )"
                  >
                    <Button
                      icon="i-heroicons-ellipsis-vertical"
                      size="md"
                      class="p-0"
                      variant="ghost"
                      color="neutral"
                    />
                  </DropdownMenu>
                </div>
              </div>

              <!-- แก้ไข comment -->
              <div class="mt-2">
                <template v-if="editingId === c.id">
                  <Textarea
                    v-model="draftById[c.id]"
                    autoresize
                    placeholder="แก้ไขข้อความ..."
                  />
                  <div class="mt-2 flex items-start justify-between">
                    <Checkbox
                      v-model="draftByIdFlag[c.id]"
                      label="คำตอบจากลูกค้า"
                    />
                    <div class="flex justify-end gap-2">
                      <Button
                        color="neutral"
                        variant="soft"
                        @click="onCancelEdit()"
                      >
                        ยกเลิก
                      </Button>
                      <Button
                        color="primary"
                        icon="material-symbols:check"
                        @click="onEdit(c)"
                      >
                        บันทึก
                      </Button>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <p class="whitespace-pre-line text-slate-700 dark:text-slate-200">
                    {{ c.detail }}
                  </p>
                </template>
              </div>

              <!-- ปุ่มเปิดส่วนตอบกลับ -->
              <div class="mt-2">
                <Button
                  size="sm"
                  variant="outline"
                  color="neutral"
                  icon="material-symbols-light:chat-outline-rounded"
                  @click="toggleReply(c.id)"
                >
                  <div class="font-bold">
                    {{ c.replies?.length }} ตอบกลับ
                  </div>
                </Button>
              </div>

              <template
                v-if=" replyOpen[c.id]"
              >
                <!-- Replies -->
                <div
                  v-if="c.replies?.length"
                  class="mt-3 space-y-2"
                >
                  <div
                    v-for="r in c.replies"
                    :key="r.id"
                    class="ml-8 flex gap-3 rounded-lg bg-white p-3"
                  >
                    <div class="flex-1">
                      <div class="flex items-start justify-between">
                        <div class="flex items-end gap-1">
                          <AvatarProfile
                            :updated-at="r.updated_at || r.created_at"
                            :item="r.user"
                            hide-team
                          />
                          <div
                            v-if="r.updated_by_id"
                            class="text-xs text-slate-500"
                          >
                            (มีการแก้ไข)
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <Badge
                            v-if="r.is_client_flag"
                            variant="outline"
                            class="rounded-full text-[#363F72] ring-[#D5D9EB]"
                            label="คำตอบจากลูกค้า"
                          />
                          <DropdownMenu
                            :content="{
                              align: 'start',
                              side: 'bottom',
                              sideOffset: 8,
                            }"
                            :items="auth.me.value?.id === r.user.id
                              ? [
                                [{
                                  label: 'แก้ไข',
                                  icon: 'i-heroicons-pencil-square',
                                  onSelect: () => onOpenEditReply(r),
                                }],
                                ...(c.updated_by_id
                                  ? [
                                    [{
                                      label: 'ประวัติ',
                                      icon: 'basil:history-solid',
                                      onSelect: () => onOpenHistory(r),
                                    }],

                                  ]
                                  : []),
                                [{
                                  label: 'ลบ',
                                  icon: 'i-heroicons-trash',
                                  onSelect: () => onDelete(r),
                                }],
                              ]
                              : (c.updated_by_id
                                ? [
                                  [{
                                    label: 'ประวัติ',
                                    icon: 'basil:history-solid',
                                    onSelect: () => onOpenHistory(r),
                                  }],

                                ]
                                : []
                              )"
                          >
                            <Button
                              icon="i-heroicons-ellipsis-vertical"
                              size="md"
                              class="p-0"
                              color="neutral"
                              variant="ghost"
                            />
                          </DropdownMenu>
                        </div>
                      </div>

                      <!-- แก้ไข reply -->
                      <div class="mt-2">
                        <template v-if="editingReplyId === r.id">
                          <Textarea
                            v-model="draftReplyById[r.id]"
                            autoresize
                            placeholder="แก้ไขข้อความ..."
                            @keydown="(e:any) => ((e.metaKey || e.ctrlKey) && e.key === 'Enter')
                              && onSaveEditReply(r.id)"
                          />
                          <div class="mt-2 flex items-start justify-between">
                            <Checkbox
                              v-model="draftReplyFlag[r.id]"
                              label="คำตอบจากลูกค้า"
                            />
                            <div class="flex justify-end gap-3">
                              <Button
                                color="neutral"
                                variant="soft"
                                @click="cancelEditReply()"
                              >
                                ยกเลิก
                              </Button>
                              <Button
                                color="primary"
                                icon="material-symbols:check"
                                @click="onSaveEditReply(r.id)"
                              >
                                บันทึก
                              </Button>
                            </div>
                          </div>
                        </template>
                        <template v-else>
                          <p class="mt-1 whitespace-pre-line text-slate-700 dark:text-slate-200">
                            {{ r.detail }}
                          </p>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- ส่วนตอบกลับ -->
                <div

                  class="mt-2 ml-8"
                >
                  <label class="mb-1 block text-[13px] text-slate-500">การตอบกลับ</label>
                  <Textarea
                    v-model="replyText[c.id]"
                    placeholder="ระบุรายละเอียด"
                    autoresize
                    :ui="{ base: 'min-h-[96px]' }"
                  />
                  <div class="mt-2 flex items-start justify-between">
                    <Checkbox
                      v-model="replyFlag[c.id]"
                      label="คำตอบจากลูกค้า"
                    />
                    <div class="flex justify-end gap-3">
                      <Button
                        color="neutral"
                        variant="outline"
                        @click="replyOpen[c.id] = false"
                      >
                        ยกเลิก
                      </Button>
                      <Button
                        icon="i-heroicons-paper-airplane"
                        color="primary"
                        :disabled="!(replyText[c.id] ?? '').trim()"
                        @click="sendReply(c)"
                      >
                        ส่งการตอบกลับ
                      </Button>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </template>
      <Empty
        v-else
        message="ยังไม่มี comments"
      />
      <div class="mt-6">
        <label class="mb-1 block text-[13px] text-slate-500">รายละเอียด(Detail)</label>
        <Textarea
          v-model="newComment"
          placeholder="ระบุรายละเอียด"
          autoresize
          :ui="{ base: 'min-h-[96px]' }"
        />
        <div class="mt-2 flex items-start justify-between">
          <Checkbox
            v-model="newCommentFlag"
            label="คำตอบจากลูกค้า"
          />
          <div class="flex justify-end gap-3">
            <Button
              color="primary"
              icon="i-heroicons-paper-airplane"
              :disabled="!newComment.trim()"
              @click="sendMain"
            >
              ส่งอัปเดต
            </Button>
          </div>
        </div>
      </div>
    </div>
  </StatusBox>
</template>

<script setup lang="ts">
import { usePmoCommentByProjectIdPageLoader } from '~/loaders/pmo/project'
import CommentHistoryModal from './CommentHistoryModal.vue'

const props = defineProps<{ projectId: string
  channel: string }>()

const dialog = useDialog()
const comments = usePmoCommentByProjectIdPageLoader(props.projectId || '')
const noti = useNotification()
const auth = useAuth()
// --- states สำหรับ inline edit/ reply ต่อคอมเมนต์
const draftById = reactive<Record<string, string>>({}) // เก็บข้อความแก้ไขแต่ละ id
const draftByIdFlag = reactive<Record<string, boolean>>({})
const replyOpen = reactive<Record<string, boolean>>({}) // เปิด/ปิดกล่องตอบกลับ
const replyText = reactive<Record<string, string>>({}) // เก็บข้อความตอบกลับแต่ละ id
const replyFlag = reactive<Record<string, boolean>>({})

// state สำหรับ “แก้ไขอินไลน์” + “ตอบกลับ”
const editingId = ref<string | null>(null) // กำลังแก้ไขคอมเมนต์ไหน
const editingReplyId = ref<string | null>(null)
const draftReplyById = reactive<Record<string, string>>({})
const draftReplyFlag = reactive<Record<string, boolean>>({})
// ส่งคอมเมนต์หลัก (กล่องล่างสุด)
const newComment = ref('')
const newCommentFlag = ref(false)
const overlay = useOverlay()
const openModalhistory = overlay.create(CommentHistoryModal)

const sendMain = () => {
  dialog.confirm({
    title: 'ยืนยันการส่งข้อความอัปเดต',
    description: `คุณต้องการส่งข้อความอัปเดตหรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    comments.addRun({
      data: {
        detail: newComment.value?.trim(),
        channel: props.channel,
        is_client_flag: newCommentFlag.value,
      },
    })

    newComment.value = ''
  })
}

const reverseCommentItems = computed(() => {
  return [...(comments.fetch.items || [])].reverse()
})

const fetchParams = computed(() => ({
  channel: props.channel,
  limit: 5,
  order_by: 'created_at ASC',
}))

const onCancelEdit = () => {
  editingId.value = null
}

const onEdit = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการแก้ไข',
    description: `คุณต้องการแก้ไขข้อความนี้หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    const text = (draftById[values.id] ?? '').trim()
    if (!text) return
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    comments.updateRun(values.id, {
      data: {
        detail: text,
        channel: props.channel,
        is_client_flag: draftByIdFlag[values.id],
      },
    })
  })
}

const onOpenEdit = (c: any) => {
  editingId.value = c.id
  draftById[c.id] = c.detail
  draftByIdFlag[c.id] = c.is_client_flag
}

const onOpenEditReply = (r: any) => {
  editingReplyId.value = r.id
  draftReplyById[r.id] = r.detail
  draftReplyFlag[r.id] = r.is_client_flag
}

const onDelete = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบช้อความนี้หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    comments.deleteRun(values.id)
  })
}

const toggleReply = (id: string) => {
  return replyOpen[id] = !replyOpen[id]
}

const onOpenHistory = (c: ICommentItem) => {
  openModalhistory.open({
    values: c,
  })
}

const sendReply = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการส่งข้อความตอบกลับ',
    description: `คุณต้องการส่งข้อความตอบกลับหรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    comments.addRun({
      data: {
        detail: replyText[values.id],
        channel: props.channel,
        parent_comment_id: values.id,
        is_client_flag: replyFlag[values.id],
      },
    })
  })
}

const cancelEditReply = () => {
  editingReplyId.value = null
}

const onSaveEditReply = (id: string) => {
  dialog.confirm({
    title: 'ยืนยันการแก้ไข',
    description: `คุณต้องการแก้ไขข้อความนี้หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    const text = (draftReplyById[id] ?? '').trim()
    if (!text) return

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    comments.updateRun(id, {
      data: {
        detail: text,
        channel: props.channel,
        is_client_flag: draftReplyFlag[id],
      },
    })
  })
}

useWatchTrue(
  () => comments.update.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'แก้ไขรายการสำเร็จ',
      description: 'คุณได้แก้ไขรายการเรียบร้อยแล้ว',
    })

    editingReplyId.value = null
    editingId.value = null

    comments.fetchPage(1, '', {
      params: fetchParams.value,
    })
  },
)

useWatchTrue(
  () => comments.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'แก้ไขรายการไม่สำเร็จ',
      description: StringHelper.getError(comments.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขรายการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => comments.delete.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'ลบรายการสำเร็จ',
      description: 'คุณได้ลบรายการเรียบร้อยแล้ว',
    })

    comments.fetchPage(1, '', {
      params: fetchParams.value,
    })
  },
)

useWatchTrue(
  () => comments.delete.status.isError,
  () => {
    dialog.close()
  },
)

useWatchTrue(
  () => comments.add.status.isSuccess,
  () => {
    if (comments.add.item?.parent_comment_id) {
      replyText[comments.add.item?.parent_comment_id] = ''
    }

    dialog.close()
    noti.success({
      title: 'เพิ่มรายการสำเร็จ',
      description: 'คุณได้เพิ่มรายการเรียบร้อยแล้ว',
    })

    comments.fetchPage(1, '', {
      params: fetchParams.value,
    })
  },
)

useWatchTrue(
  () => comments.add.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'เพิ่มรายการไม่สำเร็จ',
      description: StringHelper.getError(comments.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มรายการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

watch(
  () => props.channel,
  (newChannel, oldChannel) => {
    if (newChannel !== oldChannel) {
      comments.fetchPage(1, '', {
        params: {
          channel: newChannel,
          limit: 5,
          order_by: 'created_at ASC',
        },
      })
    }
  },
  {
    immediate: true,
  },
)
</script>
