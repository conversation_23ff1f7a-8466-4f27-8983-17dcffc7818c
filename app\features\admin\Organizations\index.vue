<template>
  <div class="space-y-4">
    <Tabs
      v-model="activeTabIndex"
      color="neutral"
      variant="link"
      :content="false"
      :items="items"
      class="w-full min-w-max"
    />
    <Ministries v-if="activeTabIndex === TabStatus.ministries" />
    <Departments v-if="activeTabIndex === TabStatus.departments" />
  </div>
</template>

<script lang="ts" setup>
import Ministries from './Ministries/index.vue'
import Departments from './Departments/index.vue'
import type { TabsItem } from '@nuxt/ui'

enum TabStatus {
  ministries = 'ministries',
  departments = 'departments',
}
const items = ref<TabsItem[]>([
  {
    label: 'กระทรวง',
    value: TabStatus.ministries,
  },
  {
    label: 'กรม',
    value: TabStatus.departments,
  },

])

const activeTabIndex = ref<TabStatus>(TabStatus.ministries)
</script>
