import type { TRACKER_TYPE } from '~/constants/timesheet'

export interface ITimesheet {
  id: string
  created_at: string
  date: string
  type: string
  leave_type?: string
  sga_name?: string
  timing: number
  description: string
  project_code?: string
  project: IProject
  sga: ISGA
  sga_id: string
  user: IUser
  created_by: string
}

export interface TrackerDetails {
  name?: string
  timing: number
  date?: string
  profileName?: string
  sgaName?: string
  leaveName?: string
}

export interface GroupedPersonalTrackers {
  project: { totalHours: number
    details: TrackerDetails[] }
  leave: { totalHours: number
    details: TrackerDetails[] }
  sga: { totalHours: number
    details: TrackerDetails[] }
  ot: { totalHours: number
    details: TrackerDetails[] }
  internal: { totalHours: number
    details: TrackerDetails[] }
  external: { totalHours: number
    details: TrackerDetails[] }
  standby: { totalHours: number
    details: TrackerDetails[] }
}

export interface Timing {
  project_code: string | null
  project_name: string | null
  sga_name: string | null
  type: TRACKER_TYPE
  total_timing: number
  ot_name: string | null
}

export interface ITimesheetSummaryReport {
  id: string
  created_at: string
  updated_at: string
  email: string
  full_name: string
  display_name: string
  position: string
  team_code: string
  company: string
  avatar_url: string
  slack_id: string
  is_active: boolean
  team: ITeam
  total_timing: number
  total_project_timing: number
  total_leave_timing: number
  total_sga_timing: number
  total_internal_timing: number
  total_external_timing: number
  total_ot_timing: number
  timings: Timing[]
}
