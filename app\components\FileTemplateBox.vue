<template>
  <div
    class="
      flex items-center justify-between rounded-lg border border-neutral-200 p-4
    "
    :class="!!readonly ? 'border-none bg-transparent p-0' : ''"
  >
    <div class="flex items-start gap-2">
      <Icon
        :name="fileIcon"
        :class="iconClass"
        class="size-[24px]"
      />
      <div>
        <p class="font-medium">
          {{ name }}
        </p>
        <Button
          v-if="showUrl"
          variant="link"
          :to="url"
          :title="url"
          class="max-w-full truncate p-0 text-sm font-medium text-[#475467]"
          target="_blank"
          :label="url"
        />
        <p
          v-if="size"
          class="text-sm text-neutral-400"
        >
          {{ formatSize }}
        </p>
      </div>
    </div>
    <Button
      v-if="url && !readonly"
      variant="ghost"
      color="neutral"
      class="text-gray-500 hover:text-gray-700"
      icon="mdi:microsoft-sharepoint"
      @click="downloadFile"
    />
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  url?: string
  name?: string
  extension?: string
  size?: number
  readonly?: boolean
  showUrl?: boolean
}>(), {
  url: '',
  name: '',
  extension: '',
  size: 0,
  readonly: false,
  showUrl: false,
})

const downloadFile = () => {
  if (props.url) {
    window.open(props.url, '_blank')
  }
}

// get size in human readable format
const formatSize = computed(() => {
  if (props.size < 1024) return `${props.size} B`
  else if (props.size < 1024 * 1024) return `${(props.size / 1024).toFixed(2)} KB`
  else if (props.size < 1024 * 1024 * 1024) return `${(props.size / (1024 * 1024)).toFixed(2)} MB`

  return `${(props.size / (1024 * 1024 * 1024)).toFixed(2)} GB`
})

// helper function to extract extension from filename
const getExtensionFromName = (filename: string) => {
  const lastDot = filename.lastIndexOf('.')

  return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : ''
}

// get file icon based on extension
const fileIcon = computed(() => {
  const ext = props.extension?.toLowerCase() || getExtensionFromName(props.name)
  const iconMap: { [key: string]: string } = {
    'pdf': 'material-icon-theme:pdf',
    'doc': 'vscode-icons:file-type-word',
    'docx': 'vscode-icons:file-type-word',
    'xls': 'vscode-icons:file-type-excel',
    'xlsx': 'vscode-icons:file-type-excel',
    'ppt': 'ri:file-ppt-line',
    'pptx': 'ri:file-ppt-line',
    'txt': 'ri:file-text-line',
    'rtf': 'ri:file-text-line',
    'jpg': 'ri:image-line',
    'jpeg': 'ri:image-line',
    'png': 'ri:image-line',
    'gif': 'ri:image-line',
    'svg': 'ri:image-line',
    'webp': 'ri:image-line',
    'bmp': 'ri:image-line',
    'ico': 'ri:image-line',
    'mp4': 'ri:video-line',
    'avi': 'ri:video-line',
    'mov': 'ri:video-line',
    'wmv': 'ri:video-line',
    'flv': 'ri:video-line',
    'webm': 'ri:video-line',
    'mkv': 'ri:video-line',
    'mp3': 'ri:music-line',
    'wav': 'ri:music-line',
    'flac': 'ri:music-line',
    'aac': 'ri:music-line',
    'ogg': 'ri:music-line',
    'wma': 'ri:music-line',
    'zip': 'ri:file-zip-line',
    'rar': 'ri:file-zip-line',
    '7z': 'ri:file-zip-line',
    'tar': 'ri:file-zip-line',
    'gz': 'ri:file-zip-line',
    'js': 'ri:code-line',
    'ts': 'ri:code-line',
    'vue': 'ri:code-line',
    'html': 'ri:code-line',
    'css': 'ri:code-line',
    'scss': 'ri:code-line',
    'sass': 'ri:code-line',
    'php': 'ri:code-line',
    'py': 'ri:code-line',
    'java': 'ri:code-line',
    'cpp': 'ri:code-line',
    'c': 'ri:code-line',
    'cs': 'ri:code-line',
    'go': 'ri:code-line',
    'rs': 'ri:code-line',
    'rb': 'ri:code-line',
    'json': 'ri:code-line',
    'xml': 'ri:code-line',
    'yaml': 'ri:code-line',
    'yml': 'ri:code-line',
    'csv': 'ri:file-list-line',
    'log': 'ri:file-text-line',
    'md': 'ri:markdown-line',
    'readme': 'ri:markdown-line',
  }

  return iconMap[ext] || 'ri:file-3-line'
})

// get icon color class based on file type
const iconClass = computed(() => {
  const ext = props.extension?.toLowerCase() || getExtensionFromName(props.name)
  const colorMap: { [key: string]: string } = {
    'pdf': 'text-red-500',
    'doc': 'text-blue-500',
    'docx': 'text-blue-500',
    'xls': 'text-green-500',
    'xlsx': 'text-green-500',
    'ppt': 'text-orange-500',
    'pptx': 'text-orange-500',
    'jpg': 'text-purple-500',
    'jpeg': 'text-purple-500',
    'png': 'text-purple-500',
    'gif': 'text-purple-500',
    'svg': 'text-purple-500',
    'webp': 'text-purple-500',
    'bmp': 'text-purple-500',
    'ico': 'text-purple-500',
    'mp4': 'text-pink-500',
    'avi': 'text-pink-500',
    'mov': 'text-pink-500',
    'wmv': 'text-pink-500',
    'flv': 'text-pink-500',
    'webm': 'text-pink-500',
    'mkv': 'text-pink-500',
    'mp3': 'text-indigo-500',
    'wav': 'text-indigo-500',
    'flac': 'text-indigo-500',
    'aac': 'text-indigo-500',
    'ogg': 'text-indigo-500',
    'wma': 'text-indigo-500',
    'zip': 'text-yellow-500',
    'rar': 'text-yellow-500',
    '7z': 'text-yellow-500',
    'tar': 'text-yellow-500',
    'gz': 'text-yellow-500',
    'js': 'text-emerald-500',
    'ts': 'text-emerald-500',
    'vue': 'text-emerald-500',
    'html': 'text-emerald-500',
    'css': 'text-emerald-500',
    'scss': 'text-emerald-500',
    'sass': 'text-emerald-500',
    'php': 'text-emerald-500',
    'py': 'text-emerald-500',
    'java': 'text-emerald-500',
    'cpp': 'text-emerald-500',
    'c': 'text-emerald-500',
    'cs': 'text-emerald-500',
    'go': 'text-emerald-500',
    'rs': 'text-emerald-500',
    'rb': 'text-emerald-500',
    'json': 'text-emerald-500',
    'xml': 'text-emerald-500',
    'yaml': 'text-emerald-500',
    'yml': 'text-emerald-500',
    'txt': 'text-slate-500',
    'rtf': 'text-slate-500',
    'csv': 'text-slate-500',
    'log': 'text-slate-500',
    'md': 'text-slate-500',
    'readme': 'text-slate-500',
  }

  return colorMap[ext] || 'text-gray-500'
})
</script>
