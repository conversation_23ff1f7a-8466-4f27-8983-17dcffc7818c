<template>
  <Card
    :ui="{
      base: 'bg-white dark:bg-slate-900',
      ring: 'ring-0',
      rounded: 'rounded-2xl',
      body: { padding: 'p-4 sm:p-5' },
    }"
  >
    <div class="flex items-start justify-between">
      <div>
        <h3 class="text-primary-900 text-xl font-bold dark:text-slate-100">
          {{ props.title ?? 'งานที่ต้องทำ' }}
        </h3>
        <p class="text-md -mt-0.5 text-slate-500 dark:text-slate-400">
          {{ props.subtitle ?? 'Checklist' }}
        </p>
      </div>
    </div>

    <div>
      <Separator
        v-if="!noUnderline"
        class="my-4"
      />
      <slot />
    </div>
  </Card>
</template>

<script setup lang="ts">
const props = defineProps<{
  title?: string
  subtitle?: string
  noUnderline?: boolean
}>()
</script>
