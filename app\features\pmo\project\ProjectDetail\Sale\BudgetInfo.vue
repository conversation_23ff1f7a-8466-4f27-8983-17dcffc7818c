<template>
  <PmoCardCollapsible
    title="งบ โครงการ"
    subtitle="Budget Info"
  >
    <Loader :loading="budget.status.value.isLoading">
      <InfoEditCard
        :form="form"
        :form-fields="formFields"
        :have-history="budget.data.value"
        :tab="tab"
        @save="onSave"
        @openHistory="onOpenHistory"
      />
    </Loader>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import InfoEditCard from '~/container/Pmo/InfoEditCard.vue'
import { useProjectBudgetLoader, useProjectCreateBudgetLoader } from '~/loaders/pmo/project'
import BudgetInfoHistory from './BudgetInfoHistory.vue'

const props = defineProps<{ projectId: string
  tab: string }>()

const dialog = useDialog()
const overlay = useOverlay()
const budget = useProjectBudgetLoader(props.projectId as string)
const createBudget = useProjectCreateBudgetLoader()
const historyModal = overlay.create(BudgetInfoHistory)
const noti = useNotification()

const onSave = (v: any) => {
  createBudget.run({
    data: v,
    urlBind: {
      project_id: props.projectId,
    },
  })

  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังส่งข้อมูล...',
  })
}

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    project_value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    bidbond_value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    fund_type: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    partner: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
  keepValuesOnUnmount: true,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'มูลค่าโครงการ (บาท)',
      name: 'project_value',
      placeholder: 'กรุณากรอกมูลค่าโครงการ (บาท)',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ประเภทงบ',
      name: 'fund_type',
      placeholder: 'กรุณาเลือกประเภทงบ',
      required: true,
      options: [
        {
          label: 'งบประจำปี',
          value: 'งบประจำปี',
        },
        {
          label: 'งบกลาง',
          value: 'งบกลาง',
        },
        {
          label: 'อื่น ๆ',
          value: 'อื่น ๆ',
        },
      ],
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'ยอดเงินค้ำประกัน',
      name: 'bidbond_value',
      placeholder: 'กรุณากรอกยอดเงินค้ำประกัน',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'คู่สัญญา',
      name: 'partner',
      placeholder: 'กรุณากรอกคู่สัญญา',
      required: true,
    },
  },
])

onMounted(() => {
  budget.run()
})

const onOpenHistory = () => {
  historyModal.open({
    projectId: props.projectId,
    tab: props.tab,
  })
}

useWatchTrue(() => budget.status.value.isSuccess, async () => {
  form.setValues({
    ...budget.data.value,
  })
})

useWatchTrue(() => createBudget.status.value.isSuccess, async () => {
  dialog.close()
  noti.success({
    title: 'แก้ไขงบโครงการ สำเร็จ',
  })

  budget.run()
})

useWatchTrue(() => createBudget.status.value.isError, async () => {
  dialog.close()
  noti.error({
    title: 'แก้ไขงบโครงการ ไม่สำเร็จ',
    description: StringHelper.getError(createBudget.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขงบโครงการ กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
