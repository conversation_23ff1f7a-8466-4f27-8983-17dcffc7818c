<template>
  <NuxtLayout>
    <ChecklistSetting />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import ChecklistSetting from '~/features/admin/ChecklistSetting/index.vue'

definePageMeta({
  layout: 'pmo',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.pmo.checklistSetting.permissions,
  },
})

useSeoMeta({
  title: routes.pmo.checklistSetting.label,
})

useApp().definePage({
  title: routes.pmo.checklistSetting.label,
  breadcrumbs: [routes.pmo.checklistSetting],
  sub_title: 'Default Checklist Settings',
})
</script>
