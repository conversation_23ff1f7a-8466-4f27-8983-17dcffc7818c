<template>
  <div class="space-y-6">
    <PmoCard
      title="ไทม์ไลน์เอกสาร"
      subtitle="Document Timeline "
    >
      <!-- Toolbar -->

      <div class="grid grid-cols-1 gap-4 lg:grid-cols-3">
        <div class="lg:col-span-2">
          <FormFields
            class="
        grid w-full gap-4
        md:mb-0 lg:grid-cols-4
      "
            :options="formFields"
          />
        </div>
        <div
          class="justify-items-end"
        >
          <div class="flex gap-2">
            <Button
              variant="outline"
              label="Export to CSV"
              icon="ep:download"
            />
            <Button
              variant="outline"
              :icon="timelineView ? 'streamline-plump:layout-window-4': 'i-fluent:timeline-24-regular'"
              @click="timelineView = !timelineView"
            />
          </div>
        </div>
      </div>

      <!-- Timeline  -->
      <div
        v-if="timelineView"
        class="space-y-10 pt-6"
      >
        <MonthGroups
          :items="docGroups"
        >
          <div
            v-if="page < documentItemByProject.fetch.options.totalPage"
            class="flex flex-col items-center justify-center"
          >
            <Separator
              orientation="vertical"
              class="-t-[100px] absolute h-[100]"
              :ui="{
                border: 'border-s-[2px] border-s-[#EAECF0]',
              }"
            />

            <Button
              variant="outline"
              label="เรียกดูไทม์ไลน์เพิ่มเติม"
              :loading="documentItemByProject.fetch.status.isLoading"
              icon="mi:arrow-down"
              @click="loadMore"
            />
          </div>
        </MonthGroups>
      </div>
      <!-- Timeline Table -->
      <div
        v-else
        class="space-y-10 pt-8"
      >
        <TimelineTable
          :items="documentItemByProject"
        />
      </div>
    </PmoCard>
  </div>
</template>

<script  lang="ts" setup>
import MonthGroups from './MonthGroups.vue'
import { watchDebounced } from '@vueuse/core'
import TimelineTable from './TimelineTable.vue'
import { usePmoDocumentItemByProjectIdPageLoader } from '~/loaders/pmo/documents'

const props = defineProps<{
  projectId: string
}>()

const timelineView = ref(true) // ย่อ/ขยาย
const documentItemByProject = usePmoDocumentItemByProjectIdPageLoader(props.projectId || '')
const docs = ref<IDocument[]>([])
const page = ref(1)
const order = ref('date DESC')
const isLoadMore = ref(false)

const loadMore = async () => {
  page.value = page.value + 1
  isLoadMore.value = true
  loadData()
}

const loadData = () => {
  documentItemByProject.fetchPage(page.value, '', {
    params: {
      order_by: form.values.sort,
      ...(form.values.q
        ? {
          q: form.values.q,
        }
        : {}),

    },
  })
}

onMounted(() => {
  isLoadMore.value = true
  form.setFieldValue('sort', order.value)
  loadData()
})

useWatchTrue(() => documentItemByProject.fetch.status.isSuccess, async () => {
  if (isLoadMore.value) {
    docs.value.push(...documentItemByProject.fetch.items)
    isLoadMore.value = false
  }
})

// UI helper type for timeline/table rendering
type UiDoc = {
  id: string
  title: string
  fileName: string
  tab_key: 'info' | 'sale' | 'pre-sale' | 'bidding'
  type: 'inbound' | 'outbound' | 'none'
  date: string // ISO
  createdBy?: IUser
  updatedBy?: IUser
  contentType?: string
  file: IFile

}

// util to extract filename from URL
const fileNameFromUrl = (u?: string) => {
  if (!u) return ''

  try {
    const p = new URL(u).pathname
    const seg = p.split('/').pop() || ''

    return seg
  } catch {
    return ''
  }
}

// mapper API item -> UiDoc
const mapApiItemToUiDoc = (it: IDocument): UiDoc => {
  const dir = String(it?.type || '').toUpperCase()
  const tab = String(it?.tab_key || '').toLowerCase()

  return {
    id: String(it.id),
    title: String(it.name || ''),
    fileName: String(it.file?.name || fileNameFromUrl(it.sharepoint_url) || ''),
    tab_key: (['info', 'sale', 'pre-sale', 'bidding'].includes(tab) ? tab : 'info') as UiDoc['tab_key'],
    type: dir === 'INBOUND' ? 'inbound' : dir === 'OUTBOUND' ? 'outbound' : 'none',
    date: String(it.date || it.created_at || new Date().toISOString()),
    createdBy: it.created_by,
    updatedBy: it.updated_by,
    contentType: 'day',
    file: it.file,
  }
}

const tab = ref<'all' | 'sale' | 'pre-sale' | 'bidding'>('all')
const q = ref('')

const apiDocs = computed<UiDoc[]>(() => {
  const arr = (docs.value as IDocument[]) || []

  return arr.map(mapApiItemToUiDoc)
})

const parseDate = (input: string | Date) => {
  if (input instanceof Date) return input
  const s = String(input)
  // try ISO first
  let d = new Date(s)
  // if invalid, try append T00:00:00 to support YYYY-MM-DD
  if (Number.isNaN(d.getTime())) d = new Date(`${s}T00:00:00`)

  // final fallback: manual Y-M-D
  if (Number.isNaN(d.getTime())) {
    const m = s.match(/^(\d{4})-(\d{2})-(\d{2})$/)
    if (m) d = new Date(Number(m[1]), Number(m[2]) - 1, Number(m[3]))
  }

  return d
}

const monthKey = (dateISO: string) => {
  const d = parseDate(dateISO)
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')

  return `${y}${m}`
}

const monthLabelTH = (dateISO: string) => {
  const d = parseDate(dateISO)

  // Thai + Buddhist calendar (falls back to Gregorian if environment lacks the calendar)
  try {
    return new Intl.DateTimeFormat('th-TH-u-ca-buddhist', {
      month: 'short',
      year: 'numeric',
    }).format(d)
  } catch {
    return new Intl.DateTimeFormat('th-TH', {
      month: 'short',
      year: 'numeric',
    }).format(d)
  }
}

const docGroups = computed(() => {
  let items = apiDocs.value.slice()

  if (form.values.sort === 'date ASC') {
    items = items.sort((a, b) => +parseDate(a.date) - +parseDate(b.date))
  } else {
    // default to DESC
    items = items.sort((a, b) => +parseDate(b.date) - +parseDate(a.date))
  }

  const result: UiDoc[] = []
  let lastMonthKey = ''

  for (const it of items) {
    const key = monthKey(it.date)

    if (key !== lastMonthKey) {
      const d = parseDate(it.date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')

      result.push({
        id: `month-${key}`,
        title: monthLabelTH(it.date),
        fileName: '',
        tab_key: 'info',
        type: 'none',
        date: `${year}-${month}-01`,
        contentType: 'month',
        file: it.file,
      })

      lastMonthKey = key
    }

    result.push(it)
  }

  return result
})

const exportCsv = () => {
  console.log('Export CSV', filtered.value.length, 'rows')
}

// toolbar
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
    sort: v.nullish(v.pipe(v.string())),
    is_active: v.nullish(v.pipe(v.string())),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'sort',
      placeholder: 'All Team',
      clearable: false,
      options: [{
        label: 'Newest',
        value: 'date DESC',
      },
      {
        label: 'Olddatest',
        value: 'date ASC',
      },
      ],
    },
  },

  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหาผู้ใช้งาน',
    },
  },

])

watchDebounced(form.values, (values) => {
  page.value = 1
  docs.value = []
  isLoadMore.value = true
  loadData()
}, {
  debounce: 300,
  deep: true,
})
</script>
