<template>
  <div class="rounded-lg bg-white p-4 ring-1 ring-gray-200">
    <TrackerForm
      v-if="showUpdateForm "
      :item="item"
      :is-edit="true"
      :date="item.date"
      @cancel="handleCancelUpdate"
      @success-update="handleSuccessUpdate"
    />
    <template v-else>
      <div
        class="flex items-center justify-between gap-2"
      >
        <div class="flex flex-col items-start gap-2 md:flex-row md:items-center">
          <div class="text-primary flex items-center gap-2">
            <Icon
              name="heroicons-outline:clock"
              class="size-6"
            />
            <p class="font-bold">
              {{ item.timing }} hr.
            </p>
            <Badge
              v-if="item.type "
              :color="item.type === 'non-project' ? 'neutral' : 'primary'"
              variant="soft"
            >
              {{ item.leave_type || item.type }}
            </Badge>
          </div>
          <p>{{ TRACKER_TYPE_LABEL[item.type as TRACKER_TYPE] }}</p>
        </div>
        <div
          class="space-x-2"
        >
          <Button
            variant="outline"
            color="neutral"
            icon="mage:pen"
            @click="updateTracker"
          />
          <Button
            variant="outline"
            color="neutral"
            trailing-icon="prime:trash"
            class="text-error"
            @click="deleteTracker"
          />
        </div>
      </div>
      <div
        v-if="item.description"
        class="mt-2"
      >
        <p
          class="text-sm break-words whitespace-pre-line"
          v-html="item.description"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import type { TRACKER_TYPE } from '~/constants/timesheet'
import TrackerForm from './TrackerForm.vue'
import { useTimeSheetPageLoader } from '~/loaders/admin/timesheet'

const emit = defineEmits(['refresh'])

const props = defineProps<{
  item: ITimesheet
  readonly?: boolean
}>()

const showUpdateForm = ref(false)
const trackers = useTimeSheetPageLoader()
const dialog = useDialog()

const updateTracker = () => {
  showUpdateForm.value = true
}

const handleCancelUpdate = () => {
  showUpdateForm.value = false
}

const handleSuccessUpdate = () => {
  emit('refresh')
  showUpdateForm.value = false
}

const noti = useNotification()

const deleteTracker = () => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: 'คุณต้องการลบข้อมูลนี้ใช่หรือไม่?',
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    trackers.deleteRun(props.item.id)

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}

useWatchTrue(() => trackers.delete.status.isSuccess, () => {
  dialog.close()
  noti.success({
    title: 'ลบข้อมูลสำเร็จ',
  })

  emit('refresh')
})

useWatchTrue(() => trackers.delete.status.isError, () => {
  dialog.close()
  noti.error({
    title: 'ลบข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(trackers.delete.status.errorData, 'เกิดข้อผิดพลาด'),
  })
})
</script>
