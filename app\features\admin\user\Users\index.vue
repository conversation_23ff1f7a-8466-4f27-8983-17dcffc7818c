<template>
  <div>
    <FormFields
      class="
        mb-4 grid w-full gap-4 md:mb-0
        lg:grid-cols-5
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="user.fetchPageChange"
      @search="user.fetchSearch"
    >
      <template #user-cell="{ row }">
        <AvatarProfile
          :item="row.original"
        />
      </template>

      <template #is_active-cell="{ row }">
        <div class="flex justify-center">
          <Switch
            :model-value="row.original.is_active"
            :label="row.original.is_active ? 'Active' : 'Inactive'"
            @update:modelValue="() => onSwitch(row.original)"
          />
        </div>
      </template>
      <template #actions-cell="{ row }">
        <Button
          icon="mage:pen"
          variant="ghost"
          color="neutral"
          square
          :to="routes.admin.userById(row.original.id).to"
        />
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useUserPageLoader } from '~/loaders/admin/user'
import { useTeamPageLoader } from '~/loaders/admin/team'
import { watchDebounced } from '@vueuse/core'

const team = useTeamPageLoader()
const route = useRoute()
const user = useUserPageLoader()
const noti = useNotification()
const dialog = useDialog()

user.fetchSetLoading()
team.fetchSetLoading()
onMounted(() => {
  user.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })

  team.fetchPage()
})

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
    team_code: v.nullish(v.pipe(v.string())),
    is_active: v.nullish(v.pipe(v.string())),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหาผู้ใช้งาน',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'team_code',
      placeholder: 'All Team',
      clearable: true,
      options: team.fetch.items.map((item) => ({
        label: item.name,
        value: item.code,
      })),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'is_active',
      placeholder: 'All Status',
      clearable: true,
      options: [
        {
          label: 'active',
          value: 'true',
        },
        {
          label: 'inactive',
          value: 'false',
        },
      ],
    },
  },
])

const tableOptions = useTable({
  repo: user,
  columns: () => [
    {
      accessorKey: 'user',
      header: 'User',
      type: COLUMN_TYPES.TEXT,
    },

    {
      accessorKey: 'created_at',
      header: 'Created At',
      type: COLUMN_TYPES.DATE,
    },

    {
      accessorKey: 'is_active',
      header: 'Status',
      meta: {
        class: {
          th: 'text-center',
        },
      },
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

const onSwitch = (value: IUser) => {
  dialog.confirm({
    title: 'เปลี่ยนแปลงสถานะการใช้งานบัญชี',
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    user.updateRun(value.id, {
      data: {
        is_active: !value.is_active,
      },
    })
  })
}

useWatchTrue(() => user.update.status.isSuccess, () => {
  dialog.close()
  noti.success({
    title: 'อัพเดทข้อมูลสำเร็จ',
    description: 'ข้อมูลของคุณถูกอัพเดทเรียบร้อยแล้ว',
  })

  user.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

useWatchTrue(() => user.update.status.isError, () => {
  dialog.close()

  noti.error({
    title: 'อัพเดทข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(user.update.status.errorData, 'เกิดข้อผิดพลาดในการส่งคำขอ กรุณาลองใหม่อีกครั้ง'),
  })

  user.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

watchDebounced(form.values, (values) => {
  user.fetchSearch(values.q || '', {
    params: {
      team_code: values.team_code || undefined,
      is_active: values.is_active || undefined,
    },
  })
}, {
  debounce: 300,
  deep: true,
})
</script>
