<template>
  <FlexDeck
    v-if="document.fetch.items && document.fetch.items.length"
    :options="tableOptions"
    container-class=" mt-2"
    @pageChange="document.fetchPageChange"
    @search="document.fetchSearch"
  >
    <template #default="{ row }: { row: IDocument }">
      <div class="rounded-lg bg-white p-3">
        <FileTemplateBox
          class="border-none bg-gray-50"
          extension="pdf"
          :name="row.name"
          :url="row.sharepoint_url"
          show-url
        />
      </div>
    </template>
  </FlexDeck>
  <Empty
    v-else
    message="No Document Template"
  />
</template>

<script setup lang="ts">
import { useTemplateDocumentPageLoader } from '~/loaders/pmo/template'

const props = defineProps<{ tab: string }>()

const document = useTemplateDocumentPageLoader()

document.fetchSetLoading()
onMounted(() => {
  document.fetchPage(1, '', {
    params: {
      tab_key: props.tab,
    },
  })
})

const tableOptions = useFlexDeck<IDocument>({
  options: {
    isRouteChange: true,
    isHidePagination: true,
  },
  repo: document,
})
</script>
