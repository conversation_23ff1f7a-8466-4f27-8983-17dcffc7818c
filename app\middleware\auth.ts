import { routes } from '~/constants/routes'

export default defineNuxtRouteMiddleware(async (to, _from) => {
  const nuxtApp = useNuxtApp()

  if (import.meta.client && nuxtApp.isHydrating && nuxtApp.payload.serverRendered) {
    return
  }

  if (!useAuth().isAuthenticated.value) {
    return navigateTo({
      path: routes.login.to,
      query: {
        redirect: to.fullPath,
      },
    })
  }

  const auth = useAuth()

  await auth.fetchMe.run()

  if (auth.fetchMe.status.value.isError) {
    return navigateTo({
      path: routes.logout.to,
      query: {
        redirect: to.fullPath,
      },
    }, {
      external: true,
    })
  }

  if ((!auth.me.value?.team_code) && to.path !== routes.chooseTeam.to) {
    return navigateTo({
      path: routes.chooseTeam.to,
    })
  }
})
