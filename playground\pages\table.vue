<template>
  <div class="space-y-8">
    <!-- Introduction -->
    <div>
      <h1
        class="text-2xl font-bold"
      >
        Table Component
      </h1>
      <p class="mt-2 text-gray-600">
        Display and manage tabular data with sorting, pagination, and selection
      </p>
    </div>

    <Log :data="store.fetch.items" />
    <div class="mx-auto w-full">
      <Table
        :options="tableOptions"
        @pageChange="store.fetchPageChange"
        @search="store.fetchSearch"
      />
    </div>
    <p class="text-xl font-bold">
      Table simple
    </p>
    <TableSimple
      :options="tableSimpleOptions"
    />
  </div>
</template>

<script lang="ts" setup>
import { type ITableItem, useTableStore } from '~/loaders/useTableLoader'
import { COLUMN_TYPES } from '#core/components/Table/types'

definePageMeta({
  layout: 'example',
})

useSeoMeta({
  title: 'table',
})

const store = useTableStore()

const tableOptions = useTable<ITableItem>({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: false,
  },
  columns: () => [
    {
      accessorKey: 'name',
      header: 'name',
    },
    {
      accessorKey: 'email',
      header: 'email',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 20,
      },
    },
    {
      accessorKey: 'data',
      type: COLUMN_TYPES.TEXT,
      header: 'any',
      cell: ({
        row,
      }) => {
        return row.original.data?.value
      },
    },
    {
      accessorKey: 'pic',
      header: 'pic',
      type: COLUMN_TYPES.IMAGE,
    },
    {
      accessorKey: 'salary',
      header: 'salary',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: {
          td: 'text-right',
          th: 'text-right',
        },
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'createdAt',
      type: COLUMN_TYPES.DATE_TIME,
    },
  ],
})

const tableSimpleOptions = useTableSimple<ITableItem>({
  items: () => store.fetch.items,
  status: () => store.fetch.status,
  options: {
    limit: 5,
  },
  columns: () => [
    {
      accessorKey: 'name',
      header: 'name',
    },
    {
      accessorKey: 'email',
      header: 'email',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 20,
      },
    },
    {
      accessorKey: 'data',
      type: COLUMN_TYPES.TEXT,
      header: 'any',
      cell: ({
        row,
      }) => {
        return row.original.data?.value
      },
    },
    {
      accessorKey: 'pic',
      header: 'pic',
      type: COLUMN_TYPES.IMAGE,
    },
    {
      accessorKey: 'salary',
      header: 'salary',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: {
          td: 'text-right',
          th: 'text-right',
        },
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'createdAt',
      type: COLUMN_TYPES.DATE_TIME,
    },
  ],
})

store.fetchSetLoading()
onMounted(() => {
  store.fetchPage()
})
</script>
