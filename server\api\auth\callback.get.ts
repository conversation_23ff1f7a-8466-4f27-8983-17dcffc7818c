import axios from 'axios'

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  const query = getQuery(event)
  const {
    token,
    error,
  } = query

  const errorMessage = encodeURIComponent('เข้าสู่ระบบไม่สำเร็จ มีบางอย่างผิดพลาด')

  if (error) {
    return sendRedirect(event, `/login?error=${errorMessage}`, 302)
  }

  try {
    await axios.get('/me', {
      baseURL: config.public.baseAPI,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    setCookie(event, 'token', token as string, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365,
    })
  } catch (error) {
    console.log(error)

    return sendRedirect(event, `/login?error=${errorMessage}`, 302)
  }

  return sendRedirect(event, '/', 302)
})
