<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="'ประวัติเอกสาร'"
    description="Document History"
  >
    <template #body>
      <FlexDeck
        :options="tableOptions"
        container-class="space-y-4 mt-2"
        @pageChange="history.fetchPageChange"
        @search="history.fetchSearch"
      >
        <template #default="{ row }: { row: IDocument }">
          <Card>
            <div class="flex items-center justify-between">
              <div class="text-sm font-medium">
                {{ row.name }}
              </div>
              <div class="flex justify-end gap-3" />
            </div>
          </Card>
        </template>
      </FlexDeck>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { usePmoDocumentHistoryByProjectIdPageLoader } from '~/loaders/pmo/documents'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  documentID: string
  projectId: string
}>()

const history = usePmoDocumentHistoryByProjectIdPageLoader(props.projectId, props.documentID)

const tableOptions = useFlexDeck<IDocument>({
  repo: history,
})

onMounted(() => {
  history.fetchPage(1, undefined, {})
})
</script>
