<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่ม Holiday
      </Button>
    </TeleportSafe>
    <FormFields
      class="
        mb-4 grid w-full gap-4
        lg:grid-cols-6
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="holidays.fetchPageChange"
      @search="holidays.fetchSearch"
    >
      <template #type-team="{ row }">
        <Badge
          variant="soft"
        >
          {{ row.original.team }}
        </Badge>
      </template>

      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import Form from '../Holidays/Form.vue'
import { watchDebounced } from '@vueuse/core'

const holidays = useHolidaysPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const route = useRoute()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหาวันหยุด',
    },
  },

])

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: holidays,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'w-2/3',
        },
      },
    },
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

holidays.fetchSetLoading()
onMounted(() => {
  holidays.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const onEdit = (values: IHolidays) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => holidays.update.status,
    onSubmit: (payload: IHolidays) => {
      holidays.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  addModal.open({
    status: () => holidays.add.status,
    onSubmit: (payload: IHolidays) => {
      holidays.addRun({
        data: payload,
      })
    },
  })
}

const onDelete = (values: IHolidays) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบ Holiday "${values.name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    holidays.deleteRun(values.id)
  })
}

watchDebounced(form.values, (values) => {
  holidays.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

useWatchTrue(
  () => holidays.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขHolidayไม่สำเร็จ',
      description: StringHelper.getError(holidays.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขHoliday กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => holidays.delete.status.isSuccess,
  () => {
    noti.success({
      title: 'ลบHolidayสำเร็จ',
      description: 'คุณได้ลบHolidayเรียบร้อยแล้ว',
    })

    holidays.fetchPage()
    dialog.close()
  },
)

useWatchTrue(
  () => holidays.delete.status.isError,
  () => {
    noti.error({
      title: 'ลบHolidayไม่สำเร็จ',
      description: StringHelper.getError(holidays.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบHoliday กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => holidays.add.status.isSuccess,
  () => {
    addModal.close()
    holidays.fetchPage()

    noti.success({
      title: 'เพิ่มHolidayสำเร็จ',
      description: 'คุณได้เพิ่มHolidayเรียบร้อยแล้ว',
    })

    dialog.close()
  },
)

useWatchTrue(
  () => holidays.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มHolidayไม่สำเร็จ',
      description: StringHelper.getError(holidays.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มHoliday กรุณาลองใหม่อีกครั้ง'),

    })

    dialog.close()
  },
)
</script>
