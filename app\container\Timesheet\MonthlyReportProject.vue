<template>
  <Card>
    <h2
      class="
          text-primary flex items-center justify-between gap-2 text-2xl
          font-bold
        "
    >
      Month Summary
      <div
        class="
            bg-primary flex size-12 items-center justify-center rounded-full
          "
      >
        <Icon
          name="mdi:chart-box-outline"
          class="size-6 text-white"
        />
      </div>
    </h2>
    <h4
      v-if="date"
      class="mb-4 font-bold"
    >
      {{ date.getMonth() }}
      /{{ date.getFullYear() }}
    </h4>

    <h3 class="mb-4 text-base font-bold">
      Total work time in the month
      <span class="text-primary text-2xl">{{ workingTrackedTiming?.toFixed(1) }}</span>
      hour from all
      <span class="text-primary text-2xl">{{ totalTrackedTiming?.toFixed(1) }}</span> hour
    </h3>
    <h4
      v-if="groupByProject.length > 0"
      class="mb-2 text-gray-400"
    >
      Divided by project group
    </h4>
    <div
      v-for="group in groupByProject"
      :key="group.type"
      class="mb-3 rounded-xl p-2 ring-1 ring-gray-200"
    >
      <div
        class="
            bg-primary-100 text-primary mb-2 flex justify-between rounded-lg
            px-3 py-2 font-bold
          "
      >
        <span>{{ group.typeLabel }}</span>
        <span>
          {{ group.totalTiming.toFixed(1) }} hour
          <span class="ml-2 text-sm text-gray-500">
            ({{ ((group.totalTiming / workingTrackedTiming) * 100).toFixed(2) }}%)
          </span>
        </span>
      </div>
      <template v-if="group.type !== TRACKER_TYPE.INTERNAL && group.type !== TRACKER_TYPE.EXTERNAL">
        <div
          v-for="(child, index) in group.children"
          :key="child.name"
          :class="[
            'flex flex-col gap-1 px-4 py-2',
            index !== group.children.length - 1 ? 'border-b border-gray-100' : '',
          ]"
        >
          <div class="flex justify-between">
            <span class="font-medium">{{ child.name }}</span>
            <span>
              {{ child.totalTiming.toFixed(1) }} hour
              <span class="text-sm text-gray-500">
                ({{ ((child.totalTiming / group.totalTiming) * 100).toFixed(2) }}%)
              </span>
            </span>
          </div>

          <ul
            v-if="group.type === TRACKER_TYPE.LEAVE"
            class="list-inside list-disc text-sm text-gray-500"
          >
            <li
              v-for="(item, idx) in child.items"
              :key="idx"
            >
              {{ formatDate(item.date) }}
            </li>
          </ul>
        </div>
      </template>
    </div>
  </Card>
</template>

<script lang="ts" setup>
import { TRACKER_TYPE } from '~/constants/timesheet'

const props = defineProps<{
  groupByProject: any[]
  date: Date | undefined
  totalTrackedTiming?: number
  workingTrackedTiming: number
}>()

const formatDate = (dateStr?: string) => {
  if (!dateStr) return ''
  const d = new Date(dateStr)
  if (Number.isNaN(d.getTime())) return dateStr

  const day = d.getDate().toString().padStart(2, '0')
  const month = (d.getMonth() + 1).toString().padStart(2, '0')
  const year = d.getFullYear()

  return `${day}/${month}/${year}`
}
</script>
