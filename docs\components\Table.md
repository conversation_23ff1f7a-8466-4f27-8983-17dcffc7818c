## Table (Component Suite)

### Overview
The Table system provides comprehensive data display with sorting, searching, pagination, and custom column rendering. Built on top of @nuxt/ui's Table component, it offers a composable-driven API that integrates seamlessly with data loading patterns.

### Key Features
- 📊 **Rich Data Display** - Support for text, numbers, images, dates, and custom content
- 🔍 **Built-in Search** - Real-time search with debouncing
- 📄 **Pagination** - Automatic pagination with customizable page sizes
- 🎨 **Column Types** - Pre-built renderers for common data types
- 📱 **Responsive Design** - Mobile-friendly table layouts
- ⚡ **Performance** - Optimized for large datasets
- 🔧 **Extensible** - Custom column components and cell renderers

### Registration
- Auto-registered by @finema/core
- Optional prefix via core.prefix (e.g., `<F-Table>`)

### Components
- **Table** (index.vue) - Main table component with full features
- **Table/Base** - Basic table without pagination
- **Table/Simple** - Simplified table for static data
- **Column Renderers**: ColumnText, ColumnNumber, ColumnImage, ColumnDate, ColumnDateTime

### Column Types
```ts
enum COLUMN_TYPES {
  TEXT = 'TEXT',           // Text with truncation support
  NUMBER = 'NUMBER',       // Formatted numbers
  IMAGE = 'IMAGE',         // Image thumbnails
  DATE = 'DATE',          // Date formatting
  DATE_TIME = 'DATE_TIME', // Date and time formatting
  COMPONENT = 'COMPONENT'  // Custom Vue components
}
```

### Basic Usage

```vue
<template>
  <div class="space-y-6">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-bold">Users</h1>
      <Button @click="refreshData">Refresh</Button>
    </div>

    <Table
      :options="tableOptions"
      @pageChange="store.fetchPageChange"
      @search="store.fetchSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { COLUMN_TYPES } from '#core/components/Table/types'

// Set up data store using usePageLoader
const store = usePageLoader({
  url: '/api/users',
  transform: (response) => response.data
})

// Configure table with columns and options
const tableOptions = useTable({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: false
  },
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Full Name'
    },
    {
      accessorKey: 'email',
      header: 'Email Address',
      type: COLUMN_TYPES.TEXT,
      meta: { max: 30 }
    },
    {
      accessorKey: 'avatar',
      header: 'Avatar',
      type: COLUMN_TYPES.IMAGE
    },
    {
      accessorKey: 'salary',
      header: 'Salary',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: { td: 'text-right', th: 'text-right' }
      }
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      type: COLUMN_TYPES.DATE_TIME
    }
  ]
})

// Load initial data
onMounted(() => {
  store.fetchPage()
})

const refreshData = () => {
  store.fetchPage()
}
</script>
```

### Advanced Example (From Playground)

This example demonstrates a comprehensive table with custom columns, actions, and advanced features:

```vue
<template>
  <div class="space-y-6">
    <!-- Header with actions -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">Employee Management</h1>
        <p class="text-gray-600 mt-1">Manage your team members and their information</p>
      </div>
      <div class="flex gap-3">
        <Button variant="outline" @click="exportData">
          <Icon name="i-heroicons-arrow-down-tray" class="mr-2" />
          Export
        </Button>
        <Button @click="addEmployee">
          <Icon name="i-heroicons-plus" class="mr-2" />
          Add Employee
        </Button>
      </div>
    </div>

    <!-- Debug panel (development only) -->
    <Log
      v-if="isDev"
      title="Table State"
      :data-items="[store.fetch.items, store.fetch.status]"
    />

    <!-- Main table -->
    <Table
      :options="tableOptions"
      @pageChange="store.fetchPageChange"
      @search="store.fetchSearch"
    >
      <!-- Custom empty state -->
      <template #empty-state>
        <div class="text-center py-12">
          <Icon name="i-heroicons-users" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No employees</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by adding your first employee.</p>
          <div class="mt-6">
            <Button @click="addEmployee">
              <Icon name="i-heroicons-plus" class="mr-2" />
              Add Employee
            </Button>
          </div>
        </div>
      </template>

      <!-- Custom error state -->
      <template #error>
        <div class="text-center py-12">
          <Icon name="i-heroicons-exclamation-triangle" class="mx-auto h-12 w-12 text-red-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading data</h3>
          <p class="mt-1 text-sm text-gray-500">There was a problem loading the employee data.</p>
          <div class="mt-6">
            <Button @click="store.fetchPage()" variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </template>
    </Table>
  </div>
</template>

<script setup lang="ts">
import { COLUMN_TYPES } from '#core/components/Table/types'

const isDev = process.dev

// Employee data interface
interface IEmployee {
  id: number
  name: string
  email: string
  department: string
  position: string
  salary: number
  avatar?: string
  status: 'active' | 'inactive' | 'pending'
  startDate: string
  lastLogin?: string
}

// Data store setup
const store = usePageLoader<IEmployee>({
  url: '/api/employees',
  transform: (response) => response.data,
  defaultSort: { field: 'name', direction: 'asc' }
})

// Table configuration
const tableOptions = useTable<IEmployee>({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: true, // Sync with URL params
    searchPlaceholder: 'Search employees...'
  },
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Employee',
      cell: ({ row }) => {
        const employee = row.original
        return h('div', { class: 'flex items-center gap-3' }, [
          h('img', {
            src: employee.avatar || '/default-avatar.png',
            alt: employee.name,
            class: 'w-8 h-8 rounded-full object-cover'
          }),
          h('div', [
            h('div', { class: 'font-medium' }, employee.name),
            h('div', { class: 'text-sm text-gray-500' }, employee.position)
          ])
        ])
      }
    },
    {
      accessorKey: 'email',
      header: 'Contact',
      type: COLUMN_TYPES.TEXT,
      meta: { max: 25 }
    },
    {
      accessorKey: 'department',
      header: 'Department',
      cell: ({ row }) => {
        const dept = row.original.department
        const colors = {
          'Engineering': 'bg-blue-100 text-blue-800',
          'Marketing': 'bg-green-100 text-green-800',
          'Sales': 'bg-purple-100 text-purple-800',
          'HR': 'bg-yellow-100 text-yellow-800'
        }
        return h('span', {
          class: `inline-flex px-2 py-1 text-xs font-medium rounded-full ${colors[dept] || 'bg-gray-100 text-gray-800'}`
        }, dept)
      }
    },
    {
      accessorKey: 'salary',
      header: 'Salary',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: { td: 'text-right', th: 'text-right' },
        format: 'currency'
      }
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status
        const statusConfig = {
          active: { color: 'green', label: 'Active' },
          inactive: { color: 'red', label: 'Inactive' },
          pending: { color: 'yellow', label: 'Pending' }
        }
        const config = statusConfig[status]
        return h('span', {
          class: `inline-flex px-2 py-1 text-xs font-medium rounded-full bg-${config.color}-100 text-${config.color}-800`
        }, config.label)
      }
    },
    {
      accessorKey: 'startDate',
      header: 'Start Date',
      type: COLUMN_TYPES.DATE
    },
    {
      accessorKey: 'lastLogin',
      header: 'Last Login',
      type: COLUMN_TYPES.DATE_TIME,
      cell: ({ row }) => {
        const lastLogin = row.original.lastLogin
        if (!lastLogin) return h('span', { class: 'text-gray-400' }, 'Never')
        return h(ColumnDateTime, { value: lastLogin })
      }
    },
    {
      accessorKey: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const employee = row.original
        return h('div', { class: 'flex gap-2' }, [
          h(Button, {
            size: 'sm',
            variant: 'ghost',
            onClick: () => editEmployee(employee)
          }, 'Edit'),
          h(Button, {
            size: 'sm',
            variant: 'ghost',
            color: 'red',
            onClick: () => deleteEmployee(employee)
          }, 'Delete')
        ])
      }
    }
  ]
})

// Actions
const dialog = useDialog()
const notification = useNotification()

const addEmployee = () => {
  // Navigate to add employee form
  navigateTo('/employees/new')
}

const editEmployee = (employee: IEmployee) => {
  navigateTo(`/employees/${employee.id}/edit`)
}

const deleteEmployee = async (employee: IEmployee) => {
  const confirmed = await dialog.confirm({
    title: 'Delete Employee',
    description: `Are you sure you want to delete ${employee.name}? This action cannot be undone.`,
    confirmText: 'Delete',
    cancelText: 'Cancel'
  })

  if (confirmed) {
    try {
      await $fetch(`/api/employees/${employee.id}`, { method: 'DELETE' })
      notification.success({
        title: 'Employee Deleted',
        description: `${employee.name} has been removed from the system.`
      })
      store.fetchPage() // Refresh data
    } catch (error) {
      notification.error({
        title: 'Delete Failed',
        description: 'There was an error deleting the employee.'
      })
    }
  }
}

const exportData = async () => {
  try {
    const data = await $fetch('/api/employees/export')
    // Handle export logic
    notification.success({
      title: 'Export Complete',
      description: 'Employee data has been exported successfully.'
    })
  } catch (error) {
    notification.error({
      title: 'Export Failed',
      description: 'There was an error exporting the data.'
    })
  }
}

// Load initial data
onMounted(() => {
  store.fetchPage()
})
</script>
```

### Column Configuration

#### Text Columns
```ts
{
  accessorKey: 'description',
  header: 'Description',
  type: COLUMN_TYPES.TEXT,
  meta: {
    max: 50, // Truncate after 50 characters
    class: { td: 'text-left', th: 'text-left' }
  }
}
```

#### Number Columns
```ts
{
  accessorKey: 'price',
  header: 'Price',
  type: COLUMN_TYPES.NUMBER,
  meta: {
    format: 'currency', // or 'decimal', 'percent'
    precision: 2,
    class: { td: 'text-right font-mono', th: 'text-right' }
  }
}
```

#### Image Columns
```ts
{
  accessorKey: 'thumbnail',
  header: 'Image',
  type: COLUMN_TYPES.IMAGE,
  meta: {
    size: 'sm', // 'xs', 'sm', 'md', 'lg'
    fallback: '/placeholder.png'
  }
}
```

#### Date Columns
```ts
{
  accessorKey: 'createdAt',
  header: 'Created',
  type: COLUMN_TYPES.DATE_TIME,
  meta: {
    format: 'dd/MM/yyyy HH:mm', // Custom date format
    timezone: 'Asia/Bangkok'
  }
}
```

#### Custom Component Columns
```ts
{
  accessorKey: 'status',
  header: 'Status',
  type: COLUMN_TYPES.COMPONENT,
  component: StatusBadge, // Your custom component
  meta: {
    props: { variant: 'outline' } // Props passed to component
  }
}
```

#### Custom Cell Renderer
```ts
{
  accessorKey: 'actions',
  header: 'Actions',
  cell: ({ row }) => {
    return h('div', { class: 'flex gap-2' }, [
      h(Button, { size: 'sm', onClick: () => edit(row.original) }, 'Edit'),
      h(Button, { size: 'sm', color: 'red', onClick: () => delete(row.original) }, 'Delete')
    ])
  }
}
```

### Table Options

```ts
interface ITableOptions {
  // Data
  rawData: T[]
  status: IStatus
  columns: TableColumn<T>[]

  // Pagination
  isHidePagination?: boolean
  pageOptions?: IPageOptions

  // Search
  isEnabledSearch?: boolean
  searchPlaceholder?: string

  // Display
  isHideCaption?: boolean
  emptyMessage?: string

  // Behavior
  isRouteChange?: boolean // Sync with URL params
  sortable?: boolean
  selectable?: boolean
}
```

### Events

- **@pageChange(page: number)** - Fired when page changes
- **@search(query: string)** - Fired when search input changes
- **@sort(column: string, direction: 'asc' | 'desc')** - Fired when column is sorted
- **@select(rows: T[])** - Fired when rows are selected (if selectable)

### Data Loading Integration

The Table component works seamlessly with data loading composables:

```ts
// Using usePageLoader for server-side pagination
const store = usePageLoader({
  url: '/api/users',
  transform: (response) => response.data,
  defaultSort: { field: 'name', direction: 'asc' }
})

// Using useListLoader for client-side operations
const listStore = useListLoader({
  url: '/api/users',
  transform: (response) => response.data
})

// Using static data with useTableSimple
const staticTable = useTableSimple({
  items: () => users.value,
  columns: () => columns.value
})
```

### Responsive Design

Tables automatically adapt to different screen sizes:

```vue
<template>
  <!-- Desktop: Full table -->
  <!-- Tablet: Horizontal scroll -->
  <!-- Mobile: Card layout (when configured) -->
  <Table :options="tableOptions" />
</template>
```

### Accessibility Features

- **Keyboard Navigation** - Arrow keys, Tab, Enter support
- **Screen Reader Support** - Proper ARIA labels and descriptions
- **Focus Management** - Clear focus indicators
- **Sort Indicators** - Visual and auditory sort direction feedback

### Performance Optimization

```ts
// Virtual scrolling for large datasets
const tableOptions = useTable({
  repo: store,
  options: {
    virtualScrolling: true,
    itemHeight: 48 // Fixed row height for virtual scrolling
  },
  columns: () => columns.value
})

// Debounced search
const tableOptions = useTable({
  repo: store,
  options: {
    searchDebounce: 300 // ms
  },
  columns: () => columns.value
})
```

### Theming and Customization

Tables inherit from the global theme configuration:

```ts
// nuxt.config.ts
export default defineNuxtConfig({
  appConfig: {
    ui: {
      table: {
        wrapper: 'relative overflow-auto',
        base: 'min-w-full table-auto text-sm',
        thead: 'bg-gray-50 dark:bg-gray-800',
        tbody: 'bg-white dark:bg-gray-900',
        tr: 'hover:bg-gray-50 dark:hover:bg-gray-800/50',
        th: 'px-4 py-3 text-left font-medium text-gray-900 dark:text-white',
        td: 'px-4 py-3 text-gray-700 dark:text-gray-300'
      }
    }
  }
})
```

### Common Patterns

#### Master-Detail View
```vue
<template>
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <div class="lg:col-span-2">
      <Table
        :options="tableOptions"
        @select="selectedRows = $event"
      />
    </div>
    <div class="lg:col-span-1">
      <DetailPanel :item="selectedRows[0]" />
    </div>
  </div>
</template>
```

#### Bulk Actions
```vue
<template>
  <div>
    <div v-if="selectedRows.length > 0" class="mb-4 p-4 bg-blue-50 rounded-lg">
      <div class="flex justify-between items-center">
        <span>{{ selectedRows.length }} items selected</span>
        <div class="flex gap-2">
          <Button @click="bulkEdit">Edit</Button>
          <Button color="red" @click="bulkDelete">Delete</Button>
        </div>
      </div>
    </div>

    <Table
      :options="tableOptions"
      @select="selectedRows = $event"
    />
  </div>
</template>
```

#### Inline Editing
```vue
<script setup>
const editingRow = ref(null)

const columns = [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => {
      const isEditing = editingRow.value === row.original.id
      return isEditing
        ? h(InputText, {
            modelValue: row.original.name,
            onUpdate: (value) => updateField(row.original.id, 'name', value)
          })
        : h('span', row.original.name)
    }
  }
]
</script>
```

### Related Components
- **useTable** - Table configuration composable
- **usePageLoader** - Server-side data loading
- **useListLoader** - Client-side data loading
- **Column Components** - ColumnText, ColumnNumber, ColumnImage, etc.

### Related Composables
- **useTable** - Main table configuration
- **useTableSimple** - Simple table for static data
- **usePageLoader** - Paginated data loading
- **useListLoader** - List data loading

### Best Practices

1. **Use appropriate column types** for better UX and performance
2. **Implement proper loading states** to show data fetching progress
3. **Handle empty and error states** gracefully
4. **Optimize for mobile** with responsive design considerations
5. **Use virtual scrolling** for large datasets (1000+ rows)
6. **Implement proper search debouncing** to avoid excessive API calls
7. **Provide clear column headers** and sorting indicators
8. **Use consistent data formatting** across similar columns

### Troubleshooting

**Table not updating when data changes?**
- Ensure your data store is reactive
- Check that the `repo` reference is stable

**Search not working?**
- Verify `isEnabledSearch: true` in options
- Check that your data loader supports search

**Pagination not showing?**
- Ensure `isHidePagination: false` (default)
- Verify your data includes pagination metadata

**Custom columns not rendering?**
- Check that your cell function returns valid VNodes
- Ensure component imports are available

