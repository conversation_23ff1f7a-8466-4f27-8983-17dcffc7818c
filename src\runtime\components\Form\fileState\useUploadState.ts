import { useDropZone, useFileDialog } from '@vueuse/core'

import type { TemplateRef } from 'vue'
import type { IUploadDropzoneAutoProps } from '../InputUploadDropzoneAuto/types'
import { useWatchTrue } from './../../../../../dist/runtime/composables/useWatch'
import PreviewModal from './PreviewModal.vue'
import type { IUploadRequest } from '#core/composables/useUpload'
import type { IFileValue } from '#core/components/Form/types'
import { computed, ref, useOverlay } from '#imports'
import { useUploadLoader } from '#core/composables/useUpload'
import { useFileAllocate, useFileProgress } from '#core/helpers/componentHelper'
import { StringHelper } from '#core/utils/StringHelper'
import { _get } from '#core/utils/lodash'

export enum UploadState {
  EMPTY = 'empty',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error',
}

export const useUploadState = (
  props: IUploadDropzoneAutoProps,
  emits: any,
  onChange: (value: IFileValue | undefined) => void,
  setErrors: (error: string) => void,
  value: any,
  acceptedFileTypes: any,
  wrapperProps: any,
  dropzoneRef: TemplateRef<HTMLDivElement | undefined>,
) => {
  const overlay = useOverlay()
  const previewModal = overlay.create(PreviewModal)
  const selectedFile = ref<File>()
  const fileAllocate = useFileAllocate(selectedFile, props)
  // File progress tracking
  const {
    percent,
    onDownloadProgress,
    onUploadProgress,
  } = useFileProgress()

  // Upload configuration
  const request: IUploadRequest = {
    requestOptions: {
      ...props.requestOptions,
      onDownloadProgress,
      onUploadProgress,
    },
    pathURL: props.uploadPathURL,
  }

  const upload = useUploadLoader(request)

  // File validation
  const validateFile = (file: File): boolean => {
    if (props.accept && fileAllocate.acceptFile.value) {
      // Check file type by MIME type or file extension
      const acceptedTypes = fileAllocate.acceptFile.value
      // Split accepted types by comma and trim whitespace
      const acceptedTypesList = acceptedTypes.split(',').map((type) => type.trim())

      // Get file extension from file name
      const fileExtension = file.name.toLowerCase().split('.').pop()

      // Check if file type matches any accepted type
      const isValidFileType = acceptedTypesList.some((acceptedType) => {
        // Handle file extensions like ".jpg", ".png", "jpg", "png"
        if (acceptedType.startsWith('.')) {
          const extension = acceptedType.slice(1).toLowerCase() // Remove leading dot

          return fileExtension === extension
        } else if (!acceptedType.includes('/') && !acceptedType.includes('*')) {
          // Handle extensions without leading dot
          return fileExtension === acceptedType.toLowerCase()
        }

        // Handle wildcard patterns like "image/*", "video/*"
        if (acceptedType.endsWith('/*')) {
          const baseType = acceptedType.slice(0, -2) // Remove "/*"

          return file.type.startsWith(baseType + '/')
        }

        // Handle exact MIME type matches like "application/pdf"
        return file.type === acceptedType
      })

      if (!isValidFileType) {
        setErrors('ประเภทไฟล์ไม่ถูกต้อง (รองรับเฉพาะ ' + acceptedTypesList.join(', ') + ')')

        return false
      }
    }

    // Check file size
    if (props.maxSize) {
      const maxSizeBytes = (fileAllocate.acceptFileSizeKb.value || 0) * 1024

      if (file.size > maxSizeBytes) {
        if (fileAllocate.isAcceptFileUseMb.value) {
          setErrors(`ขนาดไฟล์ต้องไม่เกิน ${fileAllocate.acceptFileSizeMb.value} MB`)
        } else {
          setErrors(`ขนาดไฟล์ต้องไม่เกิน ${fileAllocate.acceptFileSizeKb.value} KB`)
        }

        return false
      }
    }

    setErrors('')

    return true
  }

  // File processing
  const processFile = (file: File): void => {
    if (!validateFile(file)) return

    selectedFile.value = file
    emits('change', file)

    const formData = new FormData()

    formData.append(props.bodyKey!, file)
    upload.run({
      data: formData,
    })
  }

  // Event handlers
  const handleFileDrop = (files: File[] | null): void => {
    if (wrapperProps.value.disabled || wrapperProps.value.readonly || !files?.length || !isEmpty.value) return

    const file = files[0]

    if (file) {
      processFile(file)
    }
  }

  // File dialog configuration
  const fileDialog = useFileDialog({
    accept: acceptedFileTypes.value || '',
    directory: false,
    multiple: false,
  })

  // Dropzone configuration
  const dropzone = useDropZone(dropzoneRef, {
    onDrop: handleFileDrop,
    // dataTypes: typeof props.accept === 'string' ? [props.accept] : props.accept,
    multiple: false,
    preventDefaultForUnhandled: false,
  })

  // Computed current state
  const currentState = computed(() => {
    if (value.value) return UploadState.SUCCESS
    if (selectedFile.value && upload.status.value.isLoading) return UploadState.UPLOADING
    if (selectedFile.value && upload.status.value.isError) return UploadState.ERROR

    return UploadState.EMPTY
  })

  // State checks
  const isEmpty = computed(() => currentState.value === UploadState.EMPTY)
  const isUploading = computed(() => currentState.value === UploadState.UPLOADING)
  const isSuccess = computed(() => currentState.value === UploadState.SUCCESS)
  const isError = computed(() => currentState.value === UploadState.ERROR)

  const handleInputChange = (event: Event): void => {
    if (wrapperProps.value.disabled || wrapperProps.value.readonly) return

    const file = (event.target as HTMLInputElement).files?.[0]

    if (file) {
      processFile(file)
    }
  }

  fileDialog.onChange((files) => {
    if (files?.length) {
      processFile(files[0])
    }
  })

  const handleOpenFile = (): void => {
    if (wrapperProps.value.disabled || wrapperProps.value.readonly) return
    fileDialog.open()
  }

  const handleDeleteFile = (): void => {
    fileDialog.reset()
    upload.clear()
    selectedFile.value = undefined
    onChange(undefined)
    emits('delete')
  }

  const handleRetryUpload = (): void => {
    if (selectedFile.value) {
      const formData = new FormData()

      formData.append(props.bodyKey!, selectedFile.value)
      upload.run(formData)
    }
  }

  const handlePreview = (): void => {
    previewModal.open({
      value: value.value,
    })
  }

  useWatchTrue(
    () => upload.status.value.isSuccess,
    () => {
      if (upload.data.value) {
        const fileValue: IFileValue = {
          url: _get(upload.data.value, props.responseURL!),
          path: _get(upload.data.value, props.responsePath!),
          name: _get(upload.data.value, props.responseName!) || selectedFile.value?.name,
          size: Number(_get(upload.data.value, props.responseSize!) || selectedFile.value?.size),
          id: _get(upload.data.value, props.responseID!),
        }

        value.value = fileValue
        emits('success', fileValue)
      }
    },
  )

  useWatchTrue(
    () => upload.status.value.isError,
    () => {
      setErrors('พบข้อผิดพลาด: ' + StringHelper.getError(upload.status.value.errorData))
    },
  )

  return {
    // State
    currentState,
    isEmpty,
    isUploading,
    isSuccess,
    isError,

    selectedFile,

    // Upload utilities
    upload,
    dropzone,
    percent,

    // Handlers
    handleInputChange,
    handleOpenFile,
    handleDeleteFile,
    handleRetryUpload,
    handlePreview,
  }
}
