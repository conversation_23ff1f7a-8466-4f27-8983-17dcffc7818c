export const LEAVE_TYPE = {
  ANNUAL: 'ANNUAL',
  SICK: 'SICK',
  BUSINESS: 'BUSINESS',
  MENSTRUAL: 'MENSTRUAL',
  BIRTHDAY: 'BIRTHDAY',
  ORDINATION: 'ORDINATION',
} as const

export type LeaveType = (typeof LEAVE_TYPE)[keyof typeof LEAVE_TYPE]

export const LEAVE_LABEL: Record<LeaveType, string> = {
  [LEAVE_TYPE.ANNUAL]: 'Annual Leave',
  [LEAVE_TYPE.SICK]: 'Sick Leave',
  [LEAVE_TYPE.BUSINESS]: 'Business Leave',
  [LEAVE_TYPE.MENSTRUAL]: 'Menstrual Leave',
  [LEAVE_TYPE.BIRTHDAY]: 'Birthday Leave',
  [LEAVE_TYPE.ORDINATION]: 'Ordination Leave',
}

export const LEAVE_OPTIONS = Object.values(LEAVE_TYPE).map((type) => ({
  label: LEAVE_LABEL[type],
  value: type,
}))
