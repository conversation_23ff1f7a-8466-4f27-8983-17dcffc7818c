export const usePmoProjectsPageLoader = defineStore('pmo-projects', () => {
  const options = useRequestOptions()

  return usePageLoader<IPmoProject>({
    baseURL: '/pmo/projects',
    getBaseRequestOptions: options.auth,
  })
})

export const usePmoProjectCheckSlugPageLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<{ is_available: string }>({
    method: 'POST',
    url: '/pmo/projects/check-slug',
    getRequestOptions: options.auth,
  })
}

export const usePmoCommentByProjectIdPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<ICommentItem>({
    baseURL: `/pmo/projects/${projectId}/comments`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoCollaboratorsByProjectIdPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<ICollaborators>({
    baseURL: `/pmo/projects/${projectId}/collaborators`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoCommentVersionByProjectIdPageLoader = (projectId: string, commentId: string) => {
  const options = useRequestOptions()

  return usePageLoader<ICommentItem>({
    baseURL: `/pmo/projects/${projectId}/comments/${commentId}/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectRemarkLoader = (projectId: string) => {
  const options = useRequestOptions()

  return useObjectLoader<IRemark>({
    method: 'GET',
    url: `/pmo/projects/${projectId}/remark`,
    getRequestOptions: options.auth,
  })
}

export const useProjectCreateRemarkLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IRemark>({
    method: 'POST',
    url: `/pmo/projects/:project_id/remark`,
    getRequestOptions: options.auth,
  })
}

export const useProjectRemarkVersionLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IRemark>({
    baseURL: `/pmo/projects/${projectId}/remark/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectBudgetLoader = (projectId: string) => {
  const options = useRequestOptions()

  return useObjectLoader<IBudgetInfo>({
    method: 'GET',
    url: `/pmo/projects/${projectId}/budget`,
    getRequestOptions: options.auth,
  })
}

export const useProjectCreateBudgetLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IBudgetInfo>({
    method: 'POST',
    url: `/pmo/projects/:project_id/budget`,
    getRequestOptions: options.auth,
  })
}

export const useProjectBudgetVersionLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IBudgetInfo>({
    baseURL: `/pmo/projects/${projectId}/budget/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectBiddingLoader = (projectId: string) => {
  const options = useRequestOptions()

  return useObjectLoader<IBiddingInfo>({
    method: 'GET',
    url: `/pmo/projects/${projectId}/bidding`,
    getRequestOptions: options.auth,
  })
}

export const useProjectCreateBiddingLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IBiddingInfo>({
    method: 'POST',
    url: `/pmo/projects/:project_id/bidding`,
    getRequestOptions: options.auth,
  })
}

export const useProjectBiddingVersionLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IBiddingInfo>({
    baseURL: `/pmo/projects/${projectId}/bidding/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectContractLoader = (projectId: string) => {
  const options = useRequestOptions()

  return useObjectLoader<IContractInfo>({
    method: 'GET',
    url: `/pmo/projects/${projectId}/contract`,
    getRequestOptions: options.auth,
  })
}

export const useProjectCreateContractLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IContractInfo>({
    method: 'POST',
    url: `/pmo/projects/:project_id/contract`,
    getRequestOptions: options.auth,
  })
}

export const useProjectContractVersionLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IContractInfo>({
    baseURL: `/pmo/projects/${projectId}/contract/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectBidBondLoader = (projectId: string) => {
  const options = useRequestOptions()

  return useObjectLoader<IBidbondInfo>({
    method: 'GET',
    url: `/pmo/projects/${projectId}/bidbond`,
    getRequestOptions: options.auth,
  })
}

export const useProjectCreateBidBondLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IBidbondInfo>({
    method: 'POST',
    url: `/pmo/projects/:project_id/bidbond`,
    getRequestOptions: options.auth,
  })
}

export const useProjectBidBondVersionLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IBidbondInfo>({
    baseURL: `/pmo/projects/${projectId}/bidbond/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectLGLoader = (projectId: string) => {
  const options = useRequestOptions()

  return useObjectLoader<ILgInfo>({
    method: 'GET',
    url: `/pmo/projects/${projectId}/lg`,
    getRequestOptions: options.auth,
  })
}

export const useProjectCreateLGLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<ILgInfo>({
    method: 'POST',
    url: `/pmo/projects/:project_id/lg`,
    getRequestOptions: options.auth,
  })
}

export const useProjectLGVersionLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<ILgInfo>({
    baseURL: `/pmo/projects/${projectId}/lg/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const useProjectChecklistItemLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IChecklistItem>({
    baseURL: `/pmo/projects/${projectId}/checklist-items`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoContactPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader({
    baseURL: `/pmo/projects/${projectId}/contacts`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoCompetitorPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader({
    baseURL: `/pmo/projects/${projectId}/competitors`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoPartnerPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader({
    baseURL: `/pmo/projects/${projectId}/partners`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoVendorItemPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader({
    baseURL: `/pmo/projects/${projectId}/vendor-items`,
    getBaseRequestOptions: options.auth,
  })
}
