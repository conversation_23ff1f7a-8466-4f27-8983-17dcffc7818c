export const enum PROJECT_TAB_TYPE {
  INFO = 'INFO',
  CONFIDENTIAL = 'CONFIDENTIAL',
  SALES = 'SALES',
  PRESALES = 'PRESALES',
  BIDDING = 'BIDDING',
  PMO = 'PMO',
  BIZCO = 'BIZ<PERSON>',
  COLLABORATORS = 'COLLABORATORS',
}

export const PROJECT_TAB_LABEL = {
  [PROJECT_TAB_TYPE.INFO]: 'Project Info.',
  [PROJECT_TAB_TYPE.CONFIDENTIAL]: 'Confidential',
  [PROJECT_TAB_TYPE.SALES]: 'Sales',
  [PROJECT_TAB_TYPE.PRESALES]: 'Pre-Sales',
  [PROJECT_TAB_TYPE.BIDDING]: 'Bidding',
  [PROJECT_TAB_TYPE.PMO]: 'PMO',
  [PROJECT_TAB_TYPE.BIZCO]: 'Bizco',
  [PROJECT_TAB_TYPE.COLLABORATORS]: 'Collaborators',
}

export type TabLabel = (typeof PROJECT_TAB_LABEL)[PROJECT_TAB_TYPE]

export const PROJECT_TAB_KEY = {
  [PROJECT_TAB_TYPE.INFO]: 'projectinfo',
  [PROJECT_TAB_TYPE.CONFIDENTIAL]: 'confidential',
  [PROJECT_TAB_TYPE.SALES]: 'sales',
  [PROJECT_TAB_TYPE.PRESALES]: 'presales',
  [PROJECT_TAB_TYPE.BIDDING]: 'bidding',
  [PROJECT_TAB_TYPE.PMO]: 'pmo',
  [PROJECT_TAB_TYPE.BIZCO]: 'bizco',
  [PROJECT_TAB_TYPE.COLLABORATORS]: 'collaborators',
}

export type TabKey = (typeof PROJECT_TAB_KEY)[PROJECT_TAB_TYPE]

export const PROJECT_TAB_KEY_TO_TYPE: Record<TabKey, PROJECT_TAB_TYPE> = {
  projectinfo: PROJECT_TAB_TYPE.INFO,
  confidential: PROJECT_TAB_TYPE.CONFIDENTIAL,
  sales: PROJECT_TAB_TYPE.SALES,
  presales: PROJECT_TAB_TYPE.PRESALES,
  bidding: PROJECT_TAB_TYPE.BIDDING,
  pmo: PROJECT_TAB_TYPE.PMO,
  bizco: PROJECT_TAB_TYPE.BIZCO,
} as const

export const tabList = [
  {
    value: PROJECT_TAB_TYPE.CONFIDENTIAL,
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.CONFIDENTIAL],
    key: PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL],
    permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
    icon: 'material-symbols:lock-outline',
  },
  {
    value: PROJECT_TAB_TYPE.SALES,
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.SALES],
    key: PROJECT_TAB_KEY[PROJECT_TAB_TYPE.SALES],
    permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
    icon: '',
  },
  {
    value: PROJECT_TAB_TYPE.PRESALES,
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PRESALES],
    key: PROJECT_TAB_KEY[PROJECT_TAB_TYPE.PRESALES],
    permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
    icon: '',
  },
  {
    value: PROJECT_TAB_TYPE.BIDDING,
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIDDING],
    key: PROJECT_TAB_KEY[PROJECT_TAB_TYPE.BIDDING],
    permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
    icon: '',
  },
  {
    value: PROJECT_TAB_TYPE.BIZCO,
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIZCO],
    key: PROJECT_TAB_KEY[PROJECT_TAB_TYPE.BIZCO],
    permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
    icon: '',
  },
  {
    value: PROJECT_TAB_TYPE.PMO,
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PMO],
    key: PROJECT_TAB_KEY[PROJECT_TAB_TYPE.PMO],
    permissions: [PMO_PERMISSION.MODIFY, PMO_PERMISSION.READONLY],
    icon: '',
  },
] as const
