<template>
  <div class="w-full rounded-lg border-[#EAECF0] bg-[#F9FAFB] p-5">
    <div class="flex justify-between">
      <div class="mb-3 flex items-center gap-2 font-bold">
        <img
          :src="iconImages?.[CHECKIN.ONSITE]"
          alt="icon"
          class="size-6"
        />
        Onsite
      </div>
      <ButtonActionIcon
        size="md"
        icon="material-symbols:close-rounded"
        @click="$emit('close')"
      />
    </div>
    <div
      v-for="(_, index) in form.values.checkin"
      :key="index"
      class="my-4"
    >
      <div
        class="
          relative flex w-full flex-col items-start gap-2
          md:flex-row
        "
      >
        <div class="grid w-full gap-3 md:grid-cols-2">
          <FormFields

            :options="formFields(index).value"
          />
          <div class="flex gap-3">
            <FormFields
              class="w-full"
              :options="formFieldSiteLocation(index).value"
            />
            <Button
              v-if="index !== 0"
              icon="prime:trash"
              color="error"
              variant="outline"
              class="mt-6 ml-auto size-11 justify-center rounded-md"
              @click.prevent="removeCheckin(index)"
            />
          </div>
        </div>
      </div>
    </div>
    <Button
      icon="mdi-plus"
      variant="outline"
      color="neutral"
      class=""
      @click.prevent="addCheckinItem"
    >
      Add more site location
    </Button>
  </div>
</template>

<script lang="ts" setup>
import type { FormContext } from 'vee-validate'

defineEmits<{
  (e: 'close'): void
}>()

const {
  form,
} = defineProps<{
  form: FormContext<Partial<any>>
}>()

const removeCheckin = (index: number) => {
  const checkin = [...(form.values.checkin || [])]

  checkin.splice(index, 1)
  form.setFieldValue('checkin', checkin)
}

const addCheckinItem = () => {
  form.setFieldValue('checkin', [
    ...(form.values.checkin || []),
    {
      location_onsite: undefined,
      onsite_period: undefined,
    },
  ])
}

const formFields = (index: number) => {
  const fields: any[] = []

  fields.push({
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Select a site location',
      name: `checkin.${index}.location_onsite`,
      required: true,
    },
  },
  )

  return createFormFields(() => fields)
}

const formFieldSiteLocation = (index: number) => {
  const fields: any[] = []

  fields.push(
    {
      type: INPUT_TYPES.SELECT,
      props: {
        label: 'Period',
        name: `checkin.${index}.onsite_period`,
        required: true,
        options: (form.values.type.length > 1 || form.values.checkin?.length > 1
          ? PERIOD_OPTIONS.filter((opt) => opt.value !== PERIOD.FULL_DAY)
          : PERIOD_OPTIONS).filter((option) => option.value !== PERIOD.MANY_DAYS),
      },
    })

  return createFormFields(() => fields)
}
</script>
