import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '5618d581-7a31-4b1d-be2b-ffcf3308c0f8',
      name: '<PERSON>',
      email: 'johndo<PERSON>@email.commmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm',
      salary: 3123123,
      pic: 'https://placehold.co/600x400',
      data: {
        value: 'test nested',
      },
      createdAt: '2024-02-03T00:00:00Z',
      updated_at: '2024-02-03T00:00:00Z',
      url: 'asdasdadas.com',
      user: {
        id: '25250d1e-cc45-49bc-91ec-7e9fc34f29e2',
        created_at: '2025-09-02T10:16:37.195Z',
        updated_at: '2025-09-02T10:16:37.195Z',
        email: '<EMAIL>',
        full_name: '<EMAIL>',
        display_name: 'Di<PERSON>',
        position: 'ACCOUNT',
        team_code: 'ACCOUNT',
        company: '',
        avatar_url: '',
        slack_id: null,
        is_active: true,
        joined_date: null,
        team: {
          id: 'f8b0fe1c-8916-4261-b3eb-a3f4ee1187c1',
          created_at: '2025-08-26T10:36:53.598Z',
          updated_at: '2025-09-08T09:14:56.153Z',
          name: 'Accountant Team',
          code: 'ACCOUNT',
          color: 'violet',
          description: '',
          working_start_at: '09:00:00',
          working_end_at: '21:00:00',
          created_by_id: null,
          updated_by_id: '96d3ec63-be14-41c6-9268-ea8095d2a73b',
          deleted_by_id: null,
        },
      },
      createdBy: null,
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913be5a3ec7c',
      name: 'Peter Parker',
      email: '<EMAIL>',
      salary: 56456,
      pic: 'https://placehold.co/600x400',
      data: {
        value: 'test nested',
      },
      createdAt: '2024-02-03T00:00:00Z',
      updated_at: '2024-02-03T00:00:00Z',
      url: 'asdasdadas.com',
      user: {
        id: '25250d1e-cc45-49bc-91ec-7e9fc34f29e2',
        created_at: '2025-09-02T10:16:37.195Z',
        updated_at: '2025-09-02T10:16:37.195Z',
        email: '<EMAIL>',
        full_name: '<EMAIL>',
        display_name: 'Diow',
        position: 'ACCOUNT',
        team_code: 'ACCOUNT',
        company: '',
        avatar_url: '',
        slack_id: null,
        is_active: true,
        joined_date: null,
        team: {
          id: 'f8b0fe1c-8916-4261-b3eb-a3f4ee1187c1',
          created_at: '2025-08-26T10:36:53.598Z',
          updated_at: '2025-09-08T09:14:56.153Z',
          name: 'Accountant Team',
          code: 'ACCOUNT',
          color: 'violet',
          description: '',
          working_start_at: '09:00:00',
          working_end_at: '21:00:00',
          created_by_id: null,
          updated_by_id: '96d3ec63-be14-41c6-9268-ea8095d2a73b',
          deleted_by_id: null,
        },
      },
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913asda2a3ec7c',
      name: 'Ive Got A Name',
      email: '<EMAIL>',
      salary: 43224,
      pic: 'https://placehold.co/600x400',
      data: {
        value: 'test nested',
      },
      createdAt: '2024-02-03T00:00:00Z',
      updated_at: '2024-02-03T00:00:00Z',
      url: 'asdasdadas.com',
      user: {
        id: '25250d1e-cc45-49bc-91ec-7e9fc34f29e2',
        created_at: '2025-09-02T10:16:37.195Z',
        updated_at: '2025-09-02T10:16:37.195Z',
        email: '<EMAIL>',
        full_name: '<EMAIL>',
        display_name: 'Diow',
        position: 'ACCOUNT',
        team_code: 'ACCOUNT',
        company: '',
        avatar_url: '',
        slack_id: null,
        is_active: true,
        joined_date: null,
        team: {
          id: 'f8b0fe1c-8916-4261-b3eb-a3f4ee1187c1',
          created_at: '2025-08-26T10:36:53.598Z',
          updated_at: '2025-09-08T09:14:56.153Z',
          name: 'Accountant Team',
          code: 'ACCOUNT',
          color: 'violet',
          description: '',
          working_start_at: '09:00:00',
          working_end_at: '21:00:00',
          created_by_id: null,
          updated_by_id: '96d3ec63-be14-41c6-9268-ea8095d2a73b',
          deleted_by_id: null,
        },
      },
      createdBy: 'user_uuid',
    },
    {
      id: '5618d581-7a31-4b1d-be2b-ffcf3308c0f8',
      name: 'John Xerxes Doe',
      pic: 'https://placehold.co/600x400',
      email: '<EMAIL>',
      createdAt: '2024-02-03T00:00:00Z',
      updated_at: '2024-02-03T00:00:00Z',
      url: 'asdasdadas.com',
      user: {
        id: '25250d1e-cc45-49bc-91ec-7e9fc34f29e2',
        created_at: '2025-09-02T10:16:37.195Z',
        updated_at: '2025-09-02T10:16:37.195Z',
        email: '<EMAIL>',
        full_name: '<EMAIL>',
        display_name: 'Diow',
        position: 'ACCOUNT',
        team_code: 'ACCOUNT',
        company: '',
        avatar_url: '',
        slack_id: null,
        is_active: true,
        joined_date: null,
        team: {
          id: 'f8b0fe1c-8916-4261-b3eb-a3f4ee1187c1',
          created_at: '2025-08-26T10:36:53.598Z',
          updated_at: '2025-09-08T09:14:56.153Z',
          name: 'Accountant Team',
          code: 'ACCOUNT',
          color: 'violet',
          description: '',
          working_start_at: '09:00:00',
          working_end_at: '21:00:00',
          created_by_id: null,
          updated_by_id: '96d3ec63-be14-41c6-9268-ea8095d2a73b',
          deleted_by_id: null,
        },
      },
      createdBy: null,
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-913be5a3ec7c',
      name: 'Peter Benjamin Parker',
      pic: 'https://placehold.co/600x400',
      email: '<EMAIL>',
      createdAt: '2024-02-03T00:00:00Z',
      updated_at: '2024-02-03T00:00:00Z',
      url: 'asdasdadas.com',
      user: {
        id: '25250d1e-cc45-49bc-91ec-7e9fc34f29e2',
        created_at: '2025-09-02T10:16:37.195Z',
        updated_at: '2025-09-02T10:16:37.195Z',
        email: '<EMAIL>',
        full_name: '<EMAIL>',
        display_name: 'Diow',
        position: 'ACCOUNT',
        team_code: 'ACCOUNT',
        company: '',
        avatar_url: '',
        slack_id: null,
        is_active: true,
        joined_date: null,
        team: {
          id: 'f8b0fe1c-8916-4261-b3eb-a3f4ee1187c1',
          created_at: '2025-08-26T10:36:53.598Z',
          updated_at: '2025-09-08T09:14:56.153Z',
          name: 'Accountant Team',
          code: 'ACCOUNT',
          color: 'violet',
          description: '',
          working_start_at: '09:00:00',
          working_end_at: '21:00:00',
          created_by_id: null,
          updated_by_id: '96d3ec63-be14-41c6-9268-ea8095d2a73b',
          deleted_by_id: null,
        },
      },
      createdBy: 'user_uuid',
    },
    {
      id: 'ebdbd192-5b08-4281-84f2-91300222a3ec7c',
      name: 'Ive Got A Name 2',
      pic: 'https://placehold.co/600x400',
      email: '<EMAIL>',
      createdAt: '2024-02-03T00:00:00Z',
      updated_at: '2024-02-03T00:00:00Z',
      url: 'asdasdadas.com',
      user: {
        id: '25250d1e-cc45-49bc-91ec-7e9fc34f29e2',
        created_at: '2025-09-02T10:16:37.195Z',
        updated_at: '2025-09-02T10:16:37.195Z',
        email: '<EMAIL>',
        full_name: '<EMAIL>',
        display_name: 'Diow',
        position: 'ACCOUNT',
        team_code: 'ACCOUNT',
        company: '',
        avatar_url: '',
        slack_id: null,
        is_active: true,
        joined_date: null,
        team: {
          id: 'f8b0fe1c-8916-4261-b3eb-a3f4ee1187c1',
          created_at: '2025-08-26T10:36:53.598Z',
          updated_at: '2025-09-08T09:14:56.153Z',
          name: 'Accountant Team',
          code: 'ACCOUNT',
          color: 'violet',
          description: '',
          working_start_at: '09:00:00',
          working_end_at: '21:00:00',
          created_by_id: null,
          updated_by_id: '96d3ec63-be14-41c6-9268-ea8095d2a73b',
          deleted_by_id: null,
        },
      },
      createdBy: 'user_uuid',
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
