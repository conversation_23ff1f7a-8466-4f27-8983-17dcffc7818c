<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="แก้ไข วันหลักประกันสัญญา"
    description="date info."
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <input
          type="submit"
          hidden
        />
      </form>
    </template>
    <template #footer>
      <div class="flex w-full justify-end gap-3">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ยกเลิก
        </Button>
        <Button
          :loading="status().isLoading"
          :disabled="!form.meta.value.dirty"
          @click="onSubmit"
        >
          บันทึก
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: ILgInfo | null
  status: () => IStatus
  onSubmit: (values: ILgInfo) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    start_date: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    end_date: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันเริ่มหลักประกันสัญญา',
      name: 'start_date',
      placeholder: 'กรุณากรอกวันเริ่มหลักประกันสัญญา',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันสิ้นสุดหลักประกันสัญญา',
      name: 'end_date',
      placeholder: 'กรุณากรอกวันสิ้นสุดหลักประกันสัญญา',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as ILgInfo)
})
</script>
