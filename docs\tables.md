# Table System Guide

The @finema/core table system provides a powerful, flexible solution for displaying and managing tabular data. Built on top of @nuxt/ui's Table component, it offers advanced features like sorting, searching, pagination, and custom column rendering.

## Overview

### Key Features
- 📊 **Rich Data Display** - Support for text, numbers, images, dates, and custom content
- 🔍 **Built-in Search** - Real-time search with debouncing and server-side filtering
- 📄 **Smart Pagination** - Automatic pagination with customizable page sizes
- 🎨 **Column Types** - Pre-built renderers for common data types
- 📱 **Responsive Design** - Mobile-friendly table layouts with horizontal scrolling
- ⚡ **Performance Optimized** - Virtual scrolling for large datasets
- 🔧 **Highly Extensible** - Custom column components and cell renderers
- ♿ **Accessibility First** - Keyboard navigation and screen reader support

### Core Components
- **Table** - Main table component with full features
- **Table/Base** - Basic table without pagination
- **Table/Simple** - Simplified table for static data
- **Column Renderers** - ColumnText, ColumnNumber, ColumnImage, ColumnDate, ColumnDateTime

### Data Loading Integration
- **usePageLoader** - Server-side pagination and filtering
- **useListLoader** - Client-side operations on loaded data
- **useTable** - Table configuration and state management
- **useTableSimple** - Simple table for static data

## Quick Start

### Basic Table Setup

```vue
<template>
  <div class="space-y-6">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-bold">Users</h1>
      <Button @click="refreshData">Refresh</Button>
    </div>
    
    <Table 
      :options="tableOptions" 
      @pageChange="store.fetchPageChange"
      @search="store.fetchSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { COLUMN_TYPES } from '#core/components/Table/types'

// Set up data store
const store = usePageLoader({
  url: '/api/users',
  transform: (response) => response.data
})

// Configure table
const tableOptions = useTable({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: false
  },
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name'
    },
    {
      accessorKey: 'email',
      header: 'Email',
      type: COLUMN_TYPES.TEXT,
      meta: { max: 30 }
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      type: COLUMN_TYPES.DATE_TIME
    }
  ]
})

// Load initial data
onMounted(() => {
  store.fetchPage()
})

const refreshData = () => {
  store.fetchPage()
}
</script>
```

## Column Types and Configuration

### Text Columns

#### Basic Text
```ts
{
  accessorKey: 'description',
  header: 'Description',
  type: COLUMN_TYPES.TEXT
}
```

#### Text with Truncation
```ts
{
  accessorKey: 'description',
  header: 'Description',
  type: COLUMN_TYPES.TEXT,
  meta: {
    max: 50, // Truncate after 50 characters
    showTooltip: true // Show full text on hover
  }
}
```

#### Text with Custom Styling
```ts
{
  accessorKey: 'status',
  header: 'Status',
  type: COLUMN_TYPES.TEXT,
  meta: {
    class: {
      td: 'font-medium uppercase text-sm',
      th: 'text-center'
    }
  }
}
```

### Number Columns

#### Basic Number
```ts
{
  accessorKey: 'price',
  header: 'Price',
  type: COLUMN_TYPES.NUMBER
}
```

#### Formatted Currency
```ts
{
  accessorKey: 'salary',
  header: 'Salary',
  type: COLUMN_TYPES.NUMBER,
  meta: {
    format: 'currency',
    currency: 'USD',
    precision: 2,
    class: { td: 'text-right font-mono', th: 'text-right' }
  }
}
```

#### Percentage
```ts
{
  accessorKey: 'completion',
  header: 'Progress',
  type: COLUMN_TYPES.NUMBER,
  meta: {
    format: 'percent',
    precision: 1,
    suffix: '%'
  }
}
```

### Image Columns

#### Basic Image
```ts
{
  accessorKey: 'avatar',
  header: 'Avatar',
  type: COLUMN_TYPES.IMAGE
}
```

#### Customized Image
```ts
{
  accessorKey: 'thumbnail',
  header: 'Image',
  type: COLUMN_TYPES.IMAGE,
  meta: {
    size: 'lg', // 'xs', 'sm', 'md', 'lg'
    shape: 'rounded', // 'square', 'rounded', 'circle'
    fallback: '/placeholder.png',
    alt: (row) => `Image for ${row.name}`
  }
}
```

### Date and Time Columns

#### Date Only
```ts
{
  accessorKey: 'birthDate',
  header: 'Birth Date',
  type: COLUMN_TYPES.DATE,
  meta: {
    format: 'dd/MM/yyyy'
  }
}
```

#### Date and Time
```ts
{
  accessorKey: 'lastLogin',
  header: 'Last Login',
  type: COLUMN_TYPES.DATE_TIME,
  meta: {
    format: 'dd/MM/yyyy HH:mm',
    timezone: 'Asia/Bangkok',
    relative: true // Show "2 hours ago" style
  }
}
```

### Custom Component Columns

#### Using Pre-built Components
```ts
{
  accessorKey: 'status',
  header: 'Status',
  type: COLUMN_TYPES.COMPONENT,
  component: StatusBadge,
  meta: {
    props: { variant: 'outline' }
  }
}
```

#### Custom Cell Renderer
```ts
{
  accessorKey: 'actions',
  header: 'Actions',
  cell: ({ row }) => {
    const item = row.original
    return h('div', { class: 'flex gap-2' }, [
      h(Button, {
        size: 'sm',
        variant: 'ghost',
        onClick: () => editItem(item)
      }, 'Edit'),
      h(Button, {
        size: 'sm',
        variant: 'ghost',
        color: 'red',
        onClick: () => deleteItem(item)
      }, 'Delete')
    ])
  }
}
```

#### Complex Custom Renderer
```ts
{
  accessorKey: 'user',
  header: 'User',
  cell: ({ row }) => {
    const user = row.original
    return h('div', { class: 'flex items-center gap-3' }, [
      h('img', {
        src: user.avatar || '/default-avatar.png',
        alt: user.name,
        class: 'w-10 h-10 rounded-full object-cover'
      }),
      h('div', [
        h('div', { class: 'font-medium text-gray-900' }, user.name),
        h('div', { class: 'text-sm text-gray-500' }, user.email),
        user.isOnline && h('div', { class: 'text-xs text-green-600 flex items-center gap-1' }, [
          h('div', { class: 'w-2 h-2 bg-green-500 rounded-full' }),
          'Online'
        ])
      ])
    ])
  }
}
```

## Data Loading Patterns

### Server-Side Pagination

```ts
// Set up page loader for server-side operations
const store = usePageLoader({
  url: '/api/users',
  transform: (response) => response.data,
  defaultSort: { field: 'name', direction: 'asc' },
  defaultFilters: { status: 'active' }
})

const tableOptions = useTable({
  repo: store,
  options: {
    isEnabledSearch: true,
    isRouteChange: true, // Sync with URL parameters
    searchPlaceholder: 'Search users...'
  },
  columns: () => columns.value
})

// The store automatically handles:
// - Pagination: store.fetchPageChange(page)
// - Search: store.fetchSearch(query)
// - Sorting: store.fetchSort(field, direction)
```

### Client-Side Operations

```ts
// Load all data once, then filter/sort client-side
const listStore = useListLoader({
  url: '/api/users',
  transform: (response) => response.data
})

const tableOptions = useTable({
  repo: listStore,
  options: {
    isEnabledSearch: true,
    clientSideOperations: true // Enable client-side filtering
  },
  columns: () => columns.value
})
```

### Static Data

```ts
// For static or computed data
const users = ref([
  { id: 1, name: 'John Doe', email: '<EMAIL>' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
])

const tableOptions = useTableSimple({
  items: () => users.value,
  columns: () => columns.value,
  options: {
    isEnabledSearch: true // Client-side search
  }
})
```

## Advanced Features

### Sorting Configuration

```ts
const tableOptions = useTable({
  repo: store,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      sortable: true, // Enable sorting for this column
      defaultSort: 'asc' // Default sort direction
    },
    {
      accessorKey: 'email',
      header: 'Email',
      sortable: false // Disable sorting
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      sortable: true,
      sortKey: 'created_at' // Use different key for API
    }
  ]
})
```

### Row Selection

```vue
<template>
  <div>
    <!-- Bulk actions bar -->
    <div v-if="selectedRows.length > 0" class="mb-4 p-4 bg-blue-50 rounded-lg">
      <div class="flex justify-between items-center">
        <span class="font-medium">{{ selectedRows.length }} items selected</span>
        <div class="flex gap-2">
          <Button @click="bulkEdit" size="sm">Edit</Button>
          <Button @click="bulkDelete" color="red" size="sm">Delete</Button>
        </div>
      </div>
    </div>

    <Table 
      :options="tableOptions"
      @select="selectedRows = $event"
    />
  </div>
</template>

<script setup lang="ts">
const selectedRows = ref([])

const tableOptions = useTable({
  repo: store,
  options: {
    selectable: true,
    selectMode: 'multiple' // 'single' or 'multiple'
  },
  columns: () => columns.value
})

const bulkEdit = () => {
  // Handle bulk edit
  console.log('Editing:', selectedRows.value)
}

const bulkDelete = async () => {
  const confirmed = await dialog.confirm({
    title: 'Delete Items',
    description: `Delete ${selectedRows.value.length} selected items?`
  })
  
  if (confirmed) {
    // Handle bulk delete
  }
}
</script>
```

### Filtering and Search

#### Advanced Search Configuration
```ts
const tableOptions = useTable({
  repo: store,
  options: {
    isEnabledSearch: true,
    searchPlaceholder: 'Search by name, email, or department...',
    searchDebounce: 300, // Debounce search input
    searchFields: ['name', 'email', 'department'], // Specific fields to search
    minSearchLength: 2 // Minimum characters before search
  },
  columns: () => columns.value
})
```

#### Custom Filters
```vue
<template>
  <div class="space-y-4">
    <!-- Filter controls -->
    <div class="flex gap-4 items-center">
      <Select
        v-model="filters.department"
        placeholder="All Departments"
        :options="departmentOptions"
        @change="applyFilters"
      />
      <Select
        v-model="filters.status"
        placeholder="All Statuses"
        :options="statusOptions"
        @change="applyFilters"
      />
      <Button variant="outline" @click="clearFilters">
        Clear Filters
      </Button>
    </div>

    <Table :options="tableOptions" />
  </div>
</template>

<script setup lang="ts">
const filters = ref({
  department: null,
  status: null
})

const departmentOptions = [
  { value: 'engineering', label: 'Engineering' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'sales', label: 'Sales' }
]

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' }
]

const applyFilters = () => {
  store.setFilters(filters.value)
  store.fetchPage()
}

const clearFilters = () => {
  filters.value = { department: null, status: null }
  applyFilters()
}
</script>
```

### Responsive Design

#### Mobile-Optimized Tables
```vue
<template>
  <!-- Desktop: Full table -->
  <div class="hidden md:block">
    <Table :options="desktopTableOptions" />
  </div>

  <!-- Mobile: Card layout -->
  <div class="md:hidden space-y-4">
    <div v-for="item in store.fetch.items" :key="item.id" class="bg-white rounded-lg border p-4">
      <div class="flex items-center justify-between mb-2">
        <h3 class="font-medium">{{ item.name }}</h3>
        <span class="text-sm text-gray-500">{{ formatDate(item.createdAt) }}</span>
      </div>
      <p class="text-gray-600 text-sm mb-3">{{ item.email }}</p>
      <div class="flex gap-2">
        <Button size="sm" @click="editItem(item)">Edit</Button>
        <Button size="sm" variant="outline" @click="viewItem(item)">View</Button>
      </div>
    </div>
  </div>
</template>
```

#### Horizontal Scrolling
```vue
<template>
  <div class="overflow-x-auto">
    <Table :options="tableOptions" class="min-w-full" />
  </div>
</template>
```

### Performance Optimization

#### Virtual Scrolling for Large Datasets
```ts
const tableOptions = useTable({
  repo: store,
  options: {
    virtualScrolling: true,
    itemHeight: 48, // Fixed row height required for virtual scrolling
    overscan: 5 // Number of items to render outside visible area
  },
  columns: () => columns.value
})
```

#### Lazy Loading with Intersection Observer
```vue
<template>
  <div>
    <Table :options="tableOptions" />

    <!-- Load more trigger -->
    <div
      ref="loadMoreTrigger"
      v-if="hasMore && !store.fetch.status.isLoading"
      class="h-10 flex items-center justify-center"
    >
      <Button @click="loadMore" variant="outline">
        Load More
      </Button>
    </div>

    <!-- Loading indicator -->
    <div v-if="store.fetch.status.isLoading" class="text-center py-4">
      <Loader />
    </div>
  </div>
</template>

<script setup lang="ts">
const loadMoreTrigger = ref()
const hasMore = computed(() => store.fetch.items.length < store.fetch.options.totalCount)

// Auto-load more when scrolling near bottom
useIntersectionObserver(loadMoreTrigger, ([{ isIntersecting }]) => {
  if (isIntersecting && hasMore.value && !store.fetch.status.isLoading) {
    loadMore()
  }
})

const loadMore = () => {
  const nextPage = Math.floor(store.fetch.items.length / store.fetch.options.limit) + 1
  store.fetchPage(nextPage, true) // true = append to existing items
}
</script>
```

### Custom Table States

#### Empty State
```vue
<template>
  <Table :options="tableOptions">
    <template #empty-state>
      <div class="text-center py-12">
        <Icon name="i-heroicons-users" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ hasFilters ? 'Try adjusting your search or filters' : 'Get started by adding your first user' }}
        </p>
        <div class="mt-6">
          <Button v-if="!hasFilters" @click="addUser">
            <Icon name="i-heroicons-plus" class="mr-2" />
            Add User
          </Button>
          <Button v-else variant="outline" @click="clearFilters">
            Clear Filters
          </Button>
        </div>
      </div>
    </template>
  </Table>
</template>
```

#### Error State
```vue
<template>
  <Table :options="tableOptions">
    <template #error>
      <div class="text-center py-12">
        <Icon name="i-heroicons-exclamation-triangle" class="mx-auto h-12 w-12 text-red-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading data</h3>
        <p class="mt-1 text-sm text-gray-500">
          There was a problem loading the data. Please try again.
        </p>
        <div class="mt-6 flex gap-3 justify-center">
          <Button @click="store.fetchPage()" variant="outline">
            Try Again
          </Button>
          <Button @click="reportError" variant="ghost">
            Report Issue
          </Button>
        </div>
      </div>
    </template>
  </Table>
</template>
```

#### Loading State
```vue
<template>
  <Table :options="tableOptions">
    <template #loading-state>
      <div class="space-y-4">
        <!-- Skeleton rows -->
        <div v-for="i in 5" :key="i" class="animate-pulse">
          <div class="flex space-x-4 p-4">
            <div class="rounded-full bg-gray-200 h-10 w-10"></div>
            <div class="flex-1 space-y-2 py-1">
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
              <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </Table>
</template>
```

### Inline Editing

```vue
<script setup lang="ts">
const editingRow = ref(null)
const editingField = ref(null)

const startEdit = (rowId: string, field: string) => {
  editingRow.value = rowId
  editingField.value = field
}

const saveEdit = async (rowId: string, field: string, value: any) => {
  try {
    await $fetch(`/api/users/${rowId}`, {
      method: 'PATCH',
      body: { [field]: value }
    })

    // Update local data
    const item = store.fetch.items.find(item => item.id === rowId)
    if (item) {
      item[field] = value
    }

    editingRow.value = null
    editingField.value = null

    notification.success({ title: 'Updated successfully' })
  } catch (error) {
    notification.error({ title: 'Update failed' })
  }
}

const cancelEdit = () => {
  editingRow.value = null
  editingField.value = null
}

const columns = computed(() => [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => {
      const isEditing = editingRow.value === row.original.id && editingField.value === 'name'

      if (isEditing) {
        return h('div', { class: 'flex gap-2' }, [
          h(InputText, {
            modelValue: row.original.name,
            autoFocus: true,
            onKeyup: (e) => {
              if (e.key === 'Enter') {
                saveEdit(row.original.id, 'name', e.target.value)
              } else if (e.key === 'Escape') {
                cancelEdit()
              }
            }
          }),
          h(Button, {
            size: 'xs',
            onClick: () => saveEdit(row.original.id, 'name', row.original.name)
          }, '✓'),
          h(Button, {
            size: 'xs',
            variant: 'ghost',
            onClick: cancelEdit
          }, '✕')
        ])
      }

      return h('div', {
        class: 'cursor-pointer hover:bg-gray-50 p-1 rounded',
        onClick: () => startEdit(row.original.id, 'name')
      }, row.original.name)
    }
  }
])
</script>
```

### Export and Import

#### Data Export
```vue
<template>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">Users</h1>

    <div class="flex gap-2">
      <Button variant="outline" @click="exportData('csv')">
        Export CSV
      </Button>
      <Button variant="outline" @click="exportData('excel')">
        Export Excel
      </Button>
      <Button variant="outline" @click="exportData('pdf')">
        Export PDF
      </Button>
    </div>
  </div>

  <Table :options="tableOptions" />
</template>

<script setup lang="ts">
const exportData = async (format: 'csv' | 'excel' | 'pdf') => {
  try {
    const response = await $fetch(`/api/users/export`, {
      method: 'POST',
      body: {
        format,
        filters: store.fetch.options.filters,
        columns: columns.value.map(col => col.accessorKey)
      }
    })

    // Download file
    const blob = new Blob([response], {
      type: format === 'csv' ? 'text/csv' : 'application/octet-stream'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `users.${format}`
    a.click()
    URL.revokeObjectURL(url)

    notification.success({ title: `Data exported as ${format.toUpperCase()}` })
  } catch (error) {
    notification.error({ title: 'Export failed' })
  }
}
</script>
```

### Table Configuration Options

```ts
interface ITableOptions {
  // Data
  rawData: T[]
  status: IStatus
  columns: TableColumn<T>[]

  // Pagination
  isHidePagination?: boolean
  pageOptions?: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
  }

  // Search and Filtering
  isEnabledSearch?: boolean
  searchPlaceholder?: string
  searchDebounce?: number
  searchFields?: string[]
  minSearchLength?: number

  // Display Options
  isHideCaption?: boolean
  emptyMessage?: string
  loadingMessage?: string

  // Behavior
  isRouteChange?: boolean // Sync with URL parameters
  sortable?: boolean
  selectable?: boolean
  selectMode?: 'single' | 'multiple'

  // Performance
  virtualScrolling?: boolean
  itemHeight?: number
  overscan?: number

  // Styling
  striped?: boolean
  bordered?: boolean
  compact?: boolean
  stickyHeader?: boolean
}
```

## Events and Interactions

### Table Events
```vue
<template>
  <Table
    :options="tableOptions"
    @pageChange="handlePageChange"
    @search="handleSearch"
    @sort="handleSort"
    @select="handleSelection"
    @rowClick="handleRowClick"
    @rowDoubleClick="handleRowDoubleClick"
  />
</template>

<script setup lang="ts">
const handlePageChange = (page: number) => {
  console.log('Page changed to:', page)
  store.fetchPageChange(page)
}

const handleSearch = (query: string) => {
  console.log('Search query:', query)
  store.fetchSearch(query)
}

const handleSort = (column: string, direction: 'asc' | 'desc') => {
  console.log('Sort:', column, direction)
  store.fetchSort(column, direction)
}

const handleSelection = (selectedRows: any[]) => {
  console.log('Selected rows:', selectedRows)
}

const handleRowClick = (row: any) => {
  console.log('Row clicked:', row)
  // Navigate to detail view
  navigateTo(`/users/${row.id}`)
}

const handleRowDoubleClick = (row: any) => {
  console.log('Row double-clicked:', row)
  // Open edit modal
  openEditModal(row)
}
</script>
```

## Theming and Customization

### Global Theme Configuration
```ts
// nuxt.config.ts
export default defineNuxtConfig({
  appConfig: {
    ui: {
      table: {
        wrapper: 'relative overflow-auto',
        base: 'min-w-full table-auto text-sm',
        thead: 'bg-gray-50 dark:bg-gray-800',
        tbody: 'bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700',
        tr: {
          base: 'hover:bg-gray-50 dark:hover:bg-gray-800/50',
          selected: 'bg-blue-50 dark:bg-blue-900/20',
          active: 'bg-gray-100 dark:bg-gray-800'
        },
        th: {
          base: 'px-4 py-3 text-left font-medium text-gray-900 dark:text-white',
          padding: 'px-4 py-3',
          color: 'text-gray-900 dark:text-white',
          font: 'font-medium',
          size: 'text-sm'
        },
        td: {
          base: 'px-4 py-3 text-gray-700 dark:text-gray-300',
          padding: 'px-4 py-3',
          color: 'text-gray-700 dark:text-gray-300',
          size: 'text-sm'
        }
      }
    }
  }
})
```

### Custom Column Styling
```ts
const columns = [
  {
    accessorKey: 'status',
    header: 'Status',
    meta: {
      class: {
        th: 'text-center bg-blue-50',
        td: 'text-center font-medium'
      }
    }
  }
]
```

### Conditional Row Styling
```ts
const tableOptions = useTable({
  repo: store,
  options: {
    rowClass: (row) => {
      if (row.status === 'inactive') return 'opacity-50'
      if (row.priority === 'high') return 'bg-red-50 border-l-4 border-red-500'
      return ''
    }
  },
  columns: () => columns.value
})
```

## Best Practices

### 1. Performance
- Use virtual scrolling for tables with 1000+ rows
- Implement server-side pagination for large datasets
- Debounce search inputs to reduce API calls
- Use lazy loading for images in table cells
- Consider using `useTableSimple` for static data

### 2. User Experience
- Provide clear loading states during data fetching
- Show meaningful empty states with actionable content
- Implement proper error handling with retry options
- Use skeleton loading for better perceived performance
- Ensure tables are responsive on mobile devices

### 3. Accessibility
- Use semantic HTML table elements
- Provide proper ARIA labels for screen readers
- Ensure keyboard navigation works correctly
- Use sufficient color contrast for text and backgrounds
- Test with screen readers and keyboard-only navigation

### 4. Data Management
- Validate data before displaying in tables
- Handle null/undefined values gracefully
- Use appropriate column types for data formatting
- Implement proper error boundaries for custom components
- Cache data when appropriate to reduce API calls

### 5. Maintainability
- Keep column definitions in separate files for large tables
- Use TypeScript interfaces for type safety
- Document custom cell renderers and their props
- Follow consistent naming conventions
- Write unit tests for custom table logic

## Troubleshooting

### Common Issues

**Table not updating when data changes?**
- Ensure your data store is reactive
- Check that the `repo` reference is stable
- Verify that data transformations are working correctly

**Search not working?**
- Verify `isEnabledSearch: true` in table options
- Check that your data loader supports search functionality
- Ensure search fields are properly configured

**Pagination not showing?**
- Ensure `isHidePagination: false` (default)
- Verify your data includes pagination metadata
- Check that `pageOptions` contains valid data

**Custom columns not rendering?**
- Verify that cell functions return valid VNodes
- Ensure component imports are available in scope
- Check for JavaScript errors in the browser console

**Performance issues with large tables?**
- Enable virtual scrolling for datasets > 1000 rows
- Use server-side pagination instead of loading all data
- Optimize custom cell renderers to avoid heavy computations
- Consider using `memo` for expensive custom components

**Mobile responsiveness issues?**
- Implement horizontal scrolling for wide tables
- Consider using card layouts for mobile devices
- Test on actual mobile devices, not just browser dev tools
- Use appropriate breakpoints for responsive design

### Getting Help

- Check the [Table component documentation](./components/Table.md) for detailed API reference
- Review [playground examples](../playground/pages/table.vue) for working implementations
- Consult the [data loading guide](./data-loading.md) for advanced patterns
- See [performance optimization guide](./performance.md) for large dataset handling

## Related Documentation

- [Table Component](./components/Table.md) - Component API reference
- [Data Loading](./data-loading.md) - usePageLoader, useListLoader patterns
- [Column Components](./components/) - ColumnText, ColumnNumber, etc.
- [Theming Guide](./theming.md) - Customizing table appearance
- [Performance Guide](./performance.md) - Optimization techniques
```
```
