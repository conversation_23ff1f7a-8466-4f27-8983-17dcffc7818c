<template>
  <div class="space-y-8">
    <Confidential
      :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.BIDDING]"
      :project-id="projectId"
      :tab-label="PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIDDING]"
    />
    <PmoCardCollapsible no-underline>
      <CheckList
        :tab="PROJECT_TAB_TYPE.BIDDING"
        :project-id="projectId"
      />
    </PmoCardCollapsible>
    <BiddingInfo
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.BIDDING"
    />
    <ContractInfo
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.BIDDING"
    />
    <BindbondInfo
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.BIDDING"
    />
    <LgInfo
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.BIDDING"
    />
    <PmoCardCollapsible
      title="เอกสารเทมเพลต"
      subtitle="Document Template"
    >
      <FileTemplate :tab="PROJECT_TAB_TYPE.BIDDING" />
    </PmoCardCollapsible>
    <GroupTemplateFile
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.BIDDING"
    />
    <PmoCardCollapsible
      title="ข้อมูลอัปเดต Bidding"
      sub-title="Comment"
    >
      <Comment
        :project-id="projectId"
        :channel="PROJECT_TAB_TYPE.BIDDING"
      />
    </PmoCardCollapsible>
    <Remark
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.BIDDING"
    />
  </div>
</template>

<script lang="ts" setup>
import CheckList from '~/container/Pmo/CheckList.vue'
import Confidential from '../Confidential.vue'
import Comment from '../Comment.vue'
import Remark from '../Remark.vue'
import BiddingInfo from '../BiddingInfo.vue'
import ContractInfo from './ContractInfo.vue'
import BindbondInfo from './BindbondInfo.vue'
import FileTemplate from '../FileTemplate.vue'
import LgInfo from './LgInfo.vue'
import { PROJECT_TAB_KEY, PROJECT_TAB_TYPE } from '~/constants/projectTab'

defineProps<{ projectId: string }>()
</script>
