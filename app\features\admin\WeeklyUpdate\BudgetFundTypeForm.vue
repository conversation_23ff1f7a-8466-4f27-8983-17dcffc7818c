<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="แก้ไข ประเภทงบ"
    description="fund type info."
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <input
          type="submit"
          hidden
        />
      </form>
    </template>
    <template #footer>
      <div class="flex w-full justify-end gap-3">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ยกเลิก
        </Button>
        <Button
          :loading="status().isLoading"
          :disabled="!form.meta.value.dirty"
          @click="onSubmit"
        >
          บันทึก
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: IBudgetInfo | null
  status: () => IStatus
  onSubmit: (values: IBudgetInfo) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    fund_type: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ประเภทงบ',
      name: 'fund_type',
      placeholder: 'กรุณาเลือกประเภทงบ',
      required: true,
      options: [
        {
          label: 'งบประจำปี',
          value: 'งบประจำปี',
        },
        {
          label: 'งบกลาง',
          value: 'งบกลาง',
        },
        {
          label: 'อื่น ๆ',
          value: 'อื่น ๆ',
        },
      ],
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as IBudgetInfo)
})
</script>
