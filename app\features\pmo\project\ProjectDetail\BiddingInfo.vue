<template>
  <PmoCardCollapsible
    title="ประมูลงาน"
    subtitle="Bidding"
  >
    <Loader :loading="bidding.status.value.isLoading">
      <InfoEditCard
        :form="form"
        :form-fields="formFields"
        :have-history="bidding.data.value"
        :tab="tab"
        @save="onSave"
        @openHistory="onOpenHistory"
      />
    </Loader>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import InfoEditCard from '~/container/Pmo/InfoEditCard.vue'
import { useProjectBiddingLoader, useProjectCreateBiddingLoader } from '~/loaders/pmo/project'
import BiddingInfoHistory from './BiddingInfoHistory.vue'
import { format } from 'date-fns'

const props = defineProps<{ projectId: string
  tab: string }>()

const dialog = useDialog()
const overlay = useOverlay()
const bidding = useProjectBiddingLoader(props.projectId as string)
const createBidding = useProjectCreateBiddingLoader()
const historyModal = overlay.create(BiddingInfoHistory)
const noti = useNotification()

const onSave = (v: any) => {
  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังส่งข้อมูล...',
  })

  if (props.tab !== PROJECT_TAB_TYPE.SALES) {
    createBidding.run({
      data: {
        ...v,
        tender_date: v.tender_date ? format(v.tender_date, 'yyyy-MM-dd') : undefined,
        announce_date: v.announce_date ? format(v.announce_date, 'yyyy-MM-dd') : undefined,
      },
      urlBind: {
        project_id: props.projectId,
      },
    })
  } else {
  // สำหรับ SALES ส่งตามปกติ
    createBidding.run({
      data: {
        ...v,
        tender_date: bidding.data.value?.tender_date ? format(bidding.data.value?.tender_date, 'yyyy-MM-dd') : undefined,
        announce_date: bidding.data.value?.announce_date ? format(bidding.data.value?.announce_date, 'yyyy-MM-dd') : undefined,
      },
      urlBind: {
        project_id: props.projectId,
      },
    })
  }
}

const form = useForm({
  validationSchema: computed(() => {
    const rules: Record<string, any> = {
      bidding_type: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      bidding_value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
      tender_entity: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),

    }

    // ถ้าไม่ใช่ SALES → เพิ่ม field เพิ่มเติม
    if (props.tab !== PROJECT_TAB_TYPE.SALES) {
      Object.assign(rules, {
        tender_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันยื่นเสนอราคา')), ''),
        announce_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันที่ประกาศ')), ''),
      })
    }

    return toTypedSchema(v.object(rules))
  }),
  keepValuesOnUnmount: true,
})

const formFields = createFormFields((): any => {
  const base = [
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'ประเภทจัดซื้อจัดจ้าง',
        name: 'bidding_type',
        placeholder: 'กรุณากรอกประเภทจัดซื้อจัดจ้าง',
        required: true,
      },
    },
    {
      type: INPUT_TYPES.NUMBER,
      props: {
        label: 'มูลค่าประมูลโครงการ',
        name: 'bidding_value',
        placeholder: 'กรุณากรอกมูลค่าประมูลโครงการ',
        required: true,
        formatOptions: {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        },
        step: 0.01,
      },
    },
    {
      type: INPUT_TYPES.TEXT,
      props: {
        label: 'ชื่อใช้เข้างาน',
        name: 'tender_entity',
        placeholder: 'กรุณากรอกคู่สัญญา',
        required: true,
      },
    },
  ]

  const extra = [
    {
      type: INPUT_TYPES.DATE,
      props: {
        label: 'วันยื่นเสนอราคา',
        name: 'tender_date',
        placeholder: 'กรุณากรอกวันยื่นเสนอราคา',
        required: true,
      },
    },
    {
      type: INPUT_TYPES.DATE,
      class: 'col-span-2',
      props: {
        label: 'วันที่ประกาศ',
        name: 'announce_date',
        placeholder: 'กรุณากรอกวันที่ประกาศ',
        required: true,
      },
    },
  ]

  return props.tab === PROJECT_TAB_TYPE.SALES ? base : [...base, ...extra]
})

onMounted(() => {
  bidding.run()
})

const onOpenHistory = () => {
  historyModal.open({
    projectId: props.projectId,
    tab: props.tab,
  })
}

useWatchTrue(() => bidding.status.value.isSuccess, async () => {
  form.setValues({
    ...bidding.data.value,
  })
})

useWatchTrue(() => createBidding.status.value.isSuccess, async () => {
  dialog.close()
  noti.success({
    title: 'แก้ไขประมูลงาน สำเร็จ',
  })

  bidding.run()
})

useWatchTrue(() => createBidding.status.value.isError, async () => {
  dialog.close()
  noti.error({
    title: 'แก้ไขประมูลงาน ไม่สำเร็จ',
    description: StringHelper.getError(createBidding.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขประมูลงาน กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
