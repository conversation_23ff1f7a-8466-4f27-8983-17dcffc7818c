<template>
  <div class="mb-6 flex flex-col gap-4">
    <Form @submit="onSubmit">
      <FormFields
        :options="formField"
      />
      <div class="mt-4 flex items-center">
        <Button
          label="Search"
          size="xl"
          class="mr-2 w-fit"
          type="submit"
          @click="onSubmit"
        />
        <Button
          v-if="checkin.fetch.items && checkin.fetch.items.length > 0"
          label="Export CSV"
          size="xl"
          class="w-fit"
          variant="outline"
          icon="proicons:add-square-multiple"
          color="neutral"
          @click="exportToExcel"
        />
      </div>
    </Form>

    <div class="grid">
      <Table
        v-if="!checkin.fetch.status.isLoading"
        :options="tableOptions"
        @pageChange="checkin.fetchPage"
        @search="checkin.fetchSearch"
      >
        <template #type-cell="{ row }">
          <div class="flex flex-col items-start gap-1">
            <div
              v-for="(item, index) in row.original.original_items"
              :key="index"
              class="flex items-center gap-1"
            >
              <img
                :src="useGetIconCheckin(item)"
                alt="icon"
                class="size-6"
              />
              {{ getLabel(item) }}
              <div v-if="item.type?.endsWith('LEAVE')">
                ({{ item.period?.startsWith('HALF') ? 'Half Day' : 'Full Day' }})
              </div>
              <div v-else-if="item.type === CHECKIN.ONSITE">
                ({{ item.location }})
              </div>
            </div>
          </div>
        </template>
        <template #check-in-cell="{ row }">
          <Badge
            v-bind="getCheckinBadge(getCheckinStatus(row.original.original_items?.at(0)))"
            variant="subtle"
          />
        </template>
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { CHECKIN } from '~/constants/checkin'
import { useUserPageLoader } from '~/loaders/admin/user'
import { useAdminCheckInsPageLoader } from '~/loaders/admin/checkin'
import { useCheckInItems } from '~/composables/useCheckInItems'
import { useCheckinUtils } from '~/composables/useCheckinUtils'
import {
  format,
  startOfMonth,
  endOfMonth,
} from 'date-fns'

const noti = useNotification()
const currentDate = ref(new Date())
const checkin = useAdminCheckInsPageLoader()
const user = useUserPageLoader()
const checkitems = useCheckInItems(() => checkin.fetch.items)
const {
  getCheckinStatus, getLabel, useGetIconCheckin,
} = useCheckinUtils()

checkin.fetchSetLoading()
onMounted(() => {
  user.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      date: v.nullish(v.object({
        year: v.number(),
        month: v.number(),
      })),
      user_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกพนักงาน')), ''),
    }),
  ),
  initialValues: {
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },
})

const formField = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'user_id',
      label: 'Member',
      required: true,
      placeholder: 'Select Member',
      loading: user.fetch.status.isLoading,
      options: user.fetch.items?.map((item: IUser) => ({
        label: item.display_name,
        value: item.id,
        avatar: {
          src: item.avatar_url,
          alt: item.display_name,
        },
      })) || [],
      searchable: true,
    },
  },
])

const tableOptions = useTable<any>({
  repo: checkin,
  transformItems: () => checkitems.groupByTypeItems.value as any[],
  options: {
    isHidePagination: false,
  },
  columns: () => [
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.DATE_TIME,
    },
    {
      accessorKey: 'type',
      header: 'Check-in type',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'check-in',
      header: 'Check-in',
      type: COLUMN_TYPES.TEXT,
    },
  ],
})

const getCheckinBadge = (status: string) => {
  switch (status) {
    case 'On-time':
      return {
        color: 'success' as const,
        label: 'On-time',
      }
    case 'Late':
      return {
        color: 'warning' as const,
        label: 'Late',
      }
    case 'On-time (Edited)':
      return {
        color: 'success' as const,
        label: 'On-time (Edited)',
      }
    case 'Late (Edited)':
      return {
        color: 'warning' as const,
        label: 'Late (Edited)',
      }
    default:
      return {
        color: 'neutral' as const,
        label: 'Unknown',
      }
  }
}

const onSubmit = form.handleSubmit((values) => {
  checkin.fetchPage(1, '', {
    params: {
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      user_id: values.user_id || undefined,
    },
  })
})

const exportToExcel = () => {
  const header = ['Date', 'ClockIn type', 'ClockIn']

  const rows = checkin.fetch.items.map((item) => {
    const row: (string | number)[] = [format(item.date, 'yyyy-MM-dd'),
      item.type,
      getCheckinBadge(getCheckinStatus(item)).label]

    return row
  })

  const displayUser = user.fetch.items?.find((u) => u.id === form.values.user_id)?.display_name

  const filename = `report_clockin_${displayUser}_${currentDate.value.getMonth()}_${currentDate.value.getFullYear()}.xlsx`

  QuickExcel.export(
    header,
    rows,
    filename,
  )
}

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
}, {
  deep: true,
})
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
