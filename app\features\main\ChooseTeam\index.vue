<template>
  <div class="flex min-h-screen flex-col">
    <div class="min-h-full flex-1">
      <div class="relative mx-auto mt-[246px] max-w-md">
        <img
          src="/Isolation_mode.png"
          class="absolute top-[-246px] right-1/2 w-[310px] translate-x-1/2"
          alt="login"
        />
        <div class="bg-main-500 relative grid h-[50px] place-items-center rounded-t-xl">
          <img
            src="/logo-mini-white.png"
            class=""
            alt="logo-mini"
          />
        </div>
        <div class="relative rounded-b-xl bg-white p-6">
          <Form @submit="onSubmit">
            <FormFields
              :options="fieldsCloudServices"
            />
            <Separator class="my-5" />
            <div class="flex justify-end">
              <Button
                :loading="auth.updateMe.status.value.isLoading"
                :disabled="!form.meta.value.dirty"
                label="Save"
                type="submit"
              />
            </div>
          </Form>
        </div>
      </div>
    </div>
  </div>

  <div class="relative bg-[#353D59]">
    <img
      src="/cover/login.png"
      class="absolute -right-5 bottom-0 w-1/2 md:w-2/5"
      alt="login"
    />
    <div class="mx-auto flex w-full max-w-7xl px-6 py-6">
      <p class="text-gray-400">
        © Copyright 2025 Finema
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useTeamPageLoader } from '~/loaders/admin/team'
import { format } from 'date-fns'

const auth = useAuth()
const router = useRouter()
const team = useTeamPageLoader()
const noti = useNotification()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    display_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาระบุชื่อเล่นภาษาอังกฤษ')), ''),
    team_code: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกทีม')), ''),
  })),
  initialValues: auth.me.value,
})

team.fetchSetLoading()
onMounted(() => {
  team.fetchPage(1, '', {
    params: {
      limit: 100,
    },
  })
})

const fieldsCloudServices = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเล่นภาษาอังกฤษ (Display Name)',
      name: 'display_name',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'ทีม (Team)',
      placeholder: 'กรุณาเลือกทีม',
      name: 'team_code',
      required: true,
      loading: team.fetch.status.isLoading,
      options: ArrayHelper.toOptions(team.fetch.items, 'code', 'name'),
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  auth.updateMe.run({
    data: {
      ...values,
      joined_date: format(new Date(values.joined_date), 'yyyy-MM-dd'),
    },
  })
  // console.log(values)

  // router.push(routes.home.to)
})

useWatchTrue(() => auth.updateMe.status.value.isSuccess, () => {
  noti.success({
    title: 'อัพเดทข้อมูลสำเร็จ',
    description: 'ข้อมูลของคุณถูกอัพเดทเรียบร้อยแล้ว',
  })

  router.push(routes.home.to)
})
</script>
