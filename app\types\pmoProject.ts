export interface IPmoProject {
  id: string
  created_at: string
  updated_at: string
  name: string
  slug: string
  email: string
  tags: string[]
  status: string
  project_id: string
  created_by_id: string
  updated_by: IUser
  updated_by_id: string
  deleted_by_id: string | null
  project: IProject
  permission: ICollaborators
  budget_info: IBudgetInfo
  bidding_info: IBiddingInfo
  contract_info: IContractInfo
  bidbond_info: IBidbondInfo
  lg_info: ILgInfo
  remarks: IRemark[]
}

export interface ICommentItem {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  user_id: string
  channel: string
  detail: string
  is_client_flag: boolean
  parent_comment_id: string | null
  updated_by: IUser
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
  user: I<PERSON><PERSON>
  replies?: ICommentItem[]
}

export interface ICollaborators {
  id: string
  project_id: string
  user_id: string

  confidential_permission: PMO_PERMISSION
  confidential_main: boolean

  sales_permission: PMO_PERMISSION
  sales_main: boolean

  presales_permission: PMO_PERMISSION
  presales_main: boolean

  bidding_permission: PMO_PERMISSION
  bidding_main: boolean

  pmo_permission: PMO_PERMISSION
  pmo_main: boolean
  created_at: string
  updated_at: string
  created_by_id: string
  updated_by: IUser
  updated_by_id: string
  deleted_by_id: string | null

  user: IUser
}

export interface IBudgetInfo {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  fund_type: string
  project_value: number
  bidbond_value: number
  partner: string
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
}

export interface IBiddingInfo {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  bidding_type: string
  bidding_value: number
  tender_date: string
  tender_entity: string
  announce_date: string
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
}

export interface IContractInfo {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  contract_no: string
  value: number
  signing_date: string
  start_date: string
  end_date: string
  duration_day: number
  warranty_duration_day: number
  warranty_duration_year: number
  prime: string
  penalty_fee: number
  is_legalize_stamp: boolean
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
}

export interface IBidbondInfo {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  guarantee_asset: string
  bidbond_payer: string
  bidbond_value: number
  start_date: string
  end_date: string
  duration_month: number
  duration_year: number
  fee: number
  created_by_id: string
  updated_by_id: string
  updated_by?: IUser
  deleted_by_id: string | null
}

export interface ILgInfo {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  value: number
  start_date: string
  end_date: string
  fee: number
  interest: number
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
}

export interface IRemark {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  tab_key: string
  detail: string
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
}

export interface IChecklistItem {
  id: string
  created_at: string
  updated_at: string
  project_id: string
  tab_key: string
  detail: string
  is_checked: boolean
  assignee_id: string
  created_by_id: string
  updated_by_id: string
  deleted_by_id: string | null
  assignee: IUser
  created_by: IUser
  updated_by: IUser
}
