# FineWork Frontend - Architecture

## System Architecture

### Framework Stack
- **Frontend Framework**: Nuxt.js 4 with Vue 3
- **Language**: TypeScript
- **UI Components**: [@finema/core](app/app.config.ts) - Custom Finema design system
- **Styling**: Tailwind CSS with custom theme
- **Charts**: Chart.js with vue-chartjs integration
- **Package Manager**: Bun (preferred runtime)

### Project Structure

```
app/
├── constants/          # Application constants and enums
├── composables/        # Vue composables for shared logic
├── features/           # Feature-based organization
├── layouts/            # Layout components for different user types
├── pages/              # Nuxt.js pages following file-based routing
├── middleware/         # Route middleware for auth and common logic
├── types/              # TypeScript type definitions
├── helpers/            # Utility functions
├── loaders/            # Data loading utilities
└── assets/             # Static assets (CSS, images)

server/
├── api/                # Server-side API endpoints
│   ├── auth/           # Authentication endpoints
│   └── mock/           # Mock data endpoints
└── tsconfig.json       # Server TypeScript configuration

public/                 # Static public assets
```

## Source Code Paths

### Core Authentication System
- [`app/composables/useAuth.ts`](app/composables/useAuth.ts) - Main authentication composable
- [`app/composables/useRequestOptions.ts`](app/composables/useRequestOptions.ts) - HTTP request configuration
- [`app/middleware/auth.ts`](app/middleware/auth.ts) - Route authentication middleware
- [`app/middleware/guest.ts`](app/middleware/guest.ts) - Guest-only route middleware
- [`server/api/auth/callback.get.ts`](server/api/auth/callback.get.ts) - Slack OAuth callback handler
- [`server/api/auth/logout.ts`](server/api/auth/logout.ts) - Logout endpoint

### Feature Organization
- [`app/features/main/`](app/features/main/) - Core application features (Portal, Login, Profile)
- [`app/features/ClockIn/`](app/features/ClockIn/) - Employee check-in functionality
- [`app/features/admin/`](app/features/admin/) - Super admin features (Users, Holidays, Projects, SGAs, Teams)
- [`app/features/clockinAdmin/`](app/features/clockinAdmin/) - Clock-in admin dashboards and reports
- [`app/features/timesheetAdmin/`](app/features/timesheetAdmin/) - Timesheet admin reports

### Layout System
- [`app/layouts/main/`](app/layouts/main/) - Main user layout with navbar
- [`app/layouts/admin/`](app/layouts/admin/) - Super admin layout with sidebar
- [`app/layouts/clockin-admin/`](app/layouts/clockin-admin/) - Clock-in admin layout
- [`app/layouts/timesheet-admin/`](app/layouts/timesheet-admin/) - Timesheet admin layout

### Configuration & Constants
- [`app/constants/routes.ts`](app/constants/routes.ts) - Route definitions and navigation menus
- [`app/constants/checkin_types.ts`](app/constants/checkin_types.ts) - Check-in type definitions and labels
- [`app/constants/leave_types.ts`](app/constants/leave_types.ts) - Leave type definitions
- [`app/constants/site.ts`](app/constants/site.ts) - Site configuration
- [`app/types/user.ts`](app/types/user.ts) - User interface definitions

## Key Technical Decisions

### Authentication Flow
1. **Slack SSO Integration**: Users authenticate via Slack OAuth through [`useAuth().login()`](app/composables/useAuth.ts:36)
2. **Token Management**: JWT tokens stored in cookies with 1-year expiration
3. **Middleware Protection**: Routes protected by [`auth.ts`](app/middleware/auth.ts) middleware
4. **User Data**: User information fetched via [`/me`](app/composables/useAuth.ts:28) endpoint and stored in Pinia store

### API Integration
- **Backend API**: `finework-api.finema.dev` (production)
- **Mock API**: Local mock endpoints at `/api/mock/*` for development
- **Request Configuration**: Centralized in [`useRequestOptions()`](app/composables/useRequestOptions.ts) with auth headers

### State Management
- **Pinia Stores**: Used for user authentication state management
- **Composables**: Feature-specific logic encapsulated in Vue composables
- **Cookie State**: Authentication token persisted in HTTP-only cookies

## Design Patterns in Use

### Feature-Based Architecture
- Each major feature has its own directory under [`app/features/`](app/features/)
- Features are self-contained with their own components and logic
- Pages import and use feature components rather than implementing logic directly

### Role-Based Layout System
- Different layouts for different user types:
  - `main`: Regular users
  - `admin`: Super admin users
  - `clockin-admin`: Clock-in administrators
  - `timesheet-admin`: Timesheet administrators

### Middleware Chain
- [`common.ts`](app/middleware/common.ts): Common logic for all routes
- [`auth.ts`](app/middleware/auth.ts): Authentication requirement
- [`guest.ts`](app/middleware/guest.ts): Guest-only access (login page)

## Component Relationships

### Portal System
- [`app/pages/index.vue`](app/pages/index.vue) → [`app/features/main/Portal/index.vue`](app/features/main/Portal/index.vue)
- Portal displays available applications based on user role
- Applications include: Clock-in, Timesheet, PMO (user), and their admin variants

### Navigation Structure
- Main navigation defined in [`app/constants/routes.ts`](app/constants/routes.ts)
- Sidebar configurations for different admin types
- Thai language labels throughout the interface

## Critical Implementation Paths

### User Authentication Flow
```
Login → Slack OAuth → Callback → Token Storage → Route Protection → User Data Fetch
```

### Data Loading Pattern
```
Page → Feature Component → Composable → API Call → Data Display
```

### Admin Access Control
```
User Role Check → Layout Selection → Sidebar Menu → Feature Access
