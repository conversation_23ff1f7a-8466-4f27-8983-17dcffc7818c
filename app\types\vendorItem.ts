export interface IVendorItem {
  id: string // uuid
  project_id: string // uuid (FK -> pmo_projects.id)

  vendor_name: string
  item_name: string
  item_detail: string
  deliver_duration_day: number

  is_tor: boolean
  is_implementation: boolean
  is_training: boolean
  is_user_manual: boolean

  created_at: Date
  created_by_id: string
  updated_at: Date
  updated_by: IUser
  updated_by_id: string
  deleted_at: Date | null
  deleted_by_id: string | null
}
