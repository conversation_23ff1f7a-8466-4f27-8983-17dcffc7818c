import type { AxiosRequestConfig } from 'axios'
import type { IFieldProps, IFormFieldBase, INPUT_TYPES } from '../types'

export interface IUploadDropzoneAutoProps extends IFieldProps {
  // Upload Configuration
  requestOptions: Omit<AxiosRequestConfig, 'baseURL'> & { baseURL: string }
  uploadPathURL?: string
  bodyKey?: string // default = file
  responseURL?: string // default = url
  responsePath?: string // default = path
  responseName?: string // default = file_name
  responseSize?: string // default = size
  responseID?: string // default = id
  // File Validation
  accept?: string[] | string
  maxSize?: number // in kb

  // UI Labels
  selectFileLabel?: string // default = คลิกเพื่อเลือกไฟล์
  selectFileSubLabel?: string // default = หรือ ลากและวางที่นี่
  uploadingLabel?: string // default = กำลังอัพโหลด
  uploadFailedLabel?: string // default = อัพโหลดล้มเหลว, กรุณาลองอีกครั้ง
  retryLabel?: string // default = ลองอีกครั้ง
}

export type IUploadDropzoneAutoField = IFormFieldBase<
  INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
  IUploadDropzoneAutoProps,
  {
    change: (value: File | undefined) => void
    success: (res: any) => void
    delete: () => void
  }
>
