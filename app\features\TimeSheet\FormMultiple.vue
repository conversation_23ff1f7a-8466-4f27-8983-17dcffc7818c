<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="Add Multiple Timesheet"
    :ui="{
      content: 'max-w-7xl',
    }"
  >
    <template #body>
      <form
        class="space-y-4"
        @submit="onSubmit"
      >
        <div class="flex flex-wrap gap-4">
          <div class="w-full lg:max-w-[375px]">
            <div class="h-fit w-full flex-1 rounded-xl bg-[#EFF8FF] p-4">
              <h3 class="mb-3 font-bold">
                1. Select date(s)
              </h3>
              <FormField
                class="mt-4"
                :error="form.submitCount.value > 0 ?form.errors.value.date : ''"
              >
                <VueDatePicker
                  v-model="date"
                  :enable-time-picker="false"
                  multi-dates
                  inline
                  :max-date="new Date()"
                  auto-apply
                  :disabled-dates="markers.filter((item) => item.color !== '#eab308').map((item) => item.date)"
                  :markers="markers"
                  class="mx-auto"
                  :month-change-on-scroll="false"
                  @update-month-year="onUpdateMonthYear"
                />
              </FormField>
            </div>

            <Alert
              v-if="totalTiming > 100"
              title="Manpower เกิน 100%"
              description="กรุณาลดเปอร์เซ็นต์งานลงให้รวมกันไม่เกิน 100%"
              color="warning"
              class="mt-3"
            />
            <Card
              v-else
              class="mt-5 font-bold"
            >
              Man power ของคุณ: {{ totalTiming }}%
            </Card>
          </div>

          <div class="h-fit w-full space-y-3 rounded-xl bg-[#EFF8FF] p-4 lg:w-auto lg:flex-1">
            <h3 class="font-bold">
              2. Add task(s)
            </h3>
            <div
              v-for="(task, index) in form.values.timesheet"
              :key="index"
              class="rounded-xl bg-white p-4"
            >
              <div class="mb-4 flex items-center justify-between">
                <div class="text-lg font-bold">
                  Add a new task
                </div>
                <Button
                  v-if="form.values.timesheet && form.values.timesheet.length > 1"
                  icon="prime:trash"
                  variant="outline"
                  color="error"
                  @click="removeTask(index)"
                />
              </div>
              <FormFields
                class="grid grid-cols-1 gap-x-4 md:grid-cols-3"
                :options="getTaskFormFields(index).value"
              />
            </div>
            <Button
              icon="uil:file-plus-alt"
              label="Add task"
              class="mt-3"
              @click="addTask"
            />
          </div>
        </div>
        <Separator class="mt-6 mb-5" />
        <div class="flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            label="Cancel"
            @click="emits('close', false)"
          />
          <Button
            type="submit"
            label="Save"
            :disabled="isLoading || totalTiming > 100"
            :loading="isLoading"
          />
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import VueDatePicker from '@vuepic/vue-datepicker'
import { useProjectsPageLoader } from '~/loaders/admin/project'
import { useSGAPageLoader } from '~/loaders/admin/sga'
import { format } from 'date-fns'
import { TRACKER_TYPE } from '~/constants/timesheet'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: any
  markers: any[]
  status: () => IStatus
  onSubmit: (values: any) => void
  onUpdateMonthYear: ({
    month, year,
  }: { month: number
    year: number }) => void
}>()

const date = ref([])
const isLoading = ref(false)
const projects = useProjectsPageLoader()
const sgaTypes = useSGAPageLoader()
const typeOptions = TRACKER_TYPE_OPTIONS
const leaveTypeOptions = LEAVE_OPTIONS

projects.fetchSetLoading()
sgaTypes.fetchSetLoading()

onMounted(() => {
  projects.fetchPage()
  sgaTypes.fetchPage()
})

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      timesheet: v.array(
        v.object({
          type: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกประเภทงาน')), ''),
          project_code: v.nullish(v.string(), ''),
          leave_type: v.nullish(v.string(), ''),
          sga_id: v.nullish(v.string(), ''),
          timing: v.pipe(v.number(), v.minValue(0, 'กรุณาระบุเวลา')),
          description: v.optional(v.pipe(v.string(), v.maxLength(256)), ''),
        }),
      ),
      date: v.optional(v.array(v.string())),
    }),
  ),
  initialValues: {
    timesheet: [
      {
        type: undefined,
        project_code: undefined,
        leave_type: undefined,
        sga_id: undefined,
        timing: 0,
        description: undefined,
      },
    ],
  },
})

const projectOptions = computed(() => projects.fetch.items?.map((p: any) => ({
  value: p.code,
  label: p.name,
})) || [])

const sgaTypeOptions = computed(() => sgaTypes.fetch.items?.map((s: any) => ({
  value: s.id,
  label: s.name,
})) || [])

const isTodayOrPast = computed(() => {
  if (!date.value || date.value.length === 0) return true

  const today = new Date()

  today.setHours(0, 0, 0, 0)

  return date.value.every((d: Date) => {
    const date = new Date(d)

    date.setHours(0, 0, 0, 0)

    return date <= today
  })
})

const getTaskFormFields = (index: number) => {
  const task = form.values.timesheet?.[index]

  return createFormFields(() => [
    {
      type: INPUT_TYPES.SELECT,
      props: {
        name: `timesheet.${index}.type`,
        placeholder: 'Select type of work',
        label: 'Work Type',
        options: isTodayOrPast.value
          ? typeOptions
          : typeOptions.filter((t) => t.value === TRACKER_TYPE.LEAVE),
      },
    },

    {
      type: INPUT_TYPES.SELECT,
      isHide: ![TRACKER_TYPE.PROJECT, TRACKER_TYPE.OT].includes(task?.type as TRACKER_TYPE),
      props: {
        name: `timesheet.${index}.project_code`,
        placeholder: 'Select Project',
        label: 'Project',
        required: [TRACKER_TYPE.PROJECT, TRACKER_TYPE.OT].includes(task?.type as TRACKER_TYPE),
        options: projectOptions.value,
      },
    },

    {
      type: INPUT_TYPES.SELECT,
      isHide: task?.type !== TRACKER_TYPE.LEAVE,
      props: {
        name: `timesheet.${index}.leave_type`,
        placeholder: 'Select Leave Type',
        label: 'Leave Type',
        required: task?.type === TRACKER_TYPE.LEAVE,
        options: leaveTypeOptions,
      },
    },
    {
      type: INPUT_TYPES.SELECT,
      isHide: task?.type !== TRACKER_TYPE.SGA,
      props: {
        name: `timesheet.${index}.sga_id`,
        placeholder: 'Select SGA Type',
        label: 'SGA Type',
        required: task?.type === TRACKER_TYPE.SGA,
        options: sgaTypeOptions.value,
      },
    },
    {
      type: INPUT_TYPES.NUMBER,
      isHide: task?.leave_type === LEAVE_LABEL[LEAVE_TYPE.BIRTHDAY],
      props: {
        name: `timesheet.${index}.timing`,
        placeholder: 'Enter man power (%)',
        label: 'Manpower %',
        min: 0,
        max: 100,
      },
    },
    {
      type: INPUT_TYPES.TEXTAREA,
      class: 'md:col-span-3',
      props: {
        name: `timesheet.${index}.description`,
        placeholder: 'Enter a task description',
        label: 'Description',
      },
    },
  ])
}

onMounted(() => {
  watch(
    () => form.values.timesheet?.map((task) => task.type) || [],
    (newTypes, oldTypes) => {
      newTypes.forEach((type, index) => {
        if (type !== oldTypes[index]) {
          form.setFieldValue(`timesheet.${index}.project` as any, undefined)
          form.setFieldValue(`timesheet.${index}.leave` as any, undefined)
          form.setFieldValue(`timesheet.${index}.sga` as any, undefined)
          form.setFieldValue(`timesheet.${index}.timing` as any, 0)
          form.setFieldValue(`timesheet.${index}.description` as any, undefined)
        }
      })
    },
    {
      deep: true,
    },
  )
})

const totalTiming = computed(() => {
  return form.values.timesheet?.reduce((sum, task) => {
    return sum + Number(task.timing || 0)
  }, 0) || 0
})

const onSubmit = form.handleSubmit((values) => {
  if (date.value.length === 0) {
    form.setFieldError('date', 'กรุณาเลือกวันที่')

    return
  }

  const formatted = {
    items: date.value
      .map((d) =>
        values.timesheet.map((t) => {
          const cleaned = Object.entries(t).reduce((acc, [key, val]) => {
            if (val !== '') {
              acc[key] = val
            }

            return acc
          }, {} as Record<string, any>)

          return {
            ...cleaned,
            timing: t.timing / 100 * 8,
            date: format(new Date(d), 'yyyy-MM-dd'),
          }
        }),
      )
      .flat(),
  }

  props.onSubmit(formatted as any)
})

const addTask = () => {
  const newTask = {
    type: undefined,
    project_code: undefined,
    leave_type: undefined,
    sga_id: undefined,
    timing: 0,
    description: undefined,
  }

  const currentTasks = form.values.timesheet || []

  form.setFieldValue('timesheet', [...currentTasks, newTask])
}

const removeTask = (index: number) => {
  const currentTasks = form.values.timesheet || []
  const updatedTasks = currentTasks.filter((_, i) => i !== index)

  form.setFieldValue('timesheet', updatedTasks)
}

const onUpdateMonthYear = ({
  month, year,
}: { month: number
  year: number }) => {
  date.value = []
  props.onUpdateMonthYear({
    month,
    year,
  })
}
</script>

<style scoped>
.dp__flex_display{
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
