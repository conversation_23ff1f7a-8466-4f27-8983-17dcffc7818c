<template>
  <div class="overflow-x-auto rounded-xl border border-[#D0D5DD]">
    <div class="flex h-full">
      <div
        v-for="(day, index) in weekDays"
        :key="day.date"
        class="flex min-w-24 flex-1 cursor-pointer flex-col"
        :class="{
          '!cursor-not-allowed opacity-50': new Date(day.date) > new Date(),
        }"
        @click="() => {
          if (new Date(day.date) <= new Date()) {
            $emit('selectDate', day.date)
          }
        }"
      >
        <div
          class="border-b border-[#D0D5DD] p-4 text-right font-bold text-[#344054]"
          :class="{
            'bg-[#D1E9FF]': day.date === format(currentDate, 'yyyy-MM-dd'),
          }"
        >
          <div class="text-primary">
            {{ day.label }}
          </div>
          <div class="text-sm">
            {{ day.dayName }}
          </div>
        </div>

        <div
          class="h-full min-h-60 pt-2"
          :class="{
            'border-r border-[#D0D5DD]': index !== weekDays.length - 1,
            'bg-[#EFF8FF]': day.date === format(currentDate, 'yyyy-MM-dd'),
          }"
        >
          <div
            v-for="item in day.trackers"
            :key="item.id"
            class="
            mx-2 mb-2 overflow-y-scroll rounded-md border-t-8 px-2 py-1 text-sm
          "
            :class="getTrackerColorClass(day, item.type === TRACKER_TYPE.OT)"
          >
            <span class="flex items-center font-bold">
              <Icon
                name="heroicons-outline:clock"
                class="mr-1 size-4"
              />
              {{ item.timing }} hr.
            </span>
            <span class="text-xs">
              {{ TRACKER_TYPE_LABEL[item.type as TRACKER_TYPE] }}
              <br />
              {{ item.sga_name || item.project_name || item.leave_type }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { TRACKER_TYPE, TRACKER_TYPE_LABEL } from '~/constants/timesheet'
import { format } from 'date-fns'

defineEmits<{
  (e: 'selectDate', date: string): void
}>()

defineProps<{
  weekDays: any
  currentDate: Date
}>()

const windowWidth = ref(0)

const handleResize = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth
  }
}

onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

const getTotalTiming = (day: any): number => {
  return day.trackers.reduce((sum: number, item: any) => sum + item.timing, 0)
}

const getTrackerColorClass = (day: any, isOT: boolean): string => {
  const total = getTotalTiming(day)

  if (total > 8 && isOT) {
    return 'border-error-500 bg-error-100 text-error-700'
  } else if (total < 8) {
    return 'border-warning-500 bg-warning-100 text-warning-700'
  }

  return 'border-success-500 bg-success-50 text-success-700'
}
</script>
