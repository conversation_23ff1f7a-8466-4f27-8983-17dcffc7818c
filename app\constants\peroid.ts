export enum PERIOD {
  HALF_MORNING = 'HALF_MORNING',
  HALF_AFTERNOON = 'HALF_AFTERNOON',
  FULL_DAY = 'FULL_DAY',
  MANY_DAYS = 'MANY_DAYS',
}

export const PERIOD_LABEL = {
  [PERIOD.HALF_MORNING]: 'Half Morning',
  [PERIOD.HALF_AFTERNOON]: 'Half Afternoon',
  [PERIOD.FULL_DAY]: 'Full Day',
  [PERIOD.MANY_DAYS]: 'Many Days',
}

export const PERIOD_TYPES = [
  PERIOD.HALF_MORNING,
  PERIOD.HALF_AFTERNOON,
  PERIOD.FULL_DAY,
  PERIOD.MANY_DAYS,
] as const

export const PERIOD_ORDER: Record<PERIOD, number> = {
  [PERIOD.HALF_MORNING]: 0,
  [PERIOD.HALF_AFTERNOON]: 1,
  [PERIOD.FULL_DAY]: 2,
  [PERIOD.MANY_DAYS]: 3,
}

export const PERIOD_OPTIONS = PERIOD_TYPES.map((type) => ({
  label: PERIOD_LABEL[type],
  value: type,
}))
