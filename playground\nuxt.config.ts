export default defineNuxtConfig({
  modules: ['../src/module'],
  imports: {
    dirs: ['loaders'],
  },
  devtools: {
    enabled: true,
  },
  css: ['~/assets/css/main.css'],
  compatibilityDate: '2025-05-15',
  vite: {
    optimizeDeps: {
      include: [
        '@tiptap/vue-3',
        '@tiptap/starter-kit',
        '@tiptap/extension-underline',
        '@tiptap/extension-text-align',
        '@tiptap/extension-link',
        '@tiptap/extension-image',
        '@tiptap/extension-youtube',
        'date-fns',
        'date-fns/locale',
        'date-fns-tz',
        'url-join',
        'lodash-es',
        '@vuepic/vue-datepicker',
        '@vee-validate/valibot',
        'valibot',
        '@wdns/vue-code-block',
        '@vueuse/core',
        '@vueuse/components',
        '@vue/devtools-core',
        '@vue/devtools-kit',
        'maska/vue',
        '@nuxt/ui/locale',
      ],
    },
  },
  core: {},
})
