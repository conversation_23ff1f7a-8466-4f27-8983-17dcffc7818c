import type { AxiosRequestConfig } from 'axios'

export const useRequestOptions = () => {
  const config = useRuntimeConfig()

  const getMock = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPIMock || 'http://localhost:3000/api/mock',
    }
  }

  const getFile = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: 'https://finework-api.finema.dev/',
      headers: {
        Authorization: `Bearer 5bcc365e52b227b68fc0742827e802c556b83342df078bb0473400a4f751e741`,
      },
    }
  }

  const getDefault = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPI,
    }
  }

  return {
    getDefault,
    getMock,
    getFile,
  }
}
