import type {
  IFieldProps,
  IFormFieldBase,
  INPUT_TYPES,
} from '#core/components/Form/types'

export interface IMonthFieldProps extends IFieldProps {
  clearIcon?: string
  minDate?: Date | string
  maxDate?: Date | string
  teleport?: boolean | string | HTMLElement
}

export type IMonthField = IFormFieldBase<
  INPUT_TYPES.MONTH,
  IMonthFieldProps,
  {
    change: (value: string) => void
  }
>
