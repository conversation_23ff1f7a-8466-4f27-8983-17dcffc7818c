<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="Comment History"
    description="view change history"
    :ui="{
      content: 'max-w-2xl',
    }"
  >
    <template #body>
      <div class="space-y-5">
        <div
          v-for="value in comments.fetch.items"
          :key="value.detail"
          class="rounded-md bg-[#F2F4F7] p-2"
        >
          <AvatarProfile
            :updated-at="value.updated_at"
            :item="value.user"
            hide-team
          />
          <p class="whitespace-pre-line text-slate-700 dark:text-slate-200">
            {{ value.detail }}
          </p>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex w-full justify-end">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ปิด
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { usePmoCommentVersionByProjectIdPageLoader, usePmoProjectsPageLoader } from '~/loaders/pmo/project'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: ICommentItem

}>()

const project = usePmoProjectsPageLoader()

const comments = usePmoCommentVersionByProjectIdPageLoader(project.find.item?.id || '', props.values?.id || '')

onMounted(() => {
  comments.fetchPage(1, '', {
    params: {
      limit: 100,
    },
  })
})
</script>
