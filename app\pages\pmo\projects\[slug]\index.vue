<template>
  <NuxtLayout>
    <ProjectDetail />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import ProjectDetail from '~/features/pmo/project/ProjectDetail/index.vue'
import { usePmoProjectsPageLoader } from '~/loaders/pmo/project'

definePageMeta({
  layout: 'pmo-info',
  middleware: ['auth', 'permissions'],

  validate: async (route) => {
    const slug = route.params.slug as string
    const project = usePmoProjectsPageLoader()

    await project.findRun(slug)

    return !ParamHelper.isNotFoundError(project.find.status.errorData)
  },
  accessGuard: {
    permissions: routes.pmo.project.projectBySlug('slug').permissions,
  },
})

const project = usePmoProjectsPageLoader()
const slug = useRoute().params.slug as string

useSeoMeta({
  title: routes.pmo.project.projectBySlug(slug, project.find.item?.name).label,
})

useApp().definePage({
  title: project.find.item?.name,
  breadcrumbs: [routes.pmo.project.projects, routes.pmo.project.projectBySlug(slug, project.find.item?.name)],
})
</script>

<style scoped>
:deep(.last-tab-right > button:last-child) {
  margin-left: auto;
}
</style>
