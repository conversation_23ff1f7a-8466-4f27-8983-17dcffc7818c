<template>
  <div class="w-full rounded-lg border-[#EAECF0] bg-[#F9FAFB] p-5">
    <div class="flex justify-between">
      <div class="mb-3 flex items-center gap-2 font-bold">
        <img
          :src="iconImages?.[CHECKIN.BUSINESS_TRIP]"
          alt="icon"
          class="size-6"
        />
        Business Trip
      </div>
      <ButtonActionIcon
        size="md"
        icon="material-symbols:close-rounded"
        @click="$emit('close')"
      />
    </div>
    <FormFields
      class="grid gap-x-3 md:grid-cols-2"
      :options="formField"
    />
  </div>
</template>

<script lang="ts" setup>
import type { FormContext } from 'vee-validate'

defineEmits<{
  (e: 'close'): void
}>()

const {
  form,
} = defineProps<{
  form: FormContext<Partial<any>>
}>()

const formField = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'Select Business Trip location',
      name: 'location_business_trip_onsite',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'Leave period',
      name: 'business_period',
      required: true,
      options: (form.values.type.length > 1 || form.values.checkin?.length > 1
        ? PERIOD_OPTIONS.filter((opt) => opt.value !== PERIOD.FULL_DAY)
        : PERIOD_OPTIONS).filter((option) => option.value !== PERIOD.MANY_DAYS),
    },
  },
])
</script>
