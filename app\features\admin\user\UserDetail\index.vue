<template>
  <StatusBox
    :status="user.find.status"
    :data="user.find.item"
  >
    <div class="mb-8 flex justify-between">
      <div class="flex gap-6">
        <Avatar
          :src="user.find.item?.avatar_url || '/profile_mock.png'"
          class="size-[86px] rounded-full"
        />
        <div>
          <div class="text-3xl font-semibold">
            {{ user.find.item?.display_name }}
          </div>
          <div>
            {{ user.find.item?.full_name }}
          </div>
          <Badge
            color="warning"
            :label="user.find.item?.team?.name"
            variant="soft"
          />
        </div>
      </div>
      <div>
        <Button
          v-if="user.find.item?.is_active"
          variant="outline"
          color="neutral"
        >
          <div class="bg-success size-2 rounded-full" />{{ USER_STATUS_LABEL[userStatus.ACTIVE] }}
        </Button>
        <Button
          v-else
          variant="outline"
          color="neutral"
        >
          <div class="bg-error size-2 rounded-full" />{{ USER_STATUS_LABEL[userStatus.INACTIVE] }}
        </Button>
      </div>
    </div>
    <Card class="">
      <div class="flex flex-col md:flex-row">
        <div class="w-full flex-1 space-y-1 border-b border-r-[#EAECF0] border-b-[#EAECF0] pb-6 md:max-w-[200px] md:border-r md:border-b-0 md:px-4 md:pb-0 xl:max-w-[300px]">
          <div
            v-for="item in menuItems"
            :key="item.key"
            class="grid w-full cursor-pointer rounded-lg px-3 py-[10px]"
            :class="selected === item.key ? 'bg-[#F9FAFB]' : ''"
            @click="selected = item.key"
          >
            <div class="font-bold">
              {{ item.title }}
            </div>
            <div class="text-xs text-[#475467]">
              {{ item.subtitle }}
            </div>
          </div>
        </div>
        <div
          class="mt-6 flex-1 md:mt-0 md:ml-[30px]"
        >
          <div class="text-xl font-bold">
            {{ menuItems.find(item => item.key === selected)?.title }}
          </div>
          <div class="mb-6 text-[#475467]">
            {{ menuItems.find(item => item.key === selected)?.subtitle }}
          </div>
          <UserInfo
            v-if="selected === 'userInfo'"
            :profile="user.find.item!"
          />
          <AccountSetting
            v-else-if="selected === 'accountSetting'"
            :profile="user.find.item!"
          />
          <Permissions
            v-else-if="selected === 'permissions'"
            :profile="user.find.item!"
          />
        </div>
      </div>
    </Card>
  </StatusBox>
</template>

<script lang="ts" setup>
import AccountSetting from './AccountSetting.vue'
import UserInfo from './UserInfo.vue'
import Permissions from './Permissions.vue'
import { useUserPageLoader } from '~/loaders/admin/user'

const route = useRoute()
const id = route.params.id as string
const user = useUserPageLoader()
const selected = ref<string>('userInfo')

const menuItems = [
  {
    key: 'userInfo',
    title: 'ข้อมูลผู้ใช้งาน',
    subtitle: 'User Info.',
  },
  {
    key: 'permissions',
    title: 'สิทธิ์การใช้งาน',
    subtitle: 'Permissions',
  },
  {
    key: 'accountSetting',
    title: 'จัดการบัญชีผู้ใช้',
    subtitle: 'Account Setting',
  },
]

onMounted(() => {
  user.findRun(id)
})
</script>
