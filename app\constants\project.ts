export enum PROJECT_STATUS {
  DRAFT = 'DRAFT',
  TOR = 'TOR',
  BIDDING = 'BIDDING',
  PMO = 'PMO',
  WARRANTY = 'WARRANTY',
  CLOSED = 'CLOSED',
  CANCEL = 'CANCEL',
}

export const ProjectStatusColor: Record<PROJECT_STATUS, 'info' | 'neutral' | 'success' | 'warning' | 'error'> = {
  [PROJECT_STATUS.DRAFT]: 'info',
  [PROJECT_STATUS.TOR]: 'warning',
  [PROJECT_STATUS.BIDDING]: 'warning',
  [PROJECT_STATUS.PMO]: 'success',
  [PROJECT_STATUS.WARRANTY]: 'success',
  [PROJECT_STATUS.CLOSED]: 'error',
  [PROJECT_STATUS.CANCEL]: 'error',
}

export const PROJECT_STATUS_LABEL = {
  [PROJECT_STATUS.DRAFT]: 'Draft',
  [PROJECT_STATUS.TOR]: 'TOR',
  [PROJECT_STATUS.BIDDING]: 'Bidding',
  [PROJECT_STATUS.PMO]: 'PMO',
  [PROJECT_STATUS.WARRANTY]: 'Warranty',
  [PROJECT_STATUS.CLOSED]: 'Closed',
  [PROJECT_STATUS.CANCEL]: 'Cancel',
}

export const PROJECT_STATUS_OPTIONS = Object.entries(PROJECT_STATUS_LABEL).map(([value, label]) => ({
  value,
  label,
  chip: {
    color: ProjectStatusColor[value as PROJECT_STATUS],
  },
}))

export const statusColor: Record<string, string> = {
  info: 'border rounded-full border-blue-200 bg-blue-50   text-blue-700',
  warning: 'border rounded-full border-orange-200 bg-orange-50 text-orange-700',
  success: 'border rounded-full border-green-200 bg-green-50 text-green-700',
  error: 'border rounded-full border-red-200 bg-red-50 text-red-700',
}

export const headerStatusColor: Record<string, string> = {
  info: 'border  border-blue-200 bg-blue-50  text-blue-700',
  warning: 'border  border-orange-200 bg-orange-50 text-orange-700',
  success: 'border  border-green-200 bg-green-50 text-green-700',
  error: 'border  border-red-200 bg-red-50 text-red-700',
}

export const HeaderStatusColorClass = {
  [PROJECT_STATUS.DRAFT]: headerStatusColor.info,
  [PROJECT_STATUS.TOR]: headerStatusColor.warning,
  [PROJECT_STATUS.BIDDING]: headerStatusColor.warning,
  [PROJECT_STATUS.PMO]: headerStatusColor.success,
  [PROJECT_STATUS.WARRANTY]: headerStatusColor.success,
  [PROJECT_STATUS.CLOSED]: headerStatusColor.error,
  [PROJECT_STATUS.CANCEL]: headerStatusColor.error,
}

export const ProjectStatusColorClass = {
  [PROJECT_STATUS.DRAFT]: statusColor.info,
  [PROJECT_STATUS.TOR]: statusColor.warning,
  [PROJECT_STATUS.BIDDING]: statusColor.warning,
  [PROJECT_STATUS.PMO]: statusColor.success,
  [PROJECT_STATUS.WARRANTY]: statusColor.success,
  [PROJECT_STATUS.CLOSED]: statusColor.error,
  [PROJECT_STATUS.CANCEL]: statusColor.error,

}
