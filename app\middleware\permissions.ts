import { defineNuxtRouteMiddleware, useAuth, navigateTo, abortNavigation } from '#imports'

export interface AccessGuardOptions {
  permissions?: `${UserModule}:${Permission}`[]
  redirectTo?: string
}

export default defineNuxtRouteMiddleware(async (to, from) => {
  const options: AccessGuardOptions = to.meta.accessGuard || {}
  const auth = useAuth()

  if (
    options.permissions?.some((perm) => {
      const [module, permission] = perm.split(':') as [UserModule, Permission]

      return auth.hasPermission(module, permission)
    })
  ) {
    return
  }

  if (options.redirectTo) {
    return navigateTo(options.redirectTo)
  }

  return abortNavigation({
    statusCode: 403,
  })
})
