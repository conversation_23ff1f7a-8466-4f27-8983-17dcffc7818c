export const useCheckinUtils = () => {
  const getLabel = (data: ICheckinItem): string => {
    return GROUP_LABELS[data.type === CHECKIN.LEAVE ? data.leave_type || data.type : data.type] ?? data.type
  }

  const getCheckinStatus = (row: any) => {
    const date = new Date(row?.date)
    const created = new Date(row?.created_at)
    const firstCheckinAt = new Date(row?.first_checkin_at)

    firstCheckinAt.setSeconds(firstCheckinAt.getSeconds() + 10)

    const teamDateStr = row?.user?.team?.working_start_at || '09:00:00'
    const [h, m, s] = teamDateStr.split(':').map(Number)

    const teamDate = new Date()

    teamDate.setHours(h, m, s, 0)

    const isSameDay
      = date.getFullYear() === firstCheckinAt.getFullYear()
        && date.getMonth() === firstCheckinAt.getMonth()
        && date.getDate() === firstCheckinAt.getDate()

    if (date > firstCheckinAt) {
      return firstCheckinAt < created ? 'On-time (Edited)' : 'On-time'
    }

    if (!isSameDay) {
      return firstCheckinAt < created ? 'Late (Edited)' : 'Late'
    }

    const teamDatePlus1h = new Date(teamDate)

    teamDatePlus1h.setHours(teamDate.getHours() + 1)

    if (firstCheckinAt > teamDatePlus1h) {
      return firstCheckinAt < created ? 'Late (Edited)' : 'Late'
    }

    return firstCheckinAt < created ? 'On-time (Edited)' : 'On-time'
  }

  const useGetIconCheckin = (data: ICheckinItem): string => {
    return iconImages?.[data.type === CHECKIN.LEAVE ? data.leave_type || data.type : data.type] || ''
  }

  return {
    getLabel,
    getCheckinStatus,
    useGetIconCheckin,
  }
}
