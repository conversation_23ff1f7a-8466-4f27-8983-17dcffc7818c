<template>
  <div class="flex items-center gap-2">
    <Avatar
      v-if="item?.avatar_url"
      :src="item?.avatar_url"
      :class="avatarSizeClass"
    />
    <Avatar
      v-else
      :alt="item?.email"
      :class="avatarSizeClass"
    />
    <div class="truncate">
      <div :class="nameClass">
        {{ item?.display_name }}
      </div>
      <BadgeTeam
        v-if="item?.team && !hideTeam"
        :team="item?.team"
        :size="size"
        class="font-medium"
      />
      <div
        v-if="updatedAt"
        :class="timeClass"
      >
        {{ TimeHelper.getDateTimeFormTime(new Date(updatedAt)) }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  item: IUser
  updatedAt?: string
  size?: 'xl' | 'lg' | 'md' | 'sm'
  hideTeam?: boolean
}>()

const size = props.size || 'lg'

// Mapping ขนาด avatar และ text ตาม size
const avatarSizeClass = {
  xl: 'size-12',
  lg: 'size-10',
  md: 'size-8',
  sm: 'size-6',
}[size]

const nameClass = {
  xl: 'text-base font-medium',
  lg: 'text-sm font-medium',
  md: 'text-sm font-medium',
  sm: 'text-xs font-medium',
}[size]

const timeClass = {
  xl: 'text-sm text-slate-500 mt-1',
  lg: 'text-xs text-slate-500 mt-1',
  md: 'text-xs text-slate-500 mt-1',
  sm: 'text-[8px] text-slate-500 mt-1',
}[size]
</script>
