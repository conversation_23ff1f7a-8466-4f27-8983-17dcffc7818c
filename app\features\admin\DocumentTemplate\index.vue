<template>
  <div class="space-y-4">
    <Tabs
      v-model="activeTabIndex"
      color="neutral"
      variant="link"
      :items="items"
      class="w-full"
    />
    <div>
      <TeleportSafe to="#page-header">
        <Button
          trailing-icon="basil:plus-solid"
          @click="addDocument"
        >
          เพิ่มเอกสาร
        </Button>
      </TeleportSafe>
      <FormFields
        :form="form"
        :options="formFields"
        class="mb-4"
      />
      <Table
        :options="tableOptions"
        @pageChange="document.fetchPageChange"
        @search="document.fetchSearch"
      >
        <template #file-cell="{ row }">
          <FileTemplateBox
            class="border-none bg-gray-50"
            :url="row.original.sharepoint_url"
            extension="pdf"
            :name="row.original.name"
            readonly
            show-url
          />
        </template>
        <template #actions-cell="{ row }">
          <div class="flex justify-end">
            <ButtonActionIcon
              icon="mage:pen"
              color="neutral"
              @click="editDocument(row.original)"
            />
            <ButtonActionIcon
              icon="prime:trash"
              color="error"
              @click="onDelete(row.original)"
            />
          </div>
        </template>
        <template #by-cell="{ row }">
          <AvatarProfile
            :item="row.original.updated_by || row.original.created_by"
          />
        </template>
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TabsItem } from '@nuxt/ui'
import { watchDebounced } from '@vueuse/core'
import Form from './Form.vue'
import { useTemplateDocumentPageLoader } from '~/loaders/pmo/template'
import { PROJECT_TAB_LABEL, PROJECT_TAB_TYPE } from '~/constants/projectTab'

const document = useTemplateDocumentPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const activeTabIndex = ref<PROJECT_TAB_TYPE>(PROJECT_TAB_TYPE.SALES)

const items = ref<TabsItem[]>([
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.SALES],
    value: PROJECT_TAB_TYPE.SALES,
  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PRESALES],
    value: PROJECT_TAB_TYPE.PRESALES,

  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIDDING],
    value: PROJECT_TAB_TYPE.BIDDING,
  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.PMO],
    value: PROJECT_TAB_TYPE.PMO,
  },
  {
    label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.BIZCO],
    value: PROJECT_TAB_TYPE.BIZCO,
  },
])

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหา',
    },
  },
])

watchDebounced(form.values, (values) => {
  document.fetchSearch(values.q || '', {
    params: {
      tab_key: activeTabIndex.value,
    },
  })
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<ITemplateDocument>({
  repo: document,
  columns: () => [
    {
      accessorKey: 'file',
      header: 'เอกสาร',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'updated_at',
      header: 'แก้ไขล่าสุด',
      type: COLUMN_TYPES.DATE,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'by',
      header: 'โดย',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

document.fetchSetLoading()
onMounted(() => {
  document.fetchPage(1, '', {
    params: {
      tab_key: activeTabIndex.value,
    },
  })
})

const editDocument = (_document: ITemplateDocument) => {
  editModal.open({
    isEditing: true,
    values: _document,
    status: () => document.update.status,
    onSubmit: (values: ITemplateDocument) => {
      document.updateRun(_document.id, {
        data: values,
      })
    },
  })
}

const addDocument = () => {
  addModal.open({
    status: () => document.add.status,
    onSubmit: (values: ITemplateDocument) => {
      document.addRun({
        data: {
          ...values,
          tab_key: activeTabIndex.value,
        },
      })
    },
  })
}

const onDelete = (values: ITemplateDocument) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบเอกสาร "${values.name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    document.deleteRun(values.id)
  })
}

useWatchTrue(
  () => document.update.status.isSuccess,
  () => {
    editModal.close()
    document.fetchPage(1, '', {
      params: {
        tab_key: activeTabIndex.value,
      },
    })

    noti.success({
      title: 'แก้ไขเอกสารสำเร็จ',
      description: 'คุณได้แก้ไขเอกสารเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => document.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขเอกสารไม่สำเร็จ',
      description: StringHelper.getError(document.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขเอกสาร กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => document.delete.status.isSuccess,
  () => {
    dialog.close()
    document.fetchPage(1, '', {
      params: {
        tab_key: activeTabIndex.value,
      },
    })

    noti.success({
      title: 'ลบเอกสารสำเร็จ',
      description: 'คุณได้ลบเอกสารเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => document.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบเอกสารไม่สำเร็จ',
      description: StringHelper.getError(document.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบเอกสาร กรุณาลองใหม่อีกครั้ง'),

    })
  },
)

useWatchTrue(
  () => document.add.status.isSuccess,
  () => {
    addModal.close()
    document.fetchPage(1, '', {
      params: {
        tab_key: activeTabIndex.value,
      },
    })

    noti.success({
      title: 'เพิ่มเอกสารสำเร็จ',
      description: 'คุณได้เพิ่มเอกสารเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => document.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มเอกสารไม่สำเร็จ',
      description: StringHelper.getError(document.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มเอกสาร กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

watch(() => activeTabIndex.value, () => {
  document.fetchPage(1, '', {
    params: {
      tab_key: activeTabIndex.value,
    },
  })
})
</script>
