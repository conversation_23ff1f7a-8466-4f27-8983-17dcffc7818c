<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขเอกสารต้นแบบ' : 'จัดการเอกสารต้นแบบ'"
    description="Document Template"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: ITemplateDocument | null
  status: () => IStatus
  onSubmit: (values: ITemplateDocument) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    sharepoint_url: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเอกสาร (Document Name)',
      name: 'name',
      placeholder: 'ระบุชื่อเอกสาร',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'SharePoint Link',
      name: 'sharepoint_url',
      placeholder: 'https://finemaco.sharepoint.com/:f:/s/finema/Eus6EmWdR2hNijaIHHd5U2sBMBobVe3TN2j4eI...',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as ITemplateDocument)
})
</script>
