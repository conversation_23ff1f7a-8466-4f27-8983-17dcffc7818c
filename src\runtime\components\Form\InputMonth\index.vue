<template>
  <FieldWrapper v-bind="wrapperProps">
    <Datepicker
      v-model="value"
      :teleport="teleport"
      :disabled="wrapperProps.disabled"
      :cancel-text="appConfig.core?.locale === 'th' ? 'ยกเลิก' : 'Cancel'"
      :select-text="appConfig.core?.locale === 'th' ? 'ตกลง' : 'Select'"
      :locale="appConfig.core?.locale"
      :format="appConfig.core?.month_format"
      month-picker
      :placeholder="wrapperProps.placeholder"
      :min-date="minDate"
      :max-date="maxDate"
      :required="wrapperProps.required"
      :clearable="true"
      :always-clearable="true"
    >
      <template
        v-if="appConfig.core?.is_thai_year"
        #year="{ year }"
      >
        {{ year + 543 }}
      </template>
      <template
        v-if="appConfig.core?.is_thai_year"
        #year-overlay-value="{ value }"
      >
        {{ value + 543 }}
      </template>
      <template #dp-input>
        <Input
          :trailing-icon="!wrapperProps.required && value
            ? undefined
            : 'i-heroicons-calendar-days'"
          type="text"
          :disabled="wrapperProps.disabled"
          :model-value="formatDisplay(value)"
          :placeholder="wrapperProps.placeholder"
          :readonly="true"
          :ui="{
            base: 'cursor-pointer select-none',
            trailingIcon: 'cursor-pointer',
          }"
        />
      </template>
      <template #clear-icon="{ clear }">
        <Icon
          v-if="value && !wrapperProps.disabled && !wrapperProps.required"
          :name="clearIcon"
          :class="theme.clearIcon({
            class: [ui?.clearIcon],
          })"
          @click.stop="clear"
        />
      </template>
    </Datepicker>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import Datepicker from '@vuepic/vue-datepicker'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { computed, useAppConfig, useFieldHOC, useUiConfig } from '#imports'
import '@vuepic/vue-datepicker/dist/main.css'
import type { IMonthFieldProps } from '#core/components/Form/InputMonth/types'
import { dateTimeTheme } from '#core/theme/dateTime'
import { format, type FormatOptionsWithTZ } from 'date-fns-tz'
import * as locales from 'date-fns/locale'
import { addYears } from 'date-fns'

const props = withDefaults(defineProps<IMonthFieldProps>(), {
  clearIcon: 'ph:x-circle-fill',
  teleport: false,
})

const appConfig = useAppConfig()
const theme = computed(() => useUiConfig(dateTimeTheme, 'dateTime')())

const {
  value, wrapperProps,
} = useFieldHOC<{
  year: number
  month: number
}>(props)

const formatDisplay = (date: {
  year: number
  month: number
} | undefined) => {
  if (!date) return ''

  // date type
  let newDateStr = new Date(date.year, date.month)

  const options: FormatOptionsWithTZ = {}

  if (appConfig.core?.is_thai_year) {
    newDateStr = addYears(newDateStr, 543)
  }

  if (appConfig.core?.is_thai_month) {
    options.locale = locales.th
  }

  return format(newDateStr, appConfig.core?.month_format, options)
}
</script>
