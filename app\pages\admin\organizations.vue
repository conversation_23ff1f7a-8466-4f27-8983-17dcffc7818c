<template>
  <NuxtLayout>
    <Organizations />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import Organizations from '~/features/admin/Organizations/index.vue'

definePageMeta({
  layout: 'admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.admin.organizations.permissions,
  },
})

useSeoMeta({
  title: routes.admin.organizations.label,
})

useApp().definePage({
  title: routes.admin.organizations.label,
  breadcrumbs: [routes.admin.organizations],
  sub_title: 'Organization Management',
})
</script>
