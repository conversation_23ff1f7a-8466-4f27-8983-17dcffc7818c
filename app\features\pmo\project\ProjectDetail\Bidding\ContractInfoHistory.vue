<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="Contract History"
    description="view change history"
    :ui="{
      content: 'max-w-2xl',
    }"
  >
    <template #body>
      <div class="space-y-5">
        <div
          v-for="value in history.fetch.items"
          :key="value.detail"
          class="rounded-md bg-[#F2F4F7] p-2"
        >
          <InfoItemList
            class="mt-4"
            :items="[
              {
                value: value.contract_no,
                label: 'เลขที่สัญญา',
              },
              {
                value: value.value,
                label: 'มูลค่าโครงการตามสัญญา',
                type: TYPE_INFO_ITEM.CURRENCY,
              },
              {
                value: value.signing_date,
                label: 'วันที่เซ็นสัญญา',
                type: TYPE_INFO_ITEM.DATE,
              },
              {
                value: value.duration_day,
                label: 'ระยะเวลา (วัน)',
              },
              {
                value: value.start_date,
                label: 'วันที่เริ่มสัญญา',
                type: TYPE_INFO_ITEM.DATE,
              },
              {
                value: value.end_date,
                label: 'วันที่สิ้นสุดสัญญา',
                type: TYPE_INFO_ITEM.DATE,
              },
              {
                value: value.warranty_duration_year,
                label: 'การรับประกัน / MA ปี',
              },
              {
                value: value.warranty_duration_day,
                label: 'การรับประกัน / MA เดือน',
              },
              {
                value: value.prime,
                label: 'บริษัทที่เป็น Prime ยื่นงาน',
              },
              {
                value: value.penalty_fee,
                label: 'ค่าปรับรายวัน',
                type: TYPE_INFO_ITEM.CURRENCY,
              },
              {
                value: value.is_legalize_stamp ? 'ใช่' : 'ไม่ใช่',
                label: 'ตีตราสาร',
                type: TYPE_INFO_ITEM.BOOLEAN,
              },
            ]"
          />
          <div class="mt-3 text-xs font-bold">
            update at  {{ TimeHelper.displayDateTime(value.created_at) }}
            <span v-if="value.updated_by"> by {{ value.updated_by.display_name }}</span>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex w-full justify-end">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ปิด
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { useProjectContractVersionLoader } from '~/loaders/pmo/project'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  projectId: string
  tab: string
}>()

const history = useProjectContractVersionLoader(props.projectId as string)

onMounted(() => {
  history.fetchPage(1, '', {
    params: {
      tab_key: props.tab,
      limit: 999,
    },
  })
})
</script>
