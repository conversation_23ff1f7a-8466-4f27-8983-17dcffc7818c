<template>
  <PmoCardCollapsible
    title="งบ หลักประกัน"
    subtitle="LG"
  >
    <Loader :loading="lg.status.value.isLoading">
      <InfoEditCard
        :form="form"
        :tab="tab"
        :form-fields="formFields"
        :have-history="lg.data.value"
        @save="onSave"
        @openHistory="onOpenHistory"
      />
    </Loader>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import InfoEditCard from '~/container/Pmo/InfoEditCard.vue'
import { useProjectLGLoader, useProjectCreateLGLoader } from '~/loaders/pmo/project'
import LgInfoHistory from './LgInfoHistory.vue'
import { format } from 'date-fns'

const props = defineProps<{ projectId: string
  tab: string }>()

const dialog = useDialog()
const overlay = useOverlay()
const lg = useProjectLGLoader(props.projectId as string)
const createLG = useProjectCreateLGLoader()
const historyModal = overlay.create(LgInfoHistory)
const noti = useNotification()

const onSave = (v: any) => {
  createLG.run({
    data: {
      ...v,
      start_date: format(v.start_date, 'yyyy-MM-dd'),
      end_date: format(v.end_date, 'yyyy-MM-dd'),
    },
    urlBind: {
      project_id: props.projectId,
    },
  })

  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังส่งข้อมูล...',
  })
}

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    start_date: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    end_date: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    interest: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    fee: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
  })),
  keepValuesOnUnmount: true,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.NUMBER,
    class: 'col-span-2',
    props: {
      label: 'หลักประกันสัญญา',
      name: 'value',
      placeholder: 'กรุณากรอกหลักประกันสัญญา',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันเริ่มหลักประกันสัญญา',
      name: 'start_date',
      placeholder: 'กรุณากรอกวันเริ่มหลักประกันสัญญา',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันสิ้นสุดหลักประกันสัญญา',
      name: 'end_date',
      placeholder: 'กรุณากรอกวันสิ้นสุดหลักประกันสัญญา',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'ค่าธรรมเนียม',
      name: 'interest',
      placeholder: 'กรุณากรอกค่าธรรมเนียม',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'มูลค่าดอกเบี้ย',
      name: 'fee',
      placeholder: 'กรุณากรอกมูลค่าดอกเบี้ย',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
])

onMounted(() => {
  lg.run()
})

const onOpenHistory = () => {
  historyModal.open({
    projectId: props.projectId,
    tab: props.tab,
  })
}

useWatchTrue(() => lg.status.value.isSuccess, async () => {
  form.setValues({
    ...lg.data.value,

  })
})

useWatchTrue(() => createLG.status.value.isSuccess, async () => {
  dialog.close()
  noti.success({
    title: 'แก้ไขประมูลงาน สำเร็จ',
  })

  lg.run()
})

useWatchTrue(() => createLG.status.value.isError, async () => {
  dialog.close()
  noti.error({
    title: 'แก้ไขประมูลงาน ไม่สำเร็จ',
    description: StringHelper.getError(createLG.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขประมูลงาน กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
