<template>
  <PmoCardCollapsible
    title="ข้อมูลผู้ติดต่อ"
    subtitle="Contact Person"
  >
    <Table
      :options="tableOptions"
      @pageChange="contact.fetchPageChange"
      @search="contact.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div
          v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
          class="flex justify-end"
        >
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
    <div
      v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
      class="flex justify-end pt-2"
    >
      <Button
        color="primary"
        icon="i-heroicons-plus"
        class="rounded-xl"
        @click="onAdd()"
      >
        เพิ่มรายการ
      </Button>
    </div>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import { usePmoContactPageLoader, usePmoProjectsPageLoader } from '~/loaders/pmo/project'
import Form from '~/features/pmo/project/ProjectDetail/Confidential/FormTable.vue'

defineProps<{
  tab: TabKey
}>()

const project = usePmoProjectsPageLoader()
const contact = usePmoContactPageLoader(project.find.item?.id as string)
const noti = useNotification()
const dialog = useDialog()
const overlay = useOverlay()
const addModal = overlay.create(Form)
const editModal = overlay.create(Form)
const permission = useProjectPermission()
const form = {
  validationSchema: toTypedSchema(v.object({
    fullname: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    phone: v.nullish(v.string()),
    email: v.nullish(v.string()),
    detail: v.nullish(v.string()),
    position: v.nullish(v.string()),
  })),
}

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อ',
      name: 'fullname',
      placeholder: 'ระบุชื่อ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เบอร์ติดต่อ',
      name: 'phone',
      placeholder: 'ระบุเบอร์ติดต่อ',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'อีเมล',
      name: 'email',
      placeholder: 'ระบุอีเมล',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'รายละเอียด',
      name: 'detail',
      placeholder: 'ระบุรายละเอียด',
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ตำแหน่ง',
      name: 'position',
      placeholder: 'ระบุตำแหน่ง',
    },
  },
])

contact.fetchSetLoading()
onMounted(() => {
  contact.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const tableOptions = useTable({
  options: {
    isHidePagination: true,
  },
  repo: contact,
  columns: () => [
    {
      accessorKey: 'fullname',
      header: 'ชื่อ',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'phone',
      header: 'เบอร์ติดต่อ',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'email',
      header: 'อีเมล',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'detail',
      header: 'รายละเอียด',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'position',
      header: 'ตำแหน่ง',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

const onEdit = (_value: any) => {
  editModal.open({
    isEditing: true,
    values: _value,
    title: 'ข้อมูลผู้ติดต่อ',
    subtitle: 'Contact Person',
    formFields: formFields,
    form: form,
    status: () => contact.update.status,
    onSubmit: (values: ITemplateDocument) => {
      contact.updateRun(_value.id, {
        data: values,
      })
    },
  })
}

const onDelete = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบข้อมูลผู้ติดต่อ "${values.fullname}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    contact.deleteRun(values.id)
  })
}

const onAdd = () => {
  addModal.open({
    title: 'ข้อมูลผู้ติดต่อ',
    subtitle: 'Contact Person',
    formFields: formFields,
    form: form,
    status: () => contact.add.status,
    onSubmit: (values: any) => {
      contact.addRun({
        data: values,
      })
    },
  })
}

useWatchTrue(
  () => contact.add.status.isSuccess,
  () => {
    addModal.close()

    noti.success({
      title: 'เพิ่มข้อมูลผู้ติดต่อสำเร็จ',
      description: 'คุณได้เพิ่มข้อมูลผู้ติดต่อเรียบร้อยแล้ว',
    })

    contact.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => contact.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มข้อมูลผู้ติดต่อไม่สำเร็จ',
      description: StringHelper.getError(contact.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มข้อมูลผู้ติดต่อ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => contact.update.status.isSuccess,
  () => {
    editModal.close()
    dialog.close()

    noti.success({
      title: 'แก้ไขข้อมูลผู้ติดต่อสำเร็จ',
      description: 'คุณได้แก้ไขข้อมูลผู้ติดต่อเรียบร้อยแล้ว',
    })

    contact.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => contact.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'แก้ไขข้อมูลผู้ติดต่อไม่สำเร็จ',
      description: StringHelper.getError(contact.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขข้อมูลผู้ติดต่อ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => contact.delete.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'ลบข้อมูลผู้ติดต่อสำเร็จ',
      description: 'คุณได้ลบข้อมูลผู้ติดต่อเรียบร้อยแล้ว',
    })

    contact.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => contact.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบข้อมูลผู้ติดต่อไม่สำเร็จ',
      description: StringHelper.getError(contact.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบข้อมูลผู้ติดต่อ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
