<template>
  <FieldWrapper v-bind="wrapperProps">
    <Datepicker
      v-model="innerValue"
      :teleport="teleport"
      :disabled="wrapperProps.disabled"
      :cancel-text="appConfig.core?.locale === 'th' ? 'ยกเลิก' : 'Cancel'"
      :select-text="appConfig.core?.locale === 'th' ? 'ตกลง' : 'Select'"
      :locale="appConfig.core?.locale"
      time-picker
      :placeholder="wrapperProps.placeholder"
      :format="format"
      :min-time="minTime"
      :max-time="maxTime"
      :start-time="startTime"
      :required="required"
      :enable-seconds="enableSeconds"
      @update:model-value="onChange"
    >
      <template #dp-input="{ value: innerValue }">
        <Input
          :trailing-icon="innerValue ? undefined : 'i-heroicons-clock'"
          type="text"
          :disabled="wrapperProps.disabled"
          :model-value="innerValue"
          :placeholder="wrapperProps.placeholder"
          :readonly="true"
          :ui="{
            base: 'cursor-pointer select-none',
            trailingIcon: 'cursor-pointer',
          }"
        />
      </template>
      <template #clear-icon="{ clear }">
        <Icon
          :name="clearIcon"
          :class="theme.clearIcon({
            class: [ui?.clearIcon],
          })"
          @click.stop="clear"
        />
      </template>
    </Datepicker>
  </FieldWrapper>
</template>

<script lang="ts" setup>
import Datepicker from '@vuepic/vue-datepicker'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'
import { computed, ref, useFieldHOC, useUiConfig, watch, useAppConfig } from '#imports'
import '@vuepic/vue-datepicker/dist/main.css'
import type { ITimeFieldProps, ITimeOption } from '#core/components/Form/InputTime/types'
import { dateTimeTheme } from '#core/theme/dateTime'

const emits = defineEmits(['change'])
const props = withDefaults(defineProps<ITimeFieldProps>(), {
  clearIcon: 'ph:x-circle-fill',
  enableSeconds: false,
  teleport: false,
})

const appConfig = useAppConfig()

const innerValue = ref<ITimeOption | undefined>(undefined)

const theme = computed(() => useUiConfig(dateTimeTheme, 'dateTime')())

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<string>(props)

const onChange = (value: ITimeOption | undefined) => {
  let timeText: string | undefined = undefined

  if (value) {
    const hours = value.hours?.toString().padStart(2, '0') ?? '00'
    const minutes = value.minutes?.toString().padStart(2, '0') ?? '00'
    const seconds = props.enableSeconds
      ? (value.seconds?.toString().padStart(2, '0') ?? '00')
      : ''

    timeText = `${hours}:${minutes}${props.enableSeconds ? `:${seconds}` : ''}`
  }

  handleChange(timeText)
  emits('change', timeText)
}

watch(value, (newValue) => {
  if (newValue) {
    const timeParts = newValue.split(':')

    innerValue.value = {
      hours: Number.parseInt(timeParts[0] || '0', 10),
      minutes: Number.parseInt(timeParts[1] || '0', 10),
      ...(props.enableSeconds && timeParts[2]
        ? {
          seconds: Number.parseInt(timeParts[2], 10),
        }
        : {}),
    }
  } else {
    innerValue.value = undefined
  }
}, {
  immediate: true,
})
</script>
