<template>
  <PmoCardCollapsible
    title="งบ ค้ำประกัน"
    subtitle="Bid Bond"
  >
    <Loader :loading="bindbond.status.value.isLoading">
      <InfoEditCard
        :form="form"
        :form-fields="formFields"
        :have-history="bindbond.data.value"
        :tab="tab"
        @save="onSave"
        @openHistory="onOpenHistory"
      />
    </Loader>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import InfoEditCard from '~/container/Pmo/InfoEditCard.vue'
import { useProjectBidBondLoader, useProjectCreateBidBondLoader } from '~/loaders/pmo/project'
import BindbondInfoHistory from './BindbondInfoHistory.vue'
import { format } from 'date-fns'

const props = defineProps<{ projectId: string
  tab: string }>()

const dialog = useDialog()
const overlay = useOverlay()
const bindbond = useProjectBidBondLoader(props.projectId as string)
const createBidBond = useProjectCreateBidBondLoader()
const historyModal = overlay.create(BindbondInfoHistory)
const noti = useNotification()

const onSave = (v: any) => {
  createBidBond.run({
    data: {
      ...v,
      start_date: format(v.start_date, 'yyyy-MM-dd'),
      end_date: format(v.end_date, 'yyyy-MM-dd'),
      duration_year: v.duration_year ? v.duration_year : 0,
    },
    urlBind: {
      project_id: props.projectId,
    },
  })

  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังส่งข้อมูล...',
  })
}

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    guarantee_asset: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    bidbond_payer: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    bidbond_value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    start_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันเริ่มค้ำประกัน')), ''),
    end_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันสิ้นสุดค้ำประกัน')), ''),
    duration_year: v.optional(v.pipe(v.number())),
    duration_month: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    fee: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
  })),
  keepValuesOnUnmount: true,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ประเภทของหลักทรัพย์',
      name: 'guarantee_asset',
      placeholder: 'กรุณากรอกประเภทของหลักทรัพย์',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ผู้ออกเงินค้ำประกัน',
      name: 'bidbond_payer',
      placeholder: 'กรุณากรอกผู้ออกเงินค้ำประกัน',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    class: 'col-span-2',
    props: {
      label: 'ยอดเงินค้ำประกัน',
      name: 'bidbond_value',
      placeholder: 'กรุณากรอกยอดเงินค้ำประกัน',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันเริ่มค้ำประกัน',
      name: 'start_date',
      placeholder: 'กรุณากรอกวันเริ่มค้ำประกัน',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันสิ้นสุดค้ำประกัน',
      name: 'end_date',
      placeholder: 'กรุณากรอกวันสิ้นสุดค้ำประกัน',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'จำนวนปี',
      name: 'duration_year',
      placeholder: 'กรุณากรอกจำนวนเดือน',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'จำนวนเดือน',
      name: 'duration_month',
      placeholder: 'กรุณากรอกจำนวนเดือน',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ค่าธรรมเนียม',
      name: 'fee',
      placeholder: 'กรุณากรอกค่าธรรมเนียม',
      required: true,
    },
  },
])

onMounted(() => {
  bindbond.run()
})

const onOpenHistory = () => {
  historyModal.open({
    projectId: props.projectId,
    tab: props.tab,
  })
}

useWatchTrue(() => bindbond.status.value.isSuccess, async () => {
  form.setValues({
    ...bindbond.data.value,
  })
})

useWatchTrue(() => createBidBond.status.value.isSuccess, async () => {
  dialog.close()
  noti.success({
    title: 'แก้ไขงบค้ำประกัน สำเร็จ',
  })

  bindbond.run()
})

useWatchTrue(() => createBidBond.status.value.isError, async () => {
  dialog.close()
  noti.error({
    title: 'แก้ไขงบค้ำประกัน ไม่สำเร็จ',
    description: StringHelper.getError(createBidBond.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขงบค้ำประกัน กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
