<template>
  <div class="mb-8 overflow-hidden rounded-lg border border-[#EAECF0] bg-white">
    <div
      class="flex w-full cursor-pointer items-center justify-between border-b border-[#EAECF0] px-4 py-2"
      @click="isOpen = !isOpen"
    >
      <div class="flex items-center">
        <div
          class="mr-2 flex h-[60px] items-center justify-center"
          :class="{ 'size-[60px]': type !== 'not_checked_in' }"
        >
          <img
            v-if="type !== 'not_checked_in'"
            :src="iconImages?.[type]"
            :alt="type"
            class="animate-jelly mx-auto mb-1 size-[48px] object-cover"
          />
        </div>
        <h2
          class="text-primary mr-2 font-bold"
          :class="{ 'text-red-600': type === 'not_checked_in' }"
        >
          {{ GROUP_LABELS[type] || 'Not Check-in yet' }}
        </h2>
        <Badge
          :color="people.length > 0 ? 'success' : 'neutral'"
          variant="solid"
          class="font-bold"
          :label="people.length || 0"
        />
      </div>
      <Icon
        :name="isOpen ? 'heroicons-outline:chevron-up' : 'heroicons-outline:chevron-down'"
        class="size-6"
      />
    </div>
    <div
      v-if="isOpen"
      class="p-4"
    >
      <div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        <div
          v-for="person in people"
          :key="person.user.display_name"
          class="w-full rounded-lg border border-[#EAECF0] p-3"
        >
          <AvatarProfile
            size="xl"
            :item="person.user"
          />
          <Badge
            v-if="person.location"
            variant="outline"
            class="mt-2 ml-14"
            :label="person.location"
            color="neutral"
          />
          <div
            v-if="person.created_at"
            class="mt-2 pl-14 text-left text-xs font-medium uppercase"
          >
            {{ useDateFormat(person.created_at, 'hh:mm a') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useDateFormat } from '@vueuse/core'

defineProps({
  type: {
    type: String,
    required: true,
  },
  people: {
    type: Array<any>,
    required: true,
  },
})

const isOpen = ref(true)
</script>

<style>
@keyframes jelly {
  0% {
    transform: scale(1, 1) translateX(0);
  }
  30% {
    transform: scale(1.25, 0.75) translateX(-5px);
  }
  40% {
    transform: scale(0.75, 1.25) translateX(5px);
  }
  50% {
    transform: scale(1.15, 0.85) translateX(-3px);
  }
  65% {
    transform: scale(0.95, 1.05) translateX(2px);
  }
  75% {
    transform: scale(1.05, 0.95) translateX(-1px);
  }
  100% {
    transform: scale(1, 1) translateX(0);
  }
}

.animate-jelly {
  animation: jelly 1.2s ease-in-out;
}
</style>
