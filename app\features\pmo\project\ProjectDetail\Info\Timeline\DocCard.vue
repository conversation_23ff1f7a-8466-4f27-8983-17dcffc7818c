<template>
  <div class="h-[190px] min-h-[190px] rounded-xl border-1 border-[#EAECF0] bg-[#F9FAFB] px-3 py-4">
    <div class="flex items-start gap-3">
      <div class="flex-1 space-y-2">
        <div class="flex items-center justify-between">
          <p class="truncate p-2 text-lg font-semibold text-[#101828]">
            {{ props.doc.title }}
          </p>
          <Badge
            variant="soft"
            size="sm"
            class="border border-[#D0D5DD] bg-[#ffffff] px-2 py-0.5"
          >
            <Avatar
              :src="props.doc.createdBy?.avatar_url"
              :alt="props.doc.createdBy?.display_name"
              class="mr-1 h-5 w-5"
              :ui="
                {
                  image: 'object-cover border-0 border-[#ffffff]',
                }
              "
            />
            <span>
              {{ props.doc.createdBy?.display_name }}
            </span>
          </Badge>
        </div>
        <p class="text-md flex items-center gap-2 truncate text-[#101828]">
          <Button
            variant="link"
            size="md"
            :label="props.doc.fileName"
            class="p-0 py-2 text-[#475467]"
            icon="mdi:file-outline"
            @click="download(props.doc.file)"
          />
        </p>
        <div class="flex items-start">
          <Badge
            variant="soft"
            size="md"
            class="border border-violet-200 bg-violet-50 text-violet-700"
          >
            {{ channelLabel(props.doc.type) }}
          </Badge>
        </div>
        <div class="text-md mt-2 flex items-center gap-2 text-gray-500">
          <Icon
            name="i-lucide-clock"
            class="h-4 w-4"
          />
          <span>{{ props.doc.date ? TimeHelper.displayDate(props.doc.date) : 'N/A' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  doc: {
    id: string
    title: string
    fileName: string
    tab_key: 'info' | 'sale' | 'pre-sale' | 'bidding'
    type: 'inbound' | 'outbound' | 'none'
    date: string // ISO
    createdBy?: IUser
    updatedBy?: IUser
    contentType?: string
    file: IFile
  }
}>()

// utils
const download = async (data: any) => {
  const pathname = data.file.type
  const ext = pathname.split('/')[1].trim()

  await downloadDocument({
    url: data.file.url,
    method: 'GET',
    filename: data.file.name,
    ext: ext,
  })
}

const channelLabel = (c: string) => {
  return c === 'pre-sale' ? 'Pre-Sale' : c === 'sale' ? 'Sale' : c === 'bidding' ? 'Bidding' : 'Info'
}
</script>
