<template>
  <PmoCardCollapsible
    title="ข้อมูลสัญญา"
    subtitle="Contract"
  >
    <Loader :loading="contract.status.value.isLoading">
      <InfoEditCard
        :form="form"
        :form-fields="formFields"
        :have-history="contract.data.value"
        :tab="tab"
        @save="onSave"
        @openHistory="onOpenHistory"
      />
    </Loader>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import InfoEditCard from '~/container/Pmo/InfoEditCard.vue'
import { useProjectContractLoader, useProjectCreateContractLoader } from '~/loaders/pmo/project'
import ContractInfoHistory from './ContractInfoHistory.vue'
import { format } from 'date-fns'

const props = defineProps<{ projectId: string
  tab: string }>()

const dialog = useDialog()
const overlay = useOverlay()
const contract = useProjectContractLoader(props.projectId as string)
const createContract = useProjectCreateContractLoader()
const historyModal = overlay.create(ContractInfoHistory)
const noti = useNotification()

const onSave = (v: any) => {
  createContract.run({
    data: {
      ...v,
      signing_date: format(v.signing_date, 'yyyy-MM-dd'),
      start_date: format(v.start_date, 'yyyy-MM-dd'),
      end_date: format(v.end_date, 'yyyy-MM-dd'),
    },
    urlBind: {
      project_id: props.projectId,
    },
  })

  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังส่งข้อมูล...',
  })
}

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    contract_no: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    signing_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันที่เซ็นสัญญา')), ''),
    start_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันที่เริ่มสัญญา')), ''),
    end_date: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาลงวันที่สิ้นสุดสัญญา')), ''),
    duration_day: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    warranty_duration_day: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    warranty_duration_year: v.optional(v.pipe(v.number()), 0),
    prime: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    penalty_fee: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
    is_legalize_stamp: v.optional(v.pipe(v.boolean())),
  })),
  keepValuesOnUnmount: true,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'เลขที่สัญญา',
      name: 'contract_no',
      placeholder: 'กรุณากรอกเลขที่สัญญา',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'มูลค่าโครงการตามสัญญา',
      name: 'value',
      placeholder: 'กรุณากรอกมูลค่าโครงการตามสัญญา',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันที่เซ็นสัญญา',
      name: 'signing_date',
      placeholder: 'กรุณากรอกวันที่เซ็นสัญญา',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'ระยะเวลา (วัน)',
      name: 'duration_day',
      placeholder: 'กรุณากรอกระยะเวลา (วัน)',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันที่เริ่มสัญญา',
      name: 'start_date',
      placeholder: 'กรุณากรอกวันที่เริ่มสัญญา',
      required: true,
    },
  },

  {
    type: INPUT_TYPES.DATE,
    props: {
      label: 'วันที่สิ้นสุดสัญญา',
      name: 'end_date',
      placeholder: 'กรุณากรอกวันที่สิ้นสุดสัญญา',
      required: true,
    },
  },

  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'การรับประกัน / MA ปี',
      name: 'warranty_duration_year',
      placeholder: 'กรุณากรอกระยะเวลาการประกวด (ปี)',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'การรับประกัน / MA เดือน',
      name: 'warranty_duration_day',
      placeholder: 'กรุณากรอกระยะเวลาการประกวด (วัน)',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'บริษัทที่เป็น Prime ยื่นงาน',
      name: 'prime',
      placeholder: 'กรุณากรอกบริษัทที่เป็น Prime ยื่นงาน',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'ค่าปรับรายวัน',
      name: 'penalty_fee',
      placeholder: 'กรุณากรอกค่าปรับรายวัน',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
  {
    type: INPUT_TYPES.CHECKBOX,
    props: {
      label: 'ตีตราสาร',
      name: 'is_legalize_stamp',
    },
  },
])

onMounted(() => {
  contract.run()
})

const onOpenHistory = () => {
  historyModal.open({
    projectId: props.projectId,
    tab: props.tab,
  })
}

useWatchTrue(() => contract.status.value.isSuccess, async () => {
  form.setValues({
    ...contract.data.value,

  })
})

useWatchTrue(() => createContract.status.value.isSuccess, async () => {
  dialog.close()
  noti.success({
    title: 'แก้ไขประมูลงาน สำเร็จ',
  })

  contract.run()
})

useWatchTrue(() => createContract.status.value.isError, async () => {
  dialog.close()
  noti.error({
    title: 'แก้ไขประมูลงาน ไม่สำเร็จ',
    description: StringHelper.getError(createContract.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขประมูลงาน กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
