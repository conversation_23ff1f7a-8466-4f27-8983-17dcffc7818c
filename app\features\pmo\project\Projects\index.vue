<template>
  <TeleportSafe to="#page-header">
    <Button
      v-if="auth.hasPermission(UserModule.PMO, Permission.ADMIN, Permission.SUPER)"
      trailing-icon="basil:plus-solid"
      :to="routes.pmo.project.projectCreate.to"
    >
      สร้างโครงการใหม่
    </Button>
  </TeleportSafe>
  <FormFields
    class="
        mb-4 grid w-full gap-4 md:mb-0
        lg:grid-cols-4
      "
    :options="formFields"
  />
  <Table
    :options="tableOptions"
    @pageChange="project.fetchPageChange"
    @search="project.fetchSearch"
  >
    <template #id-cell="{ row }">
      <div>
        <NuxtLink
          :to="routes.pmo.project.projectBySlug(row.original.slug).to"
          class="text-sm font-medium text-blue-600 hover:underline"
        >
          {{ row.original.code }}
        </NuxtLink>

        <div class="mt-0.5 line-clamp-2 text-base leading-snug text-gray-900">
          {{ row.original.name }}
        </div>

        <div class="text-sm text-gray-500">
          {{ row.original.client }}
        </div>
      </div>
    </template>
    <template #start_date-cell="{ row }">
      {{ row.original.contract_info ? TimeHelper.displayDate(row.original.contract_info?.start_date) : '-' }}
    </template>
    <template #end_date-cell="{ row }">
      {{ row.original.contract_info ? TimeHelper.displayDate(row.original.contract_info?.end_date) : '-' }}
    </template>
    <template #status-cell="{ row }">
      <Badge
        variant="soft"
        :class="ProjectStatusColorClass[row.original.status as PROJECT_STATUS]"
      >
        {{ PROJECT_STATUS_LABEL[row.original.status as PROJECT_STATUS] }}
      </Badge>
    </template>
    <template #actions-cell="{ row }">
      <Button
        icon="mage:pen"
        variant="ghost"
        color="neutral"
        square
        :to="routes.pmo.project.projectBySlug(row.original.slug).to"
      />
    </template>
  </Table>
</template>

<script lang="ts" setup>
import { usePmoProjectsPageLoader } from '~/loaders/pmo/project'
import { watchDebounced } from '@vueuse/core'

const project = usePmoProjectsPageLoader()
const route = useRoute()
const auth = useAuth()

project.fetchSetLoading()
onMounted(() => {
  project.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
    status: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'Search',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'status',
      placeholder: 'All Status',
      clearable: true,
      options: PROJECT_STATUS_OPTIONS,
    },
  },
])

const tableOptions = useTable({
  repo: project,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'โครงการ',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'start_date',
      header: 'วันที่เริ่ม',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'end_date',
      header: 'วันที่สิ้นสุด',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'status',
      header: 'สถานะ',
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

watchDebounced(form.values, (values) => {
  project.fetchSearch(values.q || '', {
    params: {
      status: values.status || undefined,
    },
  })
}, {
  debounce: 300,
  deep: true,
})
</script>
