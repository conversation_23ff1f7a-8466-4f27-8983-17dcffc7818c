<template>
  <div class="space-y-8">
    <PmoCard
      title="ข้อมูลอัปเดต"
      subtitle="Comment"
      no-underline
    >
      <div class="mb-8 overflow-x-auto">
        <Tabs
          v-if="project.find.item"
          v-model="activeTabIndex"
          color="neutral"
          variant="link"
          :items="navbar.sidebarPmoCommentChannel(project.find.item)"
          class="w-full min-w-max"
          :ui="{
            label: 'text-sm',
          }"
        />
      </div>
      <Comment
        :project-id="projectId"
        :channel="activeTabIndex"
      />
    </PmoCard>
  </div>
</template>

<script lang="ts" setup>
import Comment from '../Comment.vue'
import { PROJECT_TAB_TYPE } from '~/constants/projectTab'
import { usePmoProjectsPageLoader } from '~/loaders/pmo/project'

defineProps<{ projectId: string }>()
const project = usePmoProjectsPageLoader()
const activeTabIndex = ref<PROJECT_TAB_TYPE>(PROJECT_TAB_TYPE.SALES)
const navbar = useProjectPermission()
</script>
