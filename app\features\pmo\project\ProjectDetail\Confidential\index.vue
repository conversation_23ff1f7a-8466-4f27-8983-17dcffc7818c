<template>
  <div class="space-y-8">
    <Confidential
      :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL]"
      :project-id="projectId"
      :tab-label="PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.CONFIDENTIAL]"
    />
    <PmoCardCollapsible no-underline>
      <CheckList
        :project-id="projectId"
        :tab="PROJECT_TAB_TYPE.CONFIDENTIAL"
      />
    </PmoCardCollapsible>
    <ContactInfo :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL]" />
    <CompetitorInfo :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL]" />
    <PartnersInfo :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.CONFIDENTIAL]" />
    <PmoCardCollapsible
      title="ข้อมูลอัปเดต Confidential"
      sub-title="Comment"
    >
      <Comment
        :project-id="projectId"
        :channel="PROJECT_TAB_TYPE.CONFIDENTIAL"
      />
    </PmoCardCollapsible>
    <Remark
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.CONFIDENTIAL"
    />
  </div>
</template>

<script lang="ts" setup>
import CheckList from '~/container/Pmo/CheckList.vue'
import ContactInfo from './ContactInfo.vue'
import CompetitorInfo from './CompetitorInfo.vue'
import PartnersInfo from './PartnersInfo.vue'
import Confidential from '../Confidential.vue'
import Comment from '../Comment.vue'
import Remark from '../Remark.vue'

defineProps<{ projectId: string }>()
</script>
