export enum Permission {
  USER = 'USER',
  ADMIN = 'ADMIN',
  SUPER = 'SUPER',
  NONE = 'NONE',
}

export const PERMISSION_LABEL = {
  [Permission.ADMIN]: 'Admin',
  [Permission.USER]: 'User',
  [Permission.SUPER]: 'Super Admin',
  [Permission.NONE]: 'No Access',
}

export const PERMISSION_OPTIONS = Object.entries(PERMISSION_LABEL).map(([value, label]) => ({
  value,
  label,
}))

export enum PMO_PERMISSION {
  MODIFY = 'MODIFY',
  READONLY = 'READONLY',
  NONE = 'NONE',
}

export const PMO_PERMISSION_LABEL = {
  [PMO_PERMISSION.MODIFY]: 'Modify',
  [PMO_PERMISSION.READONLY]: 'Read Only',
  [PMO_PERMISSION.NONE]: 'No Permission',
}

export const PMO_PERMISSION_TEXT = {
  [PMO_PERMISSION.MODIFY]: 'M',
  [PMO_PERMISSION.READONLY]: 'R',
  [PMO_PERMISSION.NONE]: '-',
}

export const PMO_PERMISSION_COLOR = {
  [PMO_PERMISSION.MODIFY]: 'bg-emerald-600',
  [PMO_PERMISSION.READONLY]: 'bg-amber-600 ',
  [PMO_PERMISSION.NONE]: ' bg-[#F2F4F7] !text-[#98A2B3]',
}

export const PMO_PERMISSION_OPTIONS = Object.entries(PMO_PERMISSION_LABEL).map(([value, label]) => ({
  value,
  label,
}))
