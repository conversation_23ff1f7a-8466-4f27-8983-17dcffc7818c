<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่ม Team
      </Button>
    </TeleportSafe>
    <FormFields
      class="
         mb-4 grid w-full gap-4
        lg:grid-cols-2
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="team.fetchPageChange"
      @search="team.fetchSearch"
    >
      <template #code-cell="{ row }">
        <BadgeTeam :team="row.original" />
      </template>

      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
      <template #working_time-cell="{ row }">
        <div>
          {{
            row.original?.working_start_at?.slice(0, 5).replace(':', '.')
              + ' - '
              + row.original?.working_end_at?.slice(0, 5).replace(':', '.')
          }}
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useTeamPageLoader } from '~/loaders/admin/team'
import Form from '../Teams/Form.vue'
import { watchDebounced } from '@vueuse/core'

const team = useTeamPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const route = useRoute()
const isModal = ref(false)
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหาทีม',
    },
  },

])

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: team,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'code',
      header: 'Code Team',
    },
    {
      accessorKey: 'working_time',
      header: 'Working Time',
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

team.fetchSetLoading()
onMounted(() => {
  team.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const onEdit = (values: ITeam) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => team.update.status,
    onSubmit: (payload: ITeam) => {
      team.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  isModal.value = true
  addModal.open({
    status: () => team.add.status,
    onSubmit: (payload: ITeam) => {
      team.addRun({
        data: payload,
      })
    },
  })
}

const onDelete = (values: ITeam) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบ Team "${values.name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    team.deleteRun(values.id)
  })
}

watchDebounced(form.values, (values) => {
  team.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

useWatchTrue(
  () => team.update.status.isSuccess,
  () => {
    editModal.close()
    noti.success({
      title: 'แก้ไขTeamสำเร็จ',
      description: 'คุณได้แก้ไขTeamเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => team.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขTeamไม่สำเร็จ',
      description: StringHelper.getError(team.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขTeam กรุณาลองใหม่อีกครั้ง'),

    })
  },
)

useWatchTrue(
  () => team.delete.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'ลบTeamสำเร็จ',
      description: 'คุณได้ลบTeamเรียบร้อยแล้ว',

    })

    team.fetchPage()
  },
)

useWatchTrue(
  () => team.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบTeamไม่สำเร็จ',
      description: StringHelper.getError(team.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบTeam กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => team.add.status.isSuccess,
  () => {
    addModal.close()
    team.fetchPage()

    noti.success({
      title: 'เพิ่มTeamสำเร็จ',
      description: 'คุณได้เพิ่มTeamเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => team.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มTeamไม่สำเร็จ',
      description: StringHelper.getError(team.add.status.errorData, 'กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
