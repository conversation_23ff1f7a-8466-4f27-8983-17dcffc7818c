export interface ICheckinItem {
  id: string
  user_id: string
  type: string
  period: PERIOD
  leave_period?: string
  leave_type?: string
  location?: string
  is_unused?: boolean
  created_at: string
  remark?: string
  first_checkin_at: string
  date: string
  user: IUser
}

export interface GroupedCheckin {
  date: string
  original_timestamp: string
  detail: Array<Omit<ICheckinItem, 'date'>>
}

export interface IHolidays {
  id: string
  name: string
  date: Date
}

export interface IProject {
  id: string
  name: string
  code: string
  created_at: string
}
