<template>
  <NuxtLayout>
    <DocumentTemplate />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import DocumentTemplate from '~/features/admin/DocumentTemplate/index.vue'

definePageMeta({
  layout: 'pmo',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.pmo.documentTemplate.permissions,
  },
})

useSeoMeta({
  title: routes.pmo.documentTemplate.label,
})

useApp().definePage({
  title: routes.pmo.documentTemplate.label,
  breadcrumbs: [routes.pmo.documentTemplate],
  sub_title: 'Document Template Settings',
})
</script>
