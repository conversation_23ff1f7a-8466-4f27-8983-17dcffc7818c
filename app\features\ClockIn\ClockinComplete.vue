<template>
  <div class="pt-6 pb-2 text-lg font-bold">
    Today Clock-In Completed?
  </div>
  <div>
    {{ randomMessage }}
  </div>
  <div class="mt-2 flex w-full items-center justify-between rounded-lg border-[#EAECF0] bg-[#F9FAFB] p-5">
    <div class="">
      <div class="flex flex-wrap gap-2">
        <div
          v-for="(checkin, index) in checkins"
          :key="checkin.id"
          class="flex items-center gap-2"
        >
          <img
            :src="useGetIconCheckin(checkin)"
            :alt="useGetIconCheckin(checkin)"
            class="size-5"
          />
          <div class="text-lg font-bold">
            {{ CHECKIN_LABEL[checkin.type === CHECKIN.LEAVE
              ? checkin.leave_type as CheckinType || checkin.type as CheckinType
              :checkin.type as CheckinType]
              || checkin.type }} {{ checkin.type === CHECKIN.ONSITE ? '- ' + checkin.location :'' }}
            {{ checkins.length> 1 ? `(${PERIOD_LABEL[checkin.period as PERIOD]})` : '' }}
          </div>
          <div
            v-if="index+1 < checkins.length"
            class="text-xl leading-5 font-bold"
          >
            /
          </div>
        </div>
      </div>
      <Badge
        variant="outline"
        class="mt-2 rounded-full"
      >
        Clocked-in at
        <span class="uppercase">
          {{ checkins?.[0]?.date ? useDateFormat(checkins[0].date, 'hh:mm a') : 'N/A' }}
        </span>
      </Badge>
    </div>
    <Button
      label="Edit"
      icon="mage:pen"
      variant="outline"
      color="neutral"
      class="h-9"
      @click="$emit('edit')"
    />
  </div>
</template>

<script lang="ts" setup>
import { useDateFormat } from '@vueuse/core'

defineEmits(['edit'])
defineProps<{
  checkins: Array<ICheckinItem>
}>()

const {
  useGetIconCheckin,
} = useCheckinUtils()

const positiveMessages = [
  'Hope you have a wonderful time in May 🌼',
  'Wishing you a day full of positivity ✨',
  'Keep shining, you’re doing great 🌞',
  'May your day be filled with smiles 😊',
  'You’re one step closer to your goals 🎯',
  'Enjoy every little moment today 🍃',
  'You’ve got this! 💪',
  'Take a breath and enjoy the journey 🌈',
  'Sending you good vibes today 🌟',
  'Grateful for your dedication 🙌',
  'Let today be a fresh start 🌱',
  'Little progress is still progress 🚶‍♂️',
  'Smash those goals today! 🚀',
  'Your effort matters – always 💼',
  'Be kind to yourself today 🤍',
  'Every small win counts 🏆',
  'Make today count 🔥',
  'One day at a time ⏳',
  'You’re appreciated more than you know 🌺',
  'Thank you for showing up 👏',
]

const randomMessage = computed(() => {
  const index = Math.floor(Math.random() * positiveMessages.length)

  return positiveMessages[index]
})
</script>
