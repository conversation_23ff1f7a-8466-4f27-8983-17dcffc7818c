<template>
  <div
    v-show="isOpen"
    class="mt-4 space-y-5"
  >
    <form
      @submit="onSubmit"
    >
      <FormFields
        v-if="isEditing"
        class="grid gap-x-6 lg:grid-cols-2"
        :options="formFields"
      />
      <InfoItemList
        v-else
        class="mt-4"
        :items="formFields.map((field:any) => ({
          label: field.props.label,
          value: form.values[field.props.name],
          customClass: field.class,
          type: field.type === INPUT_TYPES.DATE
            ? TYPE_INFO_ITEM.DATE
            : field.type === INPUT_TYPES.NUMBER
              ? TYPE_INFO_ITEM.NUMBER
              : field.type === INPUT_TYPES.CHECKBOX
                ? TYPE_INFO_ITEM.BOOLEAN
                : TYPE_INFO_ITEM.TEXT,
        }))"
      />
      <div class="flex items-center justify-between pt-2">
        <p class="text-xs text-slate-400">
          <template v-if="form.values?.updated_at || form.values?.created_by">
            อัพเดทล่าสุดเมื่อ {{ TimeHelper.displayDateTime(form.values?.updated_at || form.values?.created_by) }}
            <span v-if="form.values?.updated_by || form.values?.created_by"> โดย {{ form.values?.updated_by?.display_name || form.values?.created_by?.display_name }}</span>
          </template>
        </p>

        <div
          v-if="permission.hasPermission(PROJECT_TAB_KEY[tab as PROJECT_TAB_TYPE], PMO_PERMISSION.MODIFY)"
          class="flex gap-2"
        >
          <div
            v-if="!isEditing"
            class="flex gap-2"
          >
            <Button
              v-if="haveHistory && form.values?.updated_by"
              variant="outline"
              color="neutral"
              icon="cuida:history-outline"
              @click="onOpenHistory"
            >
              History
            </Button>
            <Button
              variant="outline"
              color="neutral"
              icon="i-heroicons-pencil-square"
              @click="onEdit"
            >
              Edit
            </Button>
          </div>
          <template v-else>
            <Button
              variant="soft"
              color="neutral"
              @click="onCancel"
            >
              ยกเลิก
            </Button>
            <Button
              color="primary"
              type="submit"
              :disabled="!form.meta.value.dirty"
              @click="onSubmit"
            >
              บันทึก
            </Button>
          </template>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import type { PROJECT_TAB_TYPE } from '#imports'
import type { FormContext } from 'vee-validate'

const emit = defineEmits<{
  (e: 'save', v: any): void
  (e: 'openHistory'): void
}>()

const props = defineProps<{
  form: FormContext<Partial<any>>
  formFields: any
  haveHistory?: boolean
}>()

const tab = useRoute().query.tab as string
const permission = useProjectPermission()
const dialog = useDialog()

const isOpen = ref(true)
const isEditing = ref(false)

const onEdit = () => {
  isEditing.value = true
}

const onCancel = () => {
  isEditing.value = false
}

const onOpenHistory = () => {
  emit('openHistory')
}

const onSubmit = props.form.handleSubmit((values) => {
  dialog.confirm({
    title: 'ยืนยันแก้ไข',
    description: `คุณต้องการแก้ไขข้อมูลหรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    isEditing.value = false
    emit('save', values)
  })
})
</script>
