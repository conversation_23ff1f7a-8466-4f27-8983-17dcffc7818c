<template>
  <NuxtLayout>
    <Users />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import Users from '~/features/admin/user/Users/<USER>'

definePageMeta({
  layout: 'admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.admin.users.permissions,
  },
})

useSeoMeta({
  title: routes.admin.users.label,
})

useApp().definePage({
  title: routes.admin.users.label,
  breadcrumbs: [routes.admin.users],
  sub_title: 'User Management',
})
</script>
