<template>
  <div class="mt-8">
    <Button
      class="flex items-center gap-1 px-0 pb-8"
      :to="routes.home.to"
      variant="link"
      color="neutral"
    >
      <Icon
        class="size-7"
        name="ic:baseline-chevron-left"
      />

      <div class="flex items-center gap-2">
        <img
          src="/admin/timesheet-logo.png"
          alt="Timesheet"
          class="h-[50px] w-[50px]"
        />
        <div class="text-3xl font-semibold text-[#353D59]">
          Timesheet
        </div>
      </div>
    </Button>
    <Card>
      <ProfileWithTime />
    </Card>
    <Card class="mt-4">
      <div class="flex items-center gap-1 text-2xl font-bold">
        <Icon
          name="tabler:clock-check"
          class="size-6"
        />
        Weekly Timesheet
      </div>
      <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <div class="mt-4 text-lg font-semibold">
            ระบุ Timesheet ลงในวันที่คุณต้องการบันทึก
          </div>
          <div class="text-sm">
            Select date you wish to record in your timesheet.
          </div>
        </div>
        <Button
          icon="uil:file-plus-alt"
          class="w-fit"
          @click="addMultipleTimesheet"
        >
          Add multiple tasks
        </Button>
      </div>
      <Separator class="py-4" />
      <div class="flex flex-col gap-4 lg:flex-row">
        <div class="mx-auto h-fit w-full rounded-sm border border-[#D0D5DD] lg:w-auto">
          <VueDatePicker
            v-model="currentDate"
            :enable-time-picker="false"
            inline
            auto-apply
            :max-date="new Date()"
            :markers="markers"
            :disabled-dates="disabledDates"
            class="mx-auto"
            :month-change-on-scroll="false"
            @update-month-year="onUpdateMonthYear"
          />
        </div>
        <CalendarTasks
          class="w-full flex-1"
          :week-days="weekDays"
          :current-date="currentDate"
          @selectDate="(date) => currentDate= new Date(date)"
        />
      </div>
      <div class="mt-4 w-full rounded-lg bg-[#EFF8FF] p-5">
        <div class="flex items-center gap-1 text-2xl font-bold">
          <Icon
            name="tabler:clipboard-check"
            class="size-6"
          />
          Task list
        </div>
        <div class="mt-2 font-semibold text-[#344054]">
          {{ useDateFormat(currentDate, 'MMMM DD, YYYY') }}
        </div>
        <Separator class="my-4" />
        <div
          v-if="timesheetToday.length>0"
          class="space-y-4"
        >
          <TrackerItem
            v-for="item in timesheetToday"
            :key="item?.id"
            :item="item"
            @refresh="onRefresh"
          />
        </div>
        <div v-if="!openForm && timesheetToday.length > 0 ">
          <Separator class="my-4" />
          <div class="flex flex-col items-center justify-between gap-2 md:flex-row">
            <div
              v-if="timesheetToday.reduce((sum, it) => sum + it.timing, 0) < 8"
              class="text-sm"
            >
              You have {{ 8 - timesheetToday.reduce((sum, it) => sum + it.timing, 0) }}
              more hours to complete in your timesheet for today
            </div>

            <div
              v-else
              class="text-sm"
            >
              Nice, You have already finished today's timesheet
            </div>

            <Button
              icon="mdi-plus"
              variant="solid"
              @click="openForm = true"
            >
              Add task
            </Button>
          </div>
        </div>
        <div
          v-if="!openForm && timesheetToday.length === 0 "
          class="flex min-h-[200px] flex-col items-center justify-center gap-6 bg-white"
        >
          <p class="text-center text-gray-400">
            Empty timesheet on this day.<br />
            Let’s get it done
          </p>

          <Button
            icon="mdi-plus"
            variant="solid"
            @click="openForm = true"
          >
            Add task
          </Button>
        </div>
        <TrackerForm
          v-if="openForm"
          class="mt-4"
          :date="format(currentDate, 'yyyy-MM-dd')"
          @cancel="openForm = false"
          @success-create="onRefresh"
        />
      </div>
    </Card>
    <div class="flex items-center gap-1 pt-12 text-2xl font-bold">
      <Icon
        name="fluent:history-32-filled"
        class="size-6"
      />
      Month Summary
    </div>
    <div class="flex items-center gap-2">
      <div class="mt-2 font-semibold text-[#344054]">
        In {{ useDateFormat(currentDate, 'MMMM YYYY') }}
      </div>
    </div>

    <MonthlyReportProject
      v-if=" trackerList.fetch.items && trackerList.fetch.items?.length > 0"
      class="mt-4"
      :date="currentDate"
      :working-tracked-timing="workingTrackedTiming"
      :group-by-project="groupByProject"
      :total-tracked-timing="totalTrackedTiming"
    />
    <Card
      v-else
      class="mt-4"
    >
      <Loader
        :loading="trackerList.fetch.status.isLoading"
      >
        <Empty

          message="There are no timesheet records in this month"
        />
      </Loader>
    </Card>
  </div>
</template>

<script lang="ts" setup>
import ProfileWithTime from '~/container/ProfileWithTime.vue'
import CalendarTasks from './CalendarTasks.vue'
import { startOfWeek, addDays, startOfMonth, endOfMonth, format } from 'date-fns'
import { useDateFormat } from '@vueuse/core'
import { useTimeSheetPageLoader } from '~/loaders/admin/timesheet'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import VueDatePicker from '@vuepic/vue-datepicker'
import MonthlyReportProject from '~/container/Timesheet/MonthlyReportProject.vue'
import TrackerForm from './TrackerForm.vue'
import FormMultiple from './FormMultiple.vue'
import TrackerItem from './TrackerItem.vue'
import { TRACKER_TYPE, TRACKER_TYPE_LABEL } from '~/constants/timesheet'

const trackerList = useTimeSheetPageLoader()
const companyHolidays = useHolidaysPageLoader()
const currentDate = ref(new Date())
const openForm = ref(false)
const noti = useNotification()
const overlay = useOverlay()
const openModal = overlay.create(FormMultiple)

trackerList.fetchSetLoading()
companyHolidays.fetchSetLoading()
onMounted(() => {
  trackerList.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
    },
  })

  companyHolidays.fetchPage()
})

const timesheetToday = computed(() => {
  return trackerList.fetch.items?.filter(
    (item) => format(new Date(item.date), 'yyyy-MM-dd') === format(new Date(currentDate.value), 'yyyy-MM-dd'),
  ) || []
})

const onRefresh = () => {
  openForm.value = false
  trackerList.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
    },
  })

  companyHolidays.fetchPage()
}

const groupByTrackDate = computed(() => {
  const items = trackerList.fetch.items || []
  const groupedMap = new Map<string, any>()

  for (const item of items) {
    if (!groupedMap.has(item.date)) {
      groupedMap.set(item.date, {
        date: item.date,
        items: [],
        totalTiming: 0,
      })
    }

    const group = groupedMap.get(item.date)!

    group.items.push(item)
    group.totalTiming += item.timing || 0
  }

  return Array.from(groupedMap.values()).sort((a, b) => (a.date < b.date ? -1 : 1))
})

const weekDays = computed(() => {
  if (!currentDate.value) return []
  const start = startOfWeek(currentDate.value, {
    weekStartsOn: 1,
  })

  const result = []

  for (let i = 0; i < 7; i++) {
    const d = addDays(start, i)
    const formatted = format(d, 'yyyy-MM-dd')

    const trackers
      = groupByTrackDate.value?.find((t) => format(new Date(t.date), 'yyyy-MM-dd') === formatted)?.items || []

    result.push({
      date: formatted,
      label: format(d, 'd'),
      dayName: format(d, 'EEE'),
      trackers,
    })
  }

  return result
})

const disabledDates = computed(() => {
  if (!companyHolidays.fetch.items || !groupByTrackDate.value) return []

  return companyHolidays.fetch.items.map((holiday) => new Date(holiday.date))
})

const markers = computed(() => {
  const result: any[] = []

  if (groupByTrackDate.value) {
    result.push(
      ...groupByTrackDate.value.map((group) => ({
        date: new Date(group.date),
        type: 'line',
        color: group.totalTiming >= 8 ? '#079455' : '#eab308',
      })),
    )
  }

  if (companyHolidays.fetch.items) {
    result.push(
      ...companyHolidays.fetch.items.map((holiday) => ({
        date: new Date(holiday.date),
        type: 'line',
        color: '#A5A5A5',
        tooltip: [{
          text: `หยุด ${holiday.name}`,
          color: '#A5A5A5',
        }],
      })),
    )
  }

  return result
})

const holidaysList = computed(() => companyHolidays.fetch.items?.map((h) => h.date) ?? [])

const totalWorkingDays = computed(() => {
  return useWorkingDayInMonth(1, 2 - 1, holidaysList.value)
})

const totalTrackedTiming = computed(() => totalWorkingDays.value * 8)

const onUpdateMonthYear = ({
  month, year,
}: { month: number
  year: number }) => {
  currentDate.value = new Date(year, month, 1)
  onRefresh()
}

const addMultipleTimesheet = () => {
  if (currentDate.value.getMonth() !== new Date().getMonth()) {
    currentDate.value = new Date()
    onRefresh()
  }

  openModal.open({
    status: () => trackerList.add.status,
    markers,
    onUpdateMonthYear: onUpdateMonthYear,
    onSubmit: (payload: any) => {
      trackerList.addRun({
        data: payload,
      })
    },
  })
}

const workingTrackedTiming = computed(() => {
  const items = trackerList.fetch.items ?? []

  return items.reduce((acc, item) => acc + item.timing, 0)
})

const trackerTypeOrder = [TRACKER_TYPE.INTERNAL,
  TRACKER_TYPE.EXTERNAL,
  TRACKER_TYPE.LEAVE,
  TRACKER_TYPE.SGA,
  TRACKER_TYPE.OT,
  TRACKER_TYPE.INTERNAL]

const orderMap = new Map(trackerTypeOrder.map((key, index) => [key, index]))
const groupByProject = computed(() => {
  const items = trackerList.fetch.items ?? []
  const groupedMap = new Map<string, any>()

  for (const item of items) {
    const typeKey = item.type
    const typeLabel = TRACKER_TYPE_LABEL[typeKey as TRACKER_TYPE] ?? typeKey

    if (!groupedMap.has(typeKey)) {
      groupedMap.set(typeKey, {
        type: typeKey,
        typeLabel,
        children: [],
      })
    }

    let name = 'Unknown'

    if (item.project_code) {
      name = item.project?.name
    } else if (item.sga_id) {
      name = item.sga?.name
    } else if (item.leave_type) {
      name = item.leave_type
    }

    const parent = groupedMap.get(typeKey)
    let child = parent.children.find((c: any) => c.name === name)

    if (!child) {
      child = {
        name,
        items: [],
      }

      parent.children.push(child)
    }

    child.items.push(item)
  }

  return Array.from(groupedMap.values())
    .sort((a, b) => {
      const aOrder = orderMap.get(a.type) ?? Infinity
      const bOrder = orderMap.get(b.type) ?? Infinity

      return aOrder - bOrder
    })
    .map((group) => ({
      ...group,
      children: group.children.map((child: any) => ({
        ...child,
        totalTiming: child.items.reduce((acc: number, item: ITimesheet) => acc + item.timing, 0),
      })),
      totalTiming: group.children.reduce(
        (acc: number, child: any) =>
          acc + child.items.reduce((acc2: number, item: ITimesheet) => acc2 + item.timing, 0),
        0,
      ),
    }))
})

useWatchTrue(
  () => trackerList.add.status.isSuccess,
  () => {
    openModal.close()
    noti.success({
      title: 'Timesheet added successfully',
      description: 'Your timesheet has been added successfully.',
    })

    trackerList.fetchPage(1, '', {
      params: {
        limit: 999,
        start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
        end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      },
    })
  },
)

useWatchTrue(
  () => trackerList.add.status.isError,
  () => {
    noti.error({
      title: 'Timesheet addition failed',
      description: StringHelper.getError(trackerList.add.status.errorData, 'An error occurred while saving changes, please try again'),
    })
  },
)
</script>
