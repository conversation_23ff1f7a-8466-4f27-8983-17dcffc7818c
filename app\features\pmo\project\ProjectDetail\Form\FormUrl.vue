<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="จัดการ URL โครงการ"
    description="Group Document"
    :ui="{
      content: 'max-w-2xl',
    }"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormField
          label="URL โครงการ (Project Slug) *"
          :error="form.submitCount.value > 0 || checkSlug.data.value ? form.errors.value.slug : ''"
        >
          <div class="relative flex items-center">
            <span class="bg-primary flex h-11 items-center rounded-l-md px-3 py-2 text-[16px] leading-4 text-white">
              https://work.finema.co/pmo/
            </span>
            <Input
              v-model="form.values.slug"
              :mask="MaskaHelper.englishLetter()"
              type="text"
              placeholder="URL โครงการ (Project Slug)"
              :ui="{
                base: ' !rounded-l-none',
              }"
              @update:modelValue="(value) => form.setFieldValue('slug', value) "
            />
            <Badge
              v-if="available"
              variant="soft"
              label="Available"
              color="success"
              icon="material-symbols:check-circle-outline-rounded"
              class="absolute right-4 rounded-full border-1"
            />
            <Badge
              v-else-if="available === false"
              variant="soft"
              label="Not Available"
              color="error"
              class="absolute right-4 rounded-full border-1"
              icon="material-symbols:cancel-outline-rounded"
            />
          </div>
        </FormField>
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty ||status().isLoading"
            type="submit"
          >
            บันทึก
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { usePmoProjectCheckSlugPageLoader } from '~/loaders/pmo/project'
import { watchDebounced } from '@vueuse/core'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void
}>()

const checkSlug = usePmoProjectCheckSlugPageLoader()
const available = ref<boolean | null>(null)
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    slug: v.nullish(v.pipe(v.string())),
  })),
  initialValues: props.values,

})

const onSubmit = form.handleSubmit((values) => {
  if (available.value === false) {
    return
  }

  props.onSubmit(values as any)
})

watchDebounced(
  () => form.values.slug,
  (value) => {
    if (props.values.slug === value) {
      available.value = true

      return
    }

    if (value === '') {
      available.value = false

      return
    }

    checkSlug.run({
      data: {
        slug: value,
      },
    })
  },
  {
    debounce: 600,
  },
)

useWatchTrue(
  () => checkSlug.status.value.isSuccess,
  () => {
    if (!checkSlug.data.value?.is_available) {
      available.value = false
    } else {
      available.value = true
    }
  },
)

useWatchTrue(
  () => checkSlug.status.value.isError,
  () => {
    available.value = false
  },
)
</script>
