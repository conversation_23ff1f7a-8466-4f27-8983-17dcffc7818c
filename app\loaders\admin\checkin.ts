export const useCheckInsPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<ICheckinItem>({
    baseURL: '/checkins',
    getBaseRequestOptions: options.auth,
  })
}

export const useAdminCheckInsPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<ICheckinItem>({
    baseURL: '/admin/checkins',
    getBaseRequestOptions: options.auth,
  })
}

export const useCheckinsTodayLoader = () => {
  const options = useRequestOptions()

  return useListLoader<ICheckinItem>({
    url: '/checkins/on-this-day',
    getRequestOptions: options.auth,
  })
}
