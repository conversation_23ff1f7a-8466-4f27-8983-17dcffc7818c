<template>
  <NuxtLayout>
    <SaleWeeklyUpdate />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import SaleWeeklyUpdate from '~/features/admin/WeeklyUpdate/SaleWeeklyUpdate/index.vue'

definePageMeta({
  layout: 'pmo',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.pmo.saleWeeklyUpdate.permissions,
  },
})

useSeoMeta({
  title: routes.pmo.saleWeeklyUpdate.label,
})

useApp().definePage({
  title: routes.pmo.saleWeeklyUpdate.label,
  breadcrumbs: [routes.pmo.saleWeeklyUpdate],
  sub_title: 'updated weekly by sale',
})
</script>
