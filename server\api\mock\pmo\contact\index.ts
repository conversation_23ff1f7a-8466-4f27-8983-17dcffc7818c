import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '3f5f3e9e-6b20-4cf3-9c14-7b1f0417a001',
      project_id: 'a2c4f2f3-2b4d-42f5-bf9f-4e1e4e7b1001',
      fullname: 'สมชาย ใจดี',
      phone: '0812345678',
      email: '<EMAIL>',
      detail: 'ผู้ติดต่อหลักของโครงการ',
      company: 'บริษัท เอ บี ซี จำกัด',
      position: 'Project Manager',
      created_at: new Date('2025-01-10T09:00:00Z'),
      created_by_id: '11111111-2222-3333-4444-555555555555',
      updated_at: new Date('2025-02-15T10:30:00Z'),
      updated_by_id: '11111111-2222-3333-4444-555555555555',
      deleted_at: null,
      deleted_by_id: null,
    },
    {
      id: '4b2e7a77-9a55-48c9-a111-bac51a770002',
      project_id: 'a2c4f2f3-2b4d-42f5-bf9f-4e1e4e7b1001',
      fullname: 'Olivia Rhye',
      phone: '+66 2 555 8888',
      email: '<EMAIL>',
      detail: 'ฝ่ายการเงิน',
      company: 'FinWork Solutions',
      position: 'Finance Lead',
      created_at: new Date('2025-02-01T08:00:00Z'),
      created_by_id: '22222222-3333-4444-5555-666666666666',
      updated_at: new Date('2025-02-20T09:45:00Z'),
      updated_by_id: '22222222-3333-4444-5555-666666666666',
      deleted_at: null,
      deleted_by_id: null,
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
