<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
  >
    <template #content>
      <Card
        :ui="{
          body: 'bg-[url(/dialog-bg.png)] bg-no-repeat bg-top-left bg-size-[220px]',
        }"
      >
        <div class="bg-error/10 mb-4 flex h-[48px] w-[48px] items-center justify-center rounded-full">
          <Icon
            name="prime:trash"
            class="text-error size-5"
          />
        </div>
        <div class="text-xl font-semibold">
          ลบโครงการ
        </div>
        <div class="text-muted mt-1 pb-4 text-sm">
          Delete Project
        </div>
        <form @submit="onSubmit">
          <FormField
            required
            help="*This action will remove the project from all lists and will no longer be accessible."
            :ui="{
              labelWrapper: 'justify-start gap-1 flex-wrap',
            }"
            label="Enter the following to confirm:"
            :error="form.submitCount.value > 0 ? form.errors.value.confirm : ''"
          >
            <template #hint>
              <Badge
                variant="soft"
                class="rounded-full break-words"
              >
                <div class="w-full max-w-[calc(100vw-72px)] md:max-w-[392px] lg:max-w-[448px]">
                  {{ values?.slug }}
                </div>
              </Badge>
            </template>
            <Input
              v-model="form.values.confirm"
              placeholder="Enter the following to confirm"
              @update:modelValue="(value) => form.setFieldValue('confirm', value) "
            />
          </FormField>
          <div class="mt-4 flex justify-end gap-3">
            <Button
              variant="outline"
              color="neutral"
              @click="emits('close', false)"
            >
              ยกเลิก
            </Button>
            <Button
              :loading="status().isLoading"
              :disabled="!form.meta.value.dirty ||status().isLoading"
              type="submit"
              color="error"
            >
              ลบโครงการ
            </Button>
          </div>
        </form>
      </Card>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void
}>()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    confirm: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
  initialValues: props.values,

})

const onSubmit = form.handleSubmit((values) => {
  if (values.confirm !== props.values?.slug) {
    form.setErrors({
      confirm: 'ค่าที่กรอกไม่ตรงกับที่กำหนด',
    })

    return
  }

  props.onSubmit(values as any)
})
</script>
