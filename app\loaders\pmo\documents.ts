export const usePmoDocumentByProjectIdPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IDocument>({
    baseURL: `/pmo/projects/${projectId}/documents`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoDocumentItemByProjectIdPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IDocument>({
    baseURL: `/pmo/projects/${projectId}/documents/items`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoDocumentHistoryByProjectIdPageLoader = (projectId: string, itemId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IDocument>({
    baseURL: `/pmo/projects/${projectId}/documents/items/${itemId}/versions`,
    getBaseRequestOptions: options.auth,
  })
}

export const usePmoDocumentGroupsByProjectIdPageLoader = (projectId: string) => {
  const options = useRequestOptions()

  return usePageLoader<IDocumentGroup>({
    baseURL: `/pmo/projects/${projectId}/documents/groups`,
    getBaseRequestOptions: options.auth,
  })
}

export const useDocumentRequestUploadFileLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader({
    method: 'post',
    url: '/uploads',
    getRequestOptions: options.auth,
  })
}
