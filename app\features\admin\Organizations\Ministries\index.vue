<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="addMinistry"
      >
        เพิ่มกระทรวง
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="mb-4"
    />
    <Table
      :options="tableOptions"
      @pageChange="ministry.fetchPageChange"
      @search="ministry.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="editMinistry(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { watchDebounced } from '@vueuse/core'
import Form from './Form.vue'
import { useOrgMinistryPageLoader, useOrgMinistryAdminPageLoader } from '~/loaders/admin/organizations'

const ministry = useOrgMinistryPageLoader()
const ministryAdmin = useOrgMinistryAdminPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหากระทรวง',
    },
  },
])

watchDebounced(form.values, (values) => {
  ministry.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<IMinistry>({
  repo: ministry,
  columns: () => [
    {
      accessorKey: 'name_th',
      header: 'ชื่อกระทรวง (ภาษาไทย)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'name_en',
      header: 'ชื่อกระทรวง (ภาษาอังกฤษ)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },

    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

ministry.fetchSetLoading()
onMounted(() => {
  ministry.fetchPage()
})

const editMinistry = (_ministry: IMinistry) => {
  editModal.open({
    isEditing: true,
    values: _ministry,
    status: () => ministry.update.status,
    onSubmit: (values: IMinistry) => {
      ministryAdmin.updateRun(_ministry.id, {
        data: values,
      })
    },
  })
}

const addMinistry = () => {
  addModal.open({
    status: () => ministry.add.status,
    onSubmit: (values: IMinistry) => {
      ministryAdmin.addRun({
        data: values,
      })
    },
  })
}

const onDelete = (values: IMinistry) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบกระทรวง "${values.name_th}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    ministryAdmin.deleteRun(values.id)
  })
}

useWatchTrue(
  () => ministryAdmin.update.status.isSuccess,
  () => {
    editModal.close()
    ministry.fetchPage()

    noti.success({
      title: 'แก้ไขกระทรวงสำเร็จ',
      description: 'คุณได้แก้ไขกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => ministryAdmin.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขกระทรวงไม่สำเร็จ',
      description: StringHelper.getError(ministryAdmin.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขกระทรวง กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => ministryAdmin.delete.status.isSuccess,
  () => {
    dialog.close()
    ministry.fetchPage()
    noti.success({
      title: 'ลบกระทรวงสำเร็จ',
      description: 'คุณได้ลบกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => ministryAdmin.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบกระทรวงไม่สำเร็จ',
      description: StringHelper.getError(ministryAdmin.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบกระทรวง กรุณาลองใหม่อีกครั้ง'),

    })
  },
)

useWatchTrue(
  () => ministryAdmin.add.status.isSuccess,
  () => {
    addModal.close()
    ministry.fetchPage()

    noti.success({
      title: 'เพิ่มกระทรวงสำเร็จ',
      description: 'คุณได้เพิ่มกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => ministryAdmin.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มกระทรวงไม่สำเร็จ',
      description: StringHelper.getError(ministryAdmin.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มกระทรวง กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
