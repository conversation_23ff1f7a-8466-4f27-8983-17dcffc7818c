export interface IUser {
  id: string
  email: string
  full_name: string
  display_name: string
  position: string
  team: ITeam
  team_code: string
  avatar_url: string
  access_level: IUserAccessLevel
  is_active: boolean
  joined_date: string
  created_at: string
  updated_at: string
}

export enum UserModule {
  CHECKIN = 'clockin',
  PMO = 'pmo',
  TIMESHEET = 'timesheet',
  SETTING = 'setting',
}

export interface IUserAccessLevel {
  used_id: string
  pmo: Permission.USER | Permission.ADMIN | Permission.SUPER
  clockin: Permission.USER | Permission.ADMIN
  timesheet: Permission.USER | Permission.ADMIN
  setting: Permission.USER | Permission.SUPER
}
