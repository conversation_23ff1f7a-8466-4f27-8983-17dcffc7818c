<template>
  <div class="">
    <div class="flex items-start justify-between gap-4 py-6">
      <div class="min-w-0">
        <div class="flex flex-wrap items-center gap-2">
          <h1 class="text-3xl font-semibold tracking-tight text-slate-900 dark:text-slate-50">
            {{ app.pageMeta.title }}
          </h1>

          <div id="page-subtitle" />
        </div>

        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
          {{ app.pageMeta.sub_title }}
        </p>
        <div class="flex items-center justify-between gap-2">
          <div id="page-email" />
          <div id="page-tags" />
        </div>
      </div>

      <!-- Right: Actions -->
      <div class="flex shrink-0 items-center gap-2">
        <div id="page-actions" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const app = useApp()
</script>
