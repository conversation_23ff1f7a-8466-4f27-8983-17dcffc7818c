<template>
  <PmoCardCollapsible
    title="รายการของ"
    subtitle="Item"
  >
    <Table
      :options="tableOptions"
      @pageChange="vendor.fetchPageChange"
      @search="vendor.fetchSearch"
    >
      <template #deliver_duration_day-cell="{ row }">
        {{ row.original?.deliver_duration_day ? row.original.deliver_duration_day + ' วัน' : '-' }}
      </template>
      <template #is_tor-cell="{ row }">
        <Icon
          class="size-5"
          :class="row.original.is_tor ? 'text-primary' : ''"
          :name="row.original.is_tor ?'ic:baseline-check' :'bi:dash-lg'"
        />
      </template>
      <template #is_implementation-cell="{ row }">
        <Icon
          class="size-5"
          :class="row.original.is_implementation ? 'text-primary' : ''"
          :name="row.original.is_implementation ?'ic:baseline-check' :'bi:dash-lg'"
        />
      </template>
      <template #is_training-cell="{ row }">
        <Icon
          class="size-5"
          :class="row.original.is_training ? 'text-primary' : ''"
          :name="row.original.is_training ?'ic:baseline-check' :'bi:dash-lg'"
        />
      </template>
      <template #is_user_manual-cell="{ row }">
        <Icon
          class="size-5"
          :class="row.original.is_user_manual ? 'text-primary' : ''"
          :name="row.original.is_user_manual ?'ic:baseline-check' :'bi:dash-lg'"
        />
      </template>
      <template #actions-cell="{ row }">
        <DropdownMenu
          v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
          :items="groupActions(row.original)"
        >
          <Button
            class="p-0"
            color="neutral"
            icon="i-heroicons-ellipsis-vertical"
            variant="ghost"
          />
        </DropdownMenu>
      </template>
    </Table>
    <div
      v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
      class="flex justify-end pt-2"
    >
      <Button
        color="primary"
        icon="i-heroicons-plus"
        class="rounded-xl"
        @click="onAdd()"
      >
        เพิ่มรายการ
      </Button>
    </div>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import { usePmoVendorItemPageLoader, usePmoProjectsPageLoader } from '~/loaders/pmo/project'
import Form from '~/features/pmo/project/ProjectDetail/Confidential/FormTable.vue'
import type { DropdownMenuItem } from '@nuxt/ui'

defineProps<{
  tab: TabKey
}>()

const project = usePmoProjectsPageLoader()
const vendor = usePmoVendorItemPageLoader(project.find.item?.id as string)
const noti = useNotification()
const dialog = useDialog()
const overlay = useOverlay()
const addModal = overlay.create(Form)
const editModal = overlay.create(Form)
const permission = useProjectPermission()
const form = {
  validationSchema: toTypedSchema(v.object({
    vendor_name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    item_name: v.nullish(v.string()),
    item_detail: v.nullish(v.string()),
    deliver_duration_day: v.nullish(v.number()),
    is_tor: v.optional(v.pipe(v.boolean())),
    is_training: v.optional(v.pipe(v.boolean())),
    is_implementation: v.optional(v.pipe(v.boolean())),
    is_user_manual: v.optional(v.pipe(v.boolean())),
  })),
}

const groupActions = (value: any): DropdownMenuItem[] => {
  return [
    {
      label: 'Edit Group',
      icon: 'i-heroicons-pencil-square-20-solid',
      onSelect() {
        editModal.open({
          isEditing: true,
          values: value,
          title: 'รายการของ',
          subtitle: 'Item',
          formFields: formFields,
          form: form,
          status: () => vendor.update.status,
          onSubmit: (values: ITemplateDocument) => {
            vendor.updateRun(value.id, {
              data: values,
            })
          },
        })
      },
    },
    {
      label: 'Delete Group',
      icon: 'prime:trash',
      color: 'error',
      onSelect() {
        dialog.confirm({
          title: 'ยืนยันการลบ',
          description: `คุณต้องการลบรายการของ "${value.vendor_name}" หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
          type: DialogType.ERROR,
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          vendor.deleteRun(value.id)
        })
      },
    },
  ]
}

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ผู้ขาย',
      name: 'vendor_name',
      placeholder: 'ระบุผู้ขาย',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'รายการ',
      name: 'item_name',
      placeholder: 'ระบุรายการ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'รายละเอียด',
      name: 'item_detail',
      placeholder: 'ระบุรายละเอียด',
      required: true,
    },

  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'ระยะเวลาการส่งมอบ (วัน)',
      name: 'deliver_duration_day',
      placeholder: 'ระบุระยะเวลาการส่งมอบ (วัน)',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.CHECKBOX,
    props: {
      label: 'TOR',
      name: 'is_tor',
    },
  },
  {
    type: INPUT_TYPES.CHECKBOX,
    props: {
      label: 'ติดตั้ง',
      name: 'is_implementation',
    },
  },
  {
    type: INPUT_TYPES.CHECKBOX,
    props: {
      label: 'อบรม',
      name: 'is_training',
    },
  },
  {
    type: INPUT_TYPES.CHECKBOX,
    props: {
      label: 'เอกสารการใช้งาน',
      name: 'is_user_manual',
    },
  },
])

vendor.fetchSetLoading()
onMounted(() => {
  vendor.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const tableOptions = useTable({
  repo: vendor,
  columns: () => [
    {
      accessorKey: 'vendor_name',
      header: 'ผู้ขาย',
      type: COLUMN_TYPES.TEXT,

    },
    {
      accessorKey: 'item_name',
      header: 'รายการ',
      type: COLUMN_TYPES.TEXT,

    },
    {
      accessorKey: 'item_detail',
      header: 'รายละเอียด',
      type: COLUMN_TYPES.TEXT,

    },
    {
      accessorKey: 'deliver_duration_day',
      header: 'ระยะเวลาการส่งมอบ',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: {
          th: 'text-center',
          td: 'text-center whitespace-nowrap',
        },
      },
    },
    {
      accessorKey: 'is_tor',
      header: 'TOR',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'text-center',
          td: 'text-center',
        },
      },

    },
    {
      accessorKey: 'is_implementation',
      header: 'ติดตั้ง',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'text-center',
          td: 'text-center',
        },
      },

    },
    {
      accessorKey: 'is_training',
      header: 'อบรม',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'text-center',
          td: 'text-center',
        },
      },

    },
    {
      accessorKey: 'is_user_manual',
      header: 'เอกสารการใช้งาน',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'text-center',
          td: 'text-center',
        },
      },

    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

const onAdd = () => {
  addModal.open({
    title: 'รายการของ',
    subtitle: 'Item',
    formFields: formFields,
    form: form,
    status: () => vendor.add.status,
    onSubmit: (values: any) => {
      vendor.addRun({
        data: values,
      })
    },
  })
}

useWatchTrue(
  () => vendor.add.status.isSuccess,
  () => {
    addModal.close()

    noti.success({
      title: 'เพิ่มรายการของสำเร็จ',
      description: 'คุณได้เพิ่มรายการของเรียบร้อยแล้ว',
    })

    vendor.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => vendor.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มรายการของไม่สำเร็จ',
      description: StringHelper.getError(vendor.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มรายการของ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => vendor.update.status.isSuccess,
  () => {
    editModal.close()
    dialog.close()

    noti.success({
      title: 'แก้ไขรายการของสำเร็จ',
      description: 'คุณได้แก้ไขรายการของเรียบร้อยแล้ว',
    })

    vendor.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => vendor.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'แก้ไขรายการของไม่สำเร็จ',
      description: StringHelper.getError(vendor.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขรายการของ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => vendor.delete.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'ลบรายการของสำเร็จ',
      description: 'คุณได้ลบรายการของเรียบร้อยแล้ว',
    })

    vendor.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => vendor.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบรายการของไม่สำเร็จ',
      description: StringHelper.getError(vendor.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบรายการของ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
