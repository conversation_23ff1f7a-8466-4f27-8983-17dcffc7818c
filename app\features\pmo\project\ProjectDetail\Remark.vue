<template>
  <PmoCardCollapsible
    title="หมายเหตุ"
    subtitle="Remark"
  >
    <Loader :loading="remark.status.value.isLoading">
      <InfoEditCard
        :form="form"
        :form-fields="formFields"
        :have-history="remark.data.value"
        :tab="tab"
        @save="onSave"
        @openHistory="onOpenHistory"
      />
    </Loader>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import InfoEditCard from '~/container/Pmo/InfoEditCard.vue'
import { useProjectRemarkLoader, useProjectCreateRemarkLoader } from '~/loaders/pmo/project'
import RemarkHistory from './RemarkHistory.vue'

const props = defineProps<{ projectId: string
  tab: string }>()

const dialog = useDialog()
const overlay = useOverlay()
const historyModal = overlay.create(RemarkHistory)
const remark = useProjectRemarkLoader(props.projectId as string)
const createRemark = useProjectCreateRemarkLoader()
const noti = useNotification()

const onSave = (v: any) => {
  createRemark.run({
    data: {
      ...v,
      tab_key: props.tab,
    },
    urlBind: {
      project_id: props.projectId,
    },
  })

  dialog.loading({
    title: 'กรุณารอสักครู่...',
    description: 'กำลังส่งข้อมูล...',
  })
}

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    detail: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
  keepValuesOnUnmount: true,
})

const formFields = createFormFields(() => [
  {
    class: 'col-span-2',
    type: INPUT_TYPES.TEXTAREA,
    props: {
      label: '',
      name: 'detail',
      placeholder: ' ',
    },
  },
])

onMounted(() => {
  remark.run({
    params: {
      tab_key: props.tab,
    },
  })
})

const onOpenHistory = () => {
  historyModal.open({
    projectId: props.projectId,
    tab: props.tab,
  })
}

useWatchTrue(() => remark.status.value.isSuccess, async () => {
  form.setValues({
    ...remark.data.value,
  })
})

useWatchTrue(() => createRemark.status.value.isSuccess, async () => {
  dialog.close()
  noti.success({
    title: 'แก้ไขหมายเหตุ สำเร็จ',
  })

  remark.run({
    params: {
      tab_key: props.tab,
    },
  })
})

useWatchTrue(() => createRemark.status.value.isError, async () => {
  dialog.close()
  noti.error({
    title: 'แก้ไขหมายเหตุ ไม่สำเร็จ',
    description: StringHelper.getError(createRemark.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขหมายเหตุ กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
