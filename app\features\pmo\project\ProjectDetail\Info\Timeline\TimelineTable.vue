<template>
  <div class="space-y-10">
    <div>
      <div class="overflow-hidden border-0">
        <!-- Header Group -->
        <div class="grid grid-cols-7 rounded-lg bg-gray-50 text-center font-medium text-gray-700">
          <div class="border-r border-[#EAECF0] py-3" />
          <div class="col-span-3 flex justify-center border-r border-[#EAECF0] py-3">
            <div class="flex items-center gap-2 text-gray-700">
              <Icon
                name="f7:doc-plaintext"
                class="text-primary h-5 w-5"
              />
              <span class="font-semibold">เอกสารรับเข้า</span>
            </div>
          </div>
          <div class="col-span-3 flex justify-center py-3">
            <div class="flex items-center gap-2 text-gray-700">
              <Icon
                name="mynaui:arrow-up-circle"
                class="h-5 w-5 text-[#F79009]"
              />
              <span class="font-semibold">เอกสารส่งออก</span>
            </div>
          </div>
        </div>

        <!-- Sub Header -->
        <div class="grid grid-cols-7 border-b border-[#EAECF0] text-sm text-gray-600">
          <div class="border-r border-[#EAECF0] px-3 py-4">
            วันที่
          </div>
          <div class="col-span-2 border-[#EAECF0] px-3 py-4">
            เอกสารรับเข้า
          </div>
          <div class="border-r border-[#EAECF0] px-3 py-4">
            โดย
          </div>
          <div class="col-span-2 border-[#EAECF0] px-3 py-4">
            เอกสารส่งออก
          </div>
          <div class="px-3 py-4">
            โดย
          </div>
        </div>
        <FlexDeck
          :options="tableOptions"
          container-class=""
          @pageChange="items.fetchPageChange"
          @search="items.fetchSearch"
        >
          <template #default="{ row }: { row: IDocument }">
            <div
              class="text-nd grid grid-cols-7 border-b border-[#EAECF0] text-[#101828]"
            >
              <!-- วันที่ -->
              <div
                class="flex items-center justify-center border-r border-l border-[#EAECF0] px-3 py-2 whitespace-nowrap"
              >
                <div>
                  {{ row.date ? TimeHelper.displayDate(row.date) : 'N/A' }}
                </div>
              </div>

              <!-- เอกสารรับเข้า -->
              <div
                :class="row.type.toLowerCase() !== 'inbound' ? 'bg-[#F9FAFB]':''"
                class="col-span-2 flex items-center border-r border-[#EAECF0] px-3 py-2"
              >
                <div v-if="row.type.toLowerCase() === 'inbound'">
                  <div class="font-medium">
                    {{ row.name }}
                  </div>
                  <div class="flex items-center gap-1 text-xs text-gray-500">
                    <Button
                      variant="link"
                      size="md"
                      :label="row.file.name"
                      class="p-0 py-2 text-[#475467]"
                      icon="mdi:file-outline"
                      @click="download(row)"
                    />
                  </div>
                </div>
                <div
                  v-else
                  class="text-gray-400"
                >
                  -
                </div>
              </div>

              <!-- โดย -->
              <div
                :class="row.type.toLowerCase() !== 'inbound' ? 'bg-[#F9FAFB]':''"
                class="flex items-center justify-center border-r border-[#EAECF0] px-3 py-2"
              >
                <div v-if="row.type.toLowerCase() === 'inbound'">
                  <div class="flex flex-col items-center gap-2">
                    <Badge
                      variant="soft"
                      size="sm"
                      class="border border-[#D0D5DD] bg-[#ffffff] px-2 py-0.5"
                    >
                      <Avatar
                        :src="row.created_by?.avatar_url"
                        :alt="row.created_by?.display_name"
                        class="mr-1 h-5 w-5"
                        :ui="
                          {
                            image: 'object-cover border-0 border-[#ffffff]',
                          }
                        "
                      />
                      <span>
                        {{ row.created_by?.display_name }}
                      </span>
                    </Badge>
                    <Badge
                      variant="soft"
                      color="primary"
                      class="border border-violet-200 bg-violet-50 text-violet-700"
                    >
                      {{ row.tab_key }}
                    </Badge>
                  </div>
                </div>
                <div
                  v-else
                  class="text-gray-400"
                >
                  -
                </div>
              </div>

              <!-- เอกสารส่งออก -->
              <div
                :class="row.type.toLowerCase() === 'inbound' ? 'bg-[#F9FAFB]':''"
                class="col-span-2 flex items-center border-r border-[#EAECF0] px-3 py-2"
              >
                <div v-if="row.type.toLowerCase() !== 'inbound'">
                  <div class="font-medium">
                    {{ row.name }}
                  </div>
                  <div class="flex items-center gap-1 text-xs text-gray-500">
                    <Button
                      variant="link"
                      size="md"
                      :label="row.file.name"
                      class="p-0 py-2 text-[#475467]"
                      icon="mdi:file-outline"
                      @click="download(row)"
                    />
                  </div>
                </div>
                <div
                  v-else
                  class="text-gray-400"
                >
                  -
                </div>
              </div>

              <!-- โดย -->
              <div
                :class="row.type.toLowerCase() === 'inbound' ? 'bg-[#F9FAFB]':''"
                class="flex items-center justify-center border-r border-[#EAECF0] px-3 py-2"
              >
                <div v-if="row.type.toLowerCase() !== 'inbound'">
                  <div class="flex flex-col items-center gap-2">
                    <Badge
                      variant="soft"
                      size="sm"
                      class="border border-[#D0D5DD] bg-[#ffffff] px-2 py-0.5"
                    >
                      <Avatar
                        :src="row.created_by?.avatar_url"
                        :alt="row.created_by?.display_name"
                        class="mr-1 h-5 w-5"
                        :ui="
                          {
                            image: 'object-cover border-0 border-[#ffffff]',
                          }
                        "
                      />
                      <span>
                        {{ row.created_by?.display_name }}
                      </span>
                    </Badge>
                    <Badge
                      variant="soft"
                      color="primary"
                      class="border border-violet-200 bg-violet-50 text-violet-700"
                    >
                      {{ row.tab_key }}
                    </Badge>
                  </div>
                </div>
                <div
                  v-else
                  class="text-gray-400"
                >
                  -
                </div>
              </div>
            </div>
          </template>
        </FlexDeck>
        <!-- Rows -->
      </div>
    </div>
  </div>
</template>

<script lang="ts"  setup>
const props = defineProps<{
  items: any
}>()

const tableOptions = useFlexDeck<IDocument>({
  repo: props.items,
})

// utils
const download = async (data: any) => {
  const pathname = data.file.type
  const ext = pathname.split('/')[1].trim()

  await downloadDocument({
    url: data.file.url,
    method: 'GET',
    filename: data.file.name,
    ext: ext,
  })
}
</script>
