<template>
  <div class="mb-6">
    <form
      class="flex flex-col gap-4"
      @submit="onSubmit"
    >
      <FormFields
        :options="formFields"
      />
      <div class="flex items-center gap-2">
        <Button
          size="xl"
          class="w-fit"
          type="submit"
          @click="onSubmit"
        >
          Search
        </Button>
        <Button
          v-if="timesheet.fetch.items && timesheet.fetch.items.length > 0"
          label="Export CSV"
          size="xl"
          class="w-fit"
          variant="outline"
          icon="proicons:add-square-multiple"
          color="neutral"
          @click="exportToExcel"
        />
      </div>
    </form>
    <div
      v-if="timesheet.fetch.status.isLoaded"
      class="mt-4 mb-2 text-xl font-bold"
    >
      สรุปโครงการ เดือน
      {{ pickedMonth.toString().padStart(2, '0') }}/{{ pickedYear }}
      <br />
      <span class="text-lg"> รวมทั้งหมด: {{ totalProjectHour }} ชั่วโมง </span>
      <Table
        :options="tableOptions"
        @pageChange="timesheet.fetchPageChange"
        @project_code="timesheet.fetchSearch"
      >
        <template #name-cell="{ row }">
          <AvatarProfile :item="row.original.user" />
        </template>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useProjectsPageLoader } from '~/loaders/admin/project'
import { useSGAPageLoader } from '~/loaders/admin/sga'
import { useAdminTimeSheetPageLoader } from '~/loaders/admin/timesheet'
import { startOfMonth, endOfMonth, format } from 'date-fns'
import { useTimesheetItems } from '~/composables/useTimesheetItems'

const pickedMonth = ref(getCurrentMonth())
const pickedYear = ref(getCurrentYear())
const projects = useProjectsPageLoader()
const sga = useSGAPageLoader()
const timesheet = useAdminTimeSheetPageLoader()
const currentDate = ref(new Date())
const timesheetItems = useTimesheetItems(() => timesheet.fetch.items)
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    date: v.nullish(v.object({
      year: v.number(),
      month: v.number(),
    })),
    project_code: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกโปรเจค')), ''),
  })),
  initialValues: {
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },
})

projects.fetchSetLoading()
sga.fetchSetLoading()
timesheet.fetchSetLoading()
onMounted(() => {
  projects.fetchPage()
  sga.fetchPage()
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'project_code',
      placeholder: 'Select Project',
      required: true,
      label: 'Project',
      options: projectOptions.value || [],
    },
  },

])

const tableOptions = useTable<ITimesheet>({
  repo: timesheet,
  transformItems: () => timesheetItems.groupByProjectItems.value as any[],
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'timing',
      header: 'Hour',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'percent',
      header: 'Percent (%)',
    },
  ],
})

const totalProjectHour = computed(() => {
  return Object.values(timesheetItems.groupByProjectItems.value)
    .reduce((sum, val) => sum + Number(val.timing), 0)
    .toFixed(2)
})

const projectOptions = computed(() => {
  const normalProjects = (projects.fetch.items || []).map((p) => ({
    label: p.name,
    value: p.code,
  }))

  const sgaProjects = (sga.fetch.items || []).map((p) => ({
    label: p.name + ' (SGA)',
    value: p.id,
  }))

  return [
    {
      label: TRACKER_TYPE_LABEL[TRACKER_TYPE.EXTERNAL],
      value: TRACKER_TYPE.EXTERNAL,
    },
    {
      label: TRACKER_TYPE_LABEL[TRACKER_TYPE.INTERNAL],
      value: TRACKER_TYPE.INTERNAL,
    },
    ...normalProjects,
    ...sgaProjects,
  ]
})

const exportToExcel = () => {
  const header = ['User', 'Hour', 'Percent (%)']

  const rows = timesheetItems.groupByProjectItems.value.map((item) => {
    const row: (string | number)[] = [item.user.display_name, item.timing, item.percent]

    return row
  })

  const projectName = (projects.fetch.items || []).find((p) => p.code === form.values.project_code)?.name
  const filename = `report_${projectName}_${pickedMonth.value}_${pickedYear.value}.xlsx`

  QuickExcel.export(
    header,
    rows,
    filename,
  )
}

const onSubmit = form.handleSubmit((values) => {
  const params: Record<string, any> = {
    limit: 999,
    start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
    end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
  }

  if (values.project_code === TRACKER_TYPE.EXTERNAL || values.project_code === TRACKER_TYPE.INTERNAL) {
    params.type = values.project_code
  } else if ((sga.fetch.items || []).some((s: any) => s.id === values.project_code)) {
    params.sga_id = values.project_code
  } else if ((projects.fetch.items || []).some((p: any) => p.code === values.project_code)) {
    params.project_code = values.project_code
  }

  timesheet.fetchPage(1, '', {
    params,
  })
})

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
}, {
  deep: true,
})
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
