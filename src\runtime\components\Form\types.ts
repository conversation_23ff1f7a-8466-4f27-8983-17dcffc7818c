import type { Component } from '@nuxt/schema'
import type { FormContext } from 'vee-validate'
import type { IUploadDropzoneField } from './InputUploadDropzone/types'
import type { IUploadDropzoneAutoField } from './InputUploadDropzoneAuto/types'
import type { IDateTimeRangeField } from './InputDateTimeRange/date_range_time_field.types'
import type { ITextField } from '#core/components/Form/InputText/types'
import type { ISearchField } from '#core/components/Form/InputSearch/types'
import type { ITextareaField } from '#core/components/Form/InputTextarea/types'
import type { IToggleField } from '#core/components/Form/InputToggle/types'
import type { ISelectField } from '#core/components/Form/InputSelect/types'
import type { ICheckboxField } from '#core/components/Form/InputCheckbox/types'
import type { ISelectMultipleField } from '#core/components/Form/InputSelectMultiple/types'
import type { INumberField } from '#core/components/Form/InputNumber/types'
import type { IDateTimeField } from '#core/components/Form/InputDateTime/date_time_field.types'
import type { ITimeField } from '#core/components/Form/InputTime/types'
import type { IMonthField } from '#core/components/Form/InputMonth/types'
import type { IRadioField } from '#core/components/Form/InputRadio/types'
import type { IWYSIWYGField } from '#core/components/Form/InputWYSIWYG/types'
import type { IComponentField } from '#core/components/Form/InputComponent/types'
import type { ITagsField } from '#core/components/Form/InputTags/types'

export enum INPUT_TYPES {
  TEXT = 'TEXT',
  SEARCH = 'SEARCH',
  NUMBER = 'NUMBER',
  TEXTAREA = 'TEXTAREA',
  PASSWORD = 'PASSWORD',
  EMAIL = 'EMAIL',
  TAGS = 'TAGS',
  STATIC = 'STATIC',
  TOGGLE = 'TOGGLE',
  SELECT = 'SELECT',
  SELECT_MULTIPLE = 'SELECT_MULTIPLE',
  RADIO = 'RADIO',
  CHECKBOX = 'CHECKBOX',
  DATE_TIME = 'DATE_TIME',
  TIME = 'TIME',
  DATE = 'DATE',
  MONTH = 'MONTH',
  DATE_RANGE = 'DATE_RANGE',
  DATE_TIME_RANGE = 'DATE_TIME_RANGE',
  UPLOAD_FILE_CLASSIC = 'UPLOAD_FILE_CLASSIC',
  UPLOAD_FILE_CLASSIC_AUTO = 'UPLOAD_FILE_CLASSIC_AUTO',
  UPLOAD_IMAGE_AUTO = 'UPLOAD_IMAGE_AUTO',
  UPLOAD_DROPZONE = 'UPLOAD_DROPZONE',
  UPLOAD_DROPZONE_AUTO = 'UPLOAD_DROPZONE_AUTO',
  UPLOAD_DROPZONE_AUTO_MULTIPLE = 'UPLOAD_DROPZONE_AUTO_MULTIPLE',
  UPLOAD_DROPZONE_IMAGE_AUTO_MULTIPLE = 'UPLOAD_DROPZONE_IMAGE_AUTO_MULTIPLE',
  WYSIWYG = 'WYSIWYG',
  COMPONENT = 'COMPONENT',
}

export interface IFieldProps {
  form?: FormContext
  name: string
  errorMessage?: string
  label?: string | any
  description?: string
  hint?: string
  rules?: any
  autoFocus?: boolean
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  help?: string
  ui?: object | any
}

export interface IFormFieldBase<I extends INPUT_TYPES, P extends IFieldProps, O> {
  type: I
  component?: Component
  class?: any
  ui?: object | any
  isHide?: boolean
  props: P
  on?: O
}

export type IFormField
  = | ITextField
    | ISearchField
    | INumberField
    | ITextareaField
    | IToggleField
    | ISelectField
    | ICheckboxField
    | ISelectMultipleField
    | IRadioField
    | IDateTimeField
    | ITimeField
    | IMonthField
    | IDateTimeRangeField
    | IUploadDropzoneField
    | IUploadDropzoneAutoField
    | IWYSIWYGField
    | IComponentField
    | ITagsField
    | IFormFieldBase<INPUT_TYPES.COMPONENT, any, any>

export interface IFileValue {
  url: string
  path?: string
  name?: string
  size?: number
  id?: string
}
