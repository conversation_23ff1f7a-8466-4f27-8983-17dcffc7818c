<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไข Team' : 'เพิ่ม Team'"
    description="Teams info."
    :ui="{
      body: 'overflow-y-visible',
    }"
  >
    <template #body>
      <form
        @submit="onSubmit"
      >
        <FormFields
          :options="formFields"
        />
        <FormField
          label="เลือกสี"
          required
          class="mt-4"
          :error="form.submitCount.value > 0 ?form.errors.value.color : ''"
        >
          <div class="flex flex-wrap items-center gap-2">
            <div
              v-for="(tag, i) in colors"
              :key="i"
              class="flex min-w-[50px] cursor-pointer justify-center rounded-full border px-2 py-1 text-xs leading-3"
              :class="[
                form.values.color === tag.value
                  ? colorClasses[tag.value]
                  : `border-gray-200 text-${tag.value}-700`,
              ]"
              @click="form.setFieldValue('color', tag.value)"
            >
              {{ tag.label }}
            </div>
          </div>
        </FormField>
        <FormFields
          class="mt-4 grid gap-x-4 md:grid-cols-2"
          :options="formTimeFields"
        />
        <input
          type="submit"
          hidden
        />
      </form>
    </template>
    <template #footer>
      <div class="flex w-full justify-end gap-3">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ยกเลิก
        </Button>
        <Button
          :loading="status().isLoading"
          :disabled="!form.meta.value.dirty"
          @click="onSubmit"
        >
          {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: ITeam | null
  status: () => IStatus
  onSubmit: (values: ITeam) => void
}>()

const formatTimeString = (time?: string) => {
  if (!time) return ''
  const parts = time.split(':').map(Number)
  const hours = String(parts[0] || 0).padStart(2, '0')
  const minutes = String(parts[1] || 0).padStart(2, '0')

  return `${hours}:${minutes}`
}

const form = useForm({
  initialValues: {
    ...props.values,
    working_end_at: formatTimeString(props.values?.working_end_at),
    working_start_at: formatTimeString(props.values?.working_start_at),
  },
  validationSchema: toTypedSchema(v.object({
    name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    code: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    color: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกสี')), ''),
    working_start_at: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    working_end_at: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const colors = [
  {
    label: 'Red',
    value: 'red',
  },
  {
    label: 'Pink',
    value: 'pink',
  },
  {
    label: 'Orange',
    value: 'orange',
  },
  {
    label: 'Yellow',
    value: 'yellow',
  },
  {
    label: 'Green',
    value: 'green',
  },
  {
    label: 'Blue',
    value: 'blue',
  },
  {
    label: 'Violet',
    value: 'violet',
  },
]

const formFields = createFormFields(() => [

  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อทีม (Name)',
      name: 'name',
      placeholder: ' ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'โค้ดทีม (Code)',
      name: 'code',
      placeholder: ' ',
      required: true,
    },
  },
])

const formTimeFields = createFormFields(() => [

  {
    type: INPUT_TYPES.TIME,
    props: {
      label: 'เวลาเริ่มทำงาน (Start Working At)',
      name: 'working_start_at',
      placeholder: ' ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TIME,
    props: {
      label: 'เวลาสิ้นสุดการทำงาน (End Working At)',
      name: 'working_end_at',
      placeholder: ' ',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as ITeam)
})
</script>
