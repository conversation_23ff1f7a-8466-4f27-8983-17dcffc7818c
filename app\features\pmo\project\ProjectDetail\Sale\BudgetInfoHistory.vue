<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="Budget History"
    description="view change history"
    :ui="{
      content: 'max-w-2xl',
    }"
  >
    <template #body>
      <div class="space-y-5">
        <div
          v-for="value in history.fetch.items"
          :key="value.detail"
          class="rounded-md bg-[#F2F4F7] p-2"
        >
          <InfoItemList
            class="mt-4"
            :items="[
              {
                value: value.project_value,
                label: 'มูลค่าโครงการ (บาท)',
              },
              {
                value: value.fund_type,
                label: 'ประเภทงบ',

              },
              {
                value: value.bidbond_value,
                label: 'ยอดเงินค้ำประกัน',

              },
              {
                value: value.partner,
                label: 'คู่สัญญา',
              }]"
          />
          <div class="mt-3 text-xs font-bold">
            {{ TimeHelper.displayDateTime(value.created_at) }}
            <span v-if="value.updated_by"> by {{ value.updated_by.display_name }}</span>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex w-full justify-end">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ปิด
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { useProjectBudgetVersionLoader } from '~/loaders/pmo/project'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  projectId: string
  tab: string
}>()

const history = useProjectBudgetVersionLoader(props.projectId as string)

onMounted(() => {
  history.fetchPage(1, '', {
    params: {
      tab_key: props.tab,
      limit: 999,
    },
  })
})
</script>
