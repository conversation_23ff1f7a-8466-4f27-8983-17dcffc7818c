<template>
  <FieldWrapper v-bind="wrapperProps">
    <InputTags
      :model-value="value"
      :disabled="wrapperProps.disabled"
      :leading-icon="leadingIcon"
      :max-length="maxLength"
      :varant="variant"
      :delete-icon="deleteIcon"
      :size="size"
      :trailing-icon="trailingIcon"
      :loading="loading"
      :loading-icon="loadingIcon"
      :name="name"
      :placeholder="wrapperProps.placeholder"
      :autofocus="!!autoFocus"
      :icon="icon"
      :readonly="readonly"
      :ui="ui"
      @update:model-value="onChange"
      @addTag="onAdd"
      @removeTag="onRemove"
    />
  </FieldWrapper>
</template>

<script lang="ts" setup>
import type { ITagsFieldProps } from '#core/components/Form/InputTags/types'
import { useFieldHOC } from '#core/composables/useForm'
import FieldWrapper from '#core/components/Form/FieldWrapper.vue'

const emits = defineEmits(['change', 'add', 'remove'])
const props = defineProps<ITagsFieldProps>()

const {
  value, wrapperProps, handleChange,
} = useFieldHOC<string[]>(props)

const onChange = (value: string[]) => {
  handleChange(value)
  emits('change', value)
}

const onAdd = (value: string) => {
  emits('add', value)
}

const onRemove = (value: string) => {
  emits('remove', value)
}
</script>
