import { defineEventHandler, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = {
    id: '550e8400-e29b-41d4-a716-446655440000',
    code: 'FNM20250312',
    name: 'ระบบงานส่วนกลางบริษัท FineWork',
    client: 'บริษัทฟินีม่า',
    startDate: '2568-05-22T00:00:00+07:00',
    endDate: '2569-05-20T23:59:59+07:00',
    status: 'DRAFT',
  }

  return {
    ...items,
  }
})
