<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขเอกสาร' : 'เพิ่มเอกสาร'"
    description="Document"
    :ui="{
      body: 'overflow-y-visible',
    }"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="isLoading"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: any | null
  status: () => IStatus
  onSubmit: (values: any) => void

}>()

const isLoading = ref(false)

const fileFromMeta = async (meta: {
  name: string
  url: string
  type: string
}): Promise<File> => {
  const content = 'This is a mock file' // หรือ ArrayBuffer / Uint8Array ก็ได้
  const blob = new Blob([content], {
    type: meta.type,
  })

  const file = new File([blob], meta.name, {
    type: meta.type,
  })

  return file
}

const form = useForm({
  initialValues: {
    ...props.values,
    date: props.values?.date ? new Date(props.values?.date) : '',
  },
  validationSchema: toTypedSchema(v.object({
    name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    sharepoint_url: v.optional(
      v.pipe(
        v.string(),
        v.nonEmpty('กรุณากรอก SharePoint Link'),
        v.regex(
          /^https:\/\/finemaco\.sharepoint\.com\/.+/,
          'ลิงก์ต้องขึ้นต้นด้วย https://finemaco.sharepoint.com/',
        ),
      ),
      '',
    ),
    file: v.optional(v.object({
      url: v.string(),
      name: v.string(),
      path: v.string(),
      size: v.number(),
      id: v.string(),
    })),
    date: v.pipe(v.date('กรุณาลงวันที่เอกสาร (Document Date)')),
    type: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อเอกสาร (Document Name)',
      name: 'name',
      placeholder: 'ระบุชื่อเอกสาร',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'SharePoint Link',
      name: 'sharepoint_url',
      placeholder: 'https://finemaco.sharepoint.com/:f:/s/finema/Eus6EmWdR2hNijaIHHd5U2sBMBobVe3TN2j4eI...',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.DATE_TIME,
    props: {
      label: 'ลงวันที่เอกสาร (Document Date)',
      name: 'date',
      placeholder: 'ลงวันที่เอกสาร',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.UPLOAD_DROPZONE_AUTO,
    props: {
      required: true,
      label: 'ไฟล์เอกสาร (File)',
      name: 'file',
      placeholder: 'ไฟล์เอกสาร (File)',
      requestOptions: useRequestOptions().file(),
      accept: '.jpg,.jpeg,.png,.gif,.webp,.pdf,.doc,.docx,.xls,.xlsx,.txt',
    },
  },
  {
    type: INPUT_TYPES.RADIO,
    props: {
      label: 'รูปแบบเอกสาร',
      name: 'type',
      options: DOCUMENT_TYPE_OPTIONS,
      placeholder: 'รูปแบบเอกสาร ขนาดเอกสารสูงสุด 10MB',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  isLoading.value = true
  props.onSubmit({
    ...values,
    file_id: values.file?.id,
  })
})
</script>
