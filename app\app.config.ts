import { SITE } from './constants/site'

export default defineAppConfig({
  core: {
    site_name: SITE.TITLE,
    is_thai_year: false,
    is_thai_month: false,
    date_format: 'dd MMM yyyy',
    month_format: 'MMM yyyy',
    date_time_format: 'dd/MM/yyyy HH:mm น.',
    color: '#335AFF',
    limit_per_page: 15,
    locale: 'en',
  },
  ui: {
    colors: {
      white: 'white',
    },
    dialog: {
      icons: {
        iconInfo: 'material-symbols:info-outline',
        iconConfirm: 'material-symbols:info-outline',
      },
      slots: {
        iconWrapper: 'rounded-full size-[48px] flex justify-center items-center',
        icon: 'size-6',
        base: 'rounded-xl flex-col justify-start items-start space-x-0 bg-[url(/dialog-bg.png)] bg-no-repeat bg-top-left bg-size-[220px]',
        overlay: 'bg-black/50 backdrop-blur',
        description: 'whitespace-pre-line',
        wrapper: 'justify-start items-start',
        title: 'mt-4',
        buttonGroup: 'justify-center w-full mt-6',
        cancelButton: 'w-full justify-center',
        confirmButton: 'w-full justify-center',
      },
      variants: {
        color: {
          success: {
            icon: 'text-success',
            iconWrapper: 'bg-success/10',
          },
          info: {
            icon: 'text-info',
            iconWrapper: 'bg-info/10',
          },
          warning: {
            icon: 'text-warning',
            iconWrapper: 'bg-warning/10',
          },
          error: {
            icon: 'text-error',
            iconWrapper: 'bg-error/10',
          },
          loading: {
            icon: 'text-primary size-10',
            base: 'max-w-[400px] bg-none',
            wrapper: 'justify-center',
          },
        },
        confirm: {
          true: {},
          false: {},
        },
      },
    },
    slideover: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
      },
    },
    toast: {
      slots: {
        root: 'rounded-xl',
        icon: 'size-8',
      },
    },
    card: {
      slots: {
        root: '!bg-white',
        header: 'text-lg font-bold',
      },
      variants: {
        variant: {
          soft: {
            root: 'bg-elevated/50 divide-y divide-default shadow-[0px_2px_14px_0px_rgba(0,0,0,0.06)]',
          },
        },
      },
    },
    switch: {
      slots: {
        base: 'cursor-pointer',
        label: 'cursor-pointer',
      },
    },
    breadcrumb: {
      variants: {
        active: {
          true: {
            link: 'text-black font-semibold',
          },
        },
      },
    },
    tabs: {
      slots: {
        trigger: 'data-[state=active]:font-bold cursor-pointer',
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    modal: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
        title: 'text-xl font-bold',
        content: 'divide-none overflow-y-visible',
        body: 'sm:pt-0 pt-0 ',
        footer: 'w-full',
      },
      variants: {
        fullscreen: {
          false: {
            content: 'overflow-visible',
          },
        },
      },
    },
    pagination: {
      slots: {
        first: 'disabled:hidden',
        prev: 'disabled:hidden',
        next: 'disabled:hidden',
        last: 'disabled:hidden',
      },
    },
    table: {
      slots: {
        root: 'rounded-t-md rounded-b-md bg-white',
        captionContainer: 'hidden',
        paginationInfo: 'text-gray-600',
        paginationContainer: 'items-center flex-col lg:flex-row gap-4',
        thead: 'bg-primary',
        th: 'text-[#222222] bg-white whitespace-nowrap',
        td: 'text-[#222222]',
      },
      variants: {
        pinned: {
          true: {
            th: 'sticky bg-gray-100 data-[pinned=left]:left-0 data-[pinned=right]:right-0 z-[100] ',
            td: 'sticky bg-gray-50 data-[pinned=left]:left-0 data-[pinned=right]:right-0   ',
          },
        },
        sticky: {
          true: {
            thead: 'sticky top-0 inset-x-0 bg-primary z-[1] backdrop-blur',
            tfoot: 'sticky bottom-0 inset-x-0 bg-white z-[1] backdrop-blur',
          },
        },
      },
    },
    formField: {
      slots: {
        label: 'font-bold',
      },
    },
    input: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    inputNumber: {
      variants: {
        size: {
          xl: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    inputTags: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed w-full',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    dateTime: {
      slots: {
        clearIcon: 'size-6 mr-3',
      },
    },
    selectMenu: {
      slots: {
        base: 'cursor-pointer w-full',
        item: 'cursor-pointer',
        clearIcon: 'size-6',
      },
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    textarea: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    button: {
      slots: {
        base: ['rounded-lg cursor-pointer'],
      },
      variants: {
        size: {
          xl: {
            base: 'py-2.5 font-semibold',
          },
        },
      },
      compoundVariants: [
        {
          color: 'white',
          variant: 'solid',
          class: 'text-black',
        },
      ],
      defaultVariants: {
        size: 'xl',
      },
    },
    stepper: {
      variants: {
        size: {
          xs: {
            trigger: 'size-8',
            icon: 'size-6',
            title: 'text-base font-bold',
            description: 'text-sm',
            wrapper: 'mt-1.5',
          },
        },
      },
      defaultVariants: {
        size: 'xs',
      },
    },
    dropdownMenu: {
      slots: {
        item: 'cursor-pointer',
      },
    },
  },
})
