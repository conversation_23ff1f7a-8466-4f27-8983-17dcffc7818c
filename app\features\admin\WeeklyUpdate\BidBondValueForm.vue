<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="แก้ไข ยอดเงินค้ำประกัน"
    description="Bidbond value info."
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <input
          type="submit"
          hidden
        />
      </form>
    </template>
    <template #footer>
      <div class="flex w-full justify-end gap-3">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ยกเลิก
        </Button>
        <Button
          :loading="status().isLoading"
          :disabled="!form.meta.value.dirty"
          @click="onSubmit"
        >
          บันทึก
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: IBidbondInfo | null
  status: () => IStatus
  onSubmit: (values: IBidbondInfo) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    bidbond_value: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      label: 'ยอดเงินค้ำประกัน',
      name: 'bidbond_value',
      placeholder: 'กรุณากรอกยอดเงินค้ำประกัน',
      required: true,
      formatOptions: {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      },
      step: 0.01,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as IBidbondInfo)
})
</script>
