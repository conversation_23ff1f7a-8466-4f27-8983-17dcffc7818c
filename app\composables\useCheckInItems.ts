import {
  format,
  parseISO,
  isAfter,
} from 'date-fns'

export const useCheckInItems = (checkins: () => ICheckinItem[]) => {
  const groupByTypeItems = computed(() => {
    const groups: Record<string, ICheckinItem[]> = {}
    const unusedFlags: Record<string, boolean> = {}

    for (const item of checkins()) {
      const key = format(item.date, 'yyyy-MM-dd')

      if (item.is_unused === true) {
        unusedFlags[key] = true
        continue
      }

      if (!groups[key]) groups[key] = []
      groups[key].push(item as ICheckinItem)
    }

    return Object.entries(groups).map(([date, items]) => {
      return {
        date,
        types: items.map((i) => i.type),
        original_items: items,
        display_name: items?.at(0)?.user?.display_name,
        location: items?.at(0)?.location,
        created_at: items?.at(0)?.created_at,
        is_unused: unusedFlags[date],
      }
    })
  })

  const groupByDateItems = computed(() => {
    const result: Record<
      string,
      {
        display_name: string
        team: any
        avatar_url: string
        detail: Array<{
          date: string
          type: string[]
          is_late: boolean
        }>
        ontime: number
        latetime: number
      }
    > = {}

    if (!checkins()) return []

    for (const item of checkins()) {
      const display_name
        = item.user?.display_name || item.user.email.replace('@finema.co', '').toUpperCase()

      const team = item.user?.team
      const date = format(item.date, 'yyyy-MM-dd')
      const avatar_url = item.user?.avatar_url
      const isLate = isAfter(parseISO(item.created_at), parseISO(item.date))

      if (!result[display_name]) {
        result[display_name] = {
          display_name,
          team,
          avatar_url,
          detail: [],
          ontime: 0,
          latetime: 0,
        }
      }

      const existingDetail = result?.[display_name]?.detail.find((d) => d.date === date)

      if (existingDetail) {
        if (!existingDetail.type.includes(item.type)) {
          existingDetail.type.push(item.type)
        }

        if (isLate) {
          existingDetail.is_late = true
        }
      } else {
        result[display_name]?.detail.push({
          date: format(date, 'yyyy-MM-dd'),
          type: [item.type],
          is_late: isLate,
        })
      }
    }

    for (const person of Object.values(result)) {
      // เรียงวันที่จากน้อย -> มาก
      person.detail.sort((a, b) =>
        parseISO(a.date).getTime() - parseISO(b.date).getTime(),
      )

      for (const entry of person.detail) {
        if (entry.is_late) {
          person.latetime += 1
        } else {
          person.ontime += 1
        }
      }
    }

    return Object.values(result).sort((a, b) => {
      const nameA = a.display_name.toLowerCase()
      const nameB = b.display_name.toLowerCase()

      if (nameA === nameB) {
        return a.team.localeCompare(b.team)
      }

      return nameA.localeCompare(nameB)
    })
  })

  return {
    groupByTypeItems,
    groupByDateItems,
  }
}
