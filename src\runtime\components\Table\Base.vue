<template>
  <div
    v-if="!isHideCaption"
    class="mb-4 text-gray-500"
  >
    <span class="font-bold">ผลลัพธ์ทั้งหมด:</span>
    จำนวน
    <span class="font-bold">{{ pageOptions?.totalCount || rawData.length || 0 }}</span>
    รายการ
  </div>
  <UTable
    :loading="status.isLoading"
    :columns="uTableCompatibleColumns"
    :data="rawData"
    v-bind="$attrs"
  >
    <template #loading-state>
      <div class="flex h-60 items-center justify-center">
        <Icon
          name="i-svg-spinners:180-ring-with-bg"
          class="text-primary size-8"
        />
      </div>
    </template>
    <template #empty-state>
      <Empty />
    </template>
    <template
      v-for="column in columns.filter((item) => !!item.type)"
      #[`${column.accessorKey}-cell`]="{ row }"
      :key="column.accessorKey"
    >
      <component
        :is="column.type === COLUMN_TYPES.COMPONENT ? column.component : columnTypeComponents[column.type!]"
        v-if="column.type === COLUMN_TYPES.COMPONENT || columnTypeComponents[column.type!]"
        :value="transformValue(column, row)"
        :column="column"
        :row="row"
      />
    </template>
    <template
      v-for="(_, slotName) of $slots"
      #[slotName]="slotProps"
    >
      <slot
        :name="slotName"
        v-bind="slotProps || {}"
      />
    </template>
  </UTable>
  <div
    v-if="!isHidePagination && pageOptions"
    :class="theme.paginationContainer({})"
  >
    <p
      :class="theme.paginationInfo({})"
    >
      {{ pageBetween }} รายการ จากทั้งหมด {{ totalCountWithComma }} รายการ
    </p>
    <Pagination
      v-if="pageOptions.totalPage > 1 "
      v-model:page="page"
      :default-page="pageOptions?.currentPage || 1"
      :items-per-page="pageOptions.limit"
      :total="pageOptions.totalCount"
      @update:page="onPageChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, type PropType } from 'vue'
import { COLUMN_TYPES, type ITableOptions, type TableColumn } from '#core/components/Table/types'
import ColumnNumber from '#core/components/Table/ColumnNumber.vue'
import ColumnImage from '#core/components/Table/ColumnImage.vue'
import { NumberHelper } from '#core/utils/NumberHelper'
import { ref, useUiConfig, watch } from '#imports'
import ColumnDateTime from '#core/components/Table/ColumnDateTime.vue'
import Empty from '#core/components/Empty.vue'
import ColumnDate from '#core/components/Table/ColumnDate.vue'
import ColumnText from '#core/components/Table/ColumnText.vue'
import { useWatchChange } from '#core/composables/useWatch'
import UTable from '#ui/components/Table'
import { tableTheme } from '#core/theme/table'
import type { TableRow } from '@nuxt/ui'

const columnTypeComponents = {
  [COLUMN_TYPES.NUMBER]: ColumnNumber,
  [COLUMN_TYPES.IMAGE]: ColumnImage,
  [COLUMN_TYPES.DATE_TIME]: ColumnDateTime,
  [COLUMN_TYPES.DATE]: ColumnDate,
  [COLUMN_TYPES.TEXT]: ColumnText,
}

const emits = defineEmits(['pageChange'])

const props = defineProps({
  status: {
    type: Object as PropType<ITableOptions['status']>,
    required: true,
  },
  pageOptions: {
    type: Object as PropType<ITableOptions['pageOptions']>,
    required: false,
  },
  columns: {
    type: Array as PropType<ITableOptions['columns']>, // This resolves to TableColumn[]
    required: true,
  },
  rawData: {
    type: Array as PropType<ITableOptions['rawData']>,
    required: true,
  },
  isHidePagination: {
    type: Boolean as PropType<ITableOptions['isHidePagination']>,
    default: false,
  },
  isHideCaption: {
    type: Boolean as PropType<ITableOptions['isHideCaption']>,
    default: false,
  },
})

const page = ref(props.pageOptions?.currentPage || 1)
const theme = computed(() => useUiConfig(tableTheme, 'table')())

const uTableCompatibleColumns = computed(() =>
  props.columns.map((col) => ({
    ...col,
    key: col.accessorKey, // Use accessorKey for UTable's key property
  })),
)

useWatchChange(
  () => props.pageOptions?.currentPage,
  (value: number) => {
    page.value = value
  },
)

const pageBetween = computed((): string => {
  const length = props.rawData?.length

  if (length === 0) {
    return '0'
  }

  const start = (props.pageOptions!.currentPage - 1) * props.pageOptions!.limit + 1
  const end = start + length - 1

  return `${start} - ${end}`
})

const transformValue = (column: TableColumn, row: TableRow<any>) => {
  return column.cell
    ? column.cell({
      row,
    })
    : row.getValue(column.accessorKey)
}

const totalCountWithComma = computed((): string => {
  return !props.pageOptions?.totalCount
    ? '0'
    : NumberHelper.withComma(props.pageOptions!.totalCount)
})

watch(page, () => {
  emits('pageChange', page.value)
})
</script>
