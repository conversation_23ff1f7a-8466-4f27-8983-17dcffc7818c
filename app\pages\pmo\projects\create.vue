<template>
  <NuxtLayout>
    <PmoForm />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import PmoForm from '~/features/pmo/project/ProjectCreate/index.vue'

definePageMeta({
  layout: 'pmo',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.pmo.project.projectCreate.permissions,
  },
})

useSeoMeta({
  title: routes.pmo.project.projectCreate.label,
})

useApp().definePage({
  title: routes.pmo.project.projectCreate.label,
  breadcrumbs: [routes.pmo.project.projects, routes.pmo.project.projectCreate],
  sub_title: 'Create Project',

})
</script>
