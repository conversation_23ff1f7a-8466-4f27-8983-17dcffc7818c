<template>
  <div class="relative flex min-h-screen flex-1">
    <div
      :class="[`
        fixed inset-0 z-30 hidden w-auto
        lg:block
      `, {
        'max-w-[88px]': isCollapsed,
        'max-w-[260px]': !isCollapsed,
      }]"
    >
      <Sidebar
        :is-collapsed="isCollapsed"
        @toggle-collapsed="isCollapsed = !isCollapsed"
      />
    </div>
    <div
      :class="['w-full bg-gray-50', {
        'lg:pl-[88px]': isCollapsed,
        'lg:pl-[260px]': !isCollapsed,
      }]"
    >
      <nav
        class="
          fixed top-0 left-0 z-20 flex min-h-[72px] w-screen items-center
          justify-between gap-4 bg-white px-5
          lg:justify-end
        "
        style="box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);"
      >
        <div class="flex items-center gap-4">
          <Slideover
            v-model:open="isShowSidebarMobile"
            :ui="{
              content: 'w-[80%] max-w-[260px] lg:hidden',
              overlay: 'lg:hidden',
            }"
            side="left"
          >
            <svg
              class="
                cursor-pointer
                lg:hidden
              "
              width="19"
              height="18"
              viewBox="0 0 19 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              transform="scale(-1, 1)"
              @click="isShowSidebarMobile = true"
            >
              <path
                d="M10 18L10 16L19 16L19 18L10 18ZM6 2L6 -2.62268e-07L19 -8.30516e-07L19 2L6 2ZM-3.49691e-07 10L-4.37114e-07 8L19 8L19 10L-3.49691e-07 10Z"
                fill="#4B5563"
              />
            </svg>

            <template #content>
              <Sidebar
                :is-collapsed="false"
                :is-mobile="true"
                @toggle-collapsed="isShowSidebarMobile = false"
              />
            </template>
          </Slideover>
          <NuxtLink :to="routes.home.to">
            <img
              src="/logo-mini.png"
              alt="logo_mini_color"
              class="
                h-[20px]
                lg:hidden
              "
            />
          </NuxtLink>
        </div>

        <div class="flex items-center justify-center gap-4">
          <DropdownMenu
            size="xl"
            :items="userMenuItems"
            :content="{
              align: 'end',
              side: 'bottom',
            }"
            :ui="{
              content: 'w-48',
            }"
          >
            <div class="relative flex cursor-pointer items-center gap-2">
              <div
                class="
                  hidden flex-col justify-end text-right
                  md:flex
                "
              >
                <p class="font-bold">
                  {{ auth.me.value?.display_name || auth.me.value?.full_name }}
                </p>
                <p class="text-muted text-sm">
                  {{ auth.me.value?.email || '' }}
                </p>
              </div>
              <Avatar
                class="border-muted size-[40px] border text-2xl"
                icon="ri:user-line"
                :src="auth.me.value?.avatar_url || ''"
              />
              <Icon
                name="i-ph:caret-down-light"
                class="size-5"
              />
            </div>
          </DropdownMenu>
        </div>
      </nav>
      <div class="w-full bg-gray-50 pt-[72px]">
        <main
          class="
            mx-auto min-h-full w-full max-w-7xl flex-1 px-6 py-10
            2xl:max-w-8/10
          "
        >
          <Breadcrumb
            v-if="!app.pageMeta.isHideBreadcrumbs && breadcrumbsItems.length > 1"
            :items="breadcrumbsItems"
            class="mb-6"
            :ui="{
              item: 'max-w-2/3',
              list: 'w-full',
            }"
          />
          <div
            v-if="app.pageMeta.title"
            class="
             mb-4 flex flex-col justify-between gap-1 md:mb-6 md:gap-4
              lg:flex-row lg:items-start
            "
          >
            <div class="flex flex-1 flex-col">
              <h1
                class="
                  text-3xl font-bold wrap-break-word
                  lg:max-w-2/3
                "
                :title="app.pageMeta.title"
              >
                {{ app.pageMeta.title }}
                <span id="page-title-extra" />
              </h1>

              <div id="page-subtitle" />
              <p
                v-if="app.pageMeta.sub_title"
                class="text-[#475467]"
              >
                {{ app.pageMeta.sub_title }}
              </p>
            </div>
            <div id="page-header" />
          </div>
          <slot />
        </main>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { DropdownMenuItem } from '@nuxt/ui'
import { computed } from 'vue'
import Sidebar from './components/Sidebar.vue'
import { routes } from '~/constants/routes'

const app = useApp()
const isShowSidebarMobile = ref(false)
const auth = useAuth()
// Cookie to store user preference for desktop
const isCollapsed = useCookie<boolean>('app.admin.sidebar.isCollapsed', {
  default: () => false,
  path: '/',
})

const userMenuItems: DropdownMenuItem[] = [
  {
    label: 'View profile',
    icon: 'i-lucide-user',
    to: routes.account.profile.to,
  },

  {
    label: routes.logout.label,
    icon: 'i-lucide-log-out',
    to: routes.logout.to,
    external: true,
  },
]

const breadcrumbsItems = computed(() => [
  // {
  //   label: '',
  //   icon: 'ph:house',
  //   to: '/',
  // },
  ...(app.pageMeta.breadcrumbs || []).map((item: any) => ({
    ...item,
    icon: '',
  })),
])
</script>

<style>
:root {
 --color-background: var(--color-gray-50);
}
</style>
