<template>
  <div
    class="flex flex-col"
  >
    <FormFields
      :form="form"
      :options="formFields"
      class="mb-4"
    />

    <Table
      :options="tableOptions"
      :ui="{
        th: 'text-[#222222] bg-white whitespace-nowrap border-r border-gray-200 last:border-0',
        td: 'text-[#222222] border-r border-gray-200 last:border-0',
        tr: 'odd:bg-white even:bg-gray-100',
      }"
      sticky
      class="max-h-[calc(100vh-150px)] md:max-h-[calc(100vh-340px)] lg:max-h-[calc(100vh-320px)]"
      @pageChange="project.fetchPageChange"
      @search="project.fetchSearch"
    >
      <template #name-cell="{ row }">
        <div
          class="text-sm font-medium"
        >
          {{ row.original.name }}
        </div>
        <AvatarProfile
          v-if="row.original.collaborators?.find((item:any) => item.sales_main)?.user"
          size="sm"
          class="mt-1"
          :item="row.original.collaborators?.find((item:any) => item.sales_main)?.user"
        />
      </template>
      <template #bidding_info_bidding_value-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer text-right"
          @click="bidingValueModal.open({
            values: row.original.bidding_info,
            status: () => createBidding?.status.value,
            onSubmit: (values) => {
              createBidding?.run({
                data: {
                  ...row.original.bidding_info,
                  tender_date: row.original.bidding_info?.tender_date
                    ? format(row.original.bidding_info?.tender_date, 'yyyy-MM-dd')
                    : undefined,
                  announce_date: row.original.bidding_info?.announce_date
                    ? format(row.original.bidding_info?.announce_date, 'yyyy-MM-dd')
                    : undefined,
                  bidding_value: values.bidding_value,
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ formatNumberUnit(row.original.bidding_info?.bidding_value) || '-' }}
        </div>
      </template>
      <template #lg_info_value-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer text-right"
          @click="lgValueModal.open({
            values: row.original.lg_info,
            status: () => createLG?.status.value,
            onSubmit: (values) => {
              createLG?.run({
                data: {
                  ...row.original.lg_info,
                  start_date: row.original?.lg_info.start_date
                    ? format(row.original.lg_info.start_date, 'yyyy-MM-dd')
                    : undefined,
                  end_date: row.original.lg_info.end_date
                    ? format(row.original.lg_info.end_date, 'yyyy-MM-dd')
                    : undefined,
                  value: values.value,
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ formatNumberUnit(row.original.lg_info?.value) || '-' }}
        </div>
      </template>

      <template #budget_info_fund_type-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer"
          @click="budgetFundTypeModal.open({
            values: row.original.budget_info,
            status: () => createBudget?.status.value,
            onSubmit: (values) => {
              createBudget?.run({
                data: {
                  ...row.original.budget_info,
                  fund_type: values.fund_type,
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ row.original.budget_info?.fund_type || '-' }}
        </div>
      </template>

      <template #bidding_info_bidding_type-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer"
          @click="biddingTypeModal.open({
            values: row.original.bidding_info,
            status: () => createBidding?.status.value,
            onSubmit: (values) => {
              createBidding?.run({
                data: {
                  ...row.original.bidding_info,
                  tender_date: row.original.bidding_info?.tender_date
                    ? format(row.original.bidding_info?.tender_date, 'yyyy-MM-dd')
                    : undefined,
                  announce_date: row.original.bidding_info?.announce_date
                    ? format(row.original.bidding_info?.announce_date, 'yyyy-MM-dd')
                    : undefined,
                  bidding_type: values.bidding_type,
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ row.original.bidding_info?.bidding_type || '-' }}
        </div>
      </template>
      <template #lg_info_start_date-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer"
          @click="lgDateModal.open({
            values: row.original.lg_info,
            status: () => createLG?.status.value,
            onSubmit: (values) => {
              createLG?.run({
                data: {
                  ...row.original.lg_info,
                  start_date: format(values.start_date, 'yyyy-MM-dd'),
                  end_date: format(values.end_date, 'yyyy-MM-dd'),
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ row.original.lg_info?.start_date
            ? TimeHelper.displayDate(row.original.lg_info?.start_date)
            : '-' }}
        </div>
      </template>
      <template #budget_info_partner-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer"
          @click="budgetPartnerModal.open({
            values: row.original.budget_info,
            status: () => createBudget?.status.value,
            onSubmit: (values) => {
              createBudget?.run({
                data: {
                  ...row.original.budget_info,
                  partner: values.partner,
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ row.original.budget_info?.partner || '-' }}
        </div>
      </template>
      <template #remark-cell="{ row }">
        <div
          class="min-h-5 w-full cursor-pointer"
          @click="remarkValueModal.open({
            values: row.original.remarks?.find((r:any) => r.tab_key === PROJECT_TAB_TYPE.BIDDING)?.detail,
            status: () => createRemark?.status.value,
            onSubmit: (values) => {
              createRemark?.run({
                data: {
                  detail: values.detail,
                  tab_key: PROJECT_TAB_TYPE.BIDDING,
                },
                urlBind: {
                  project_id: row.original.id,
                },
              });
            },
          })"
        >
          {{ row.original.remarks?.find((r:any) => r.tab_key === PROJECT_TAB_TYPE.BIDDING)?.detail || '-' }}
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { watchDebounced } from '@vueuse/core'
import { usePmoProjectsPageLoader, useProjectCreateBiddingLoader, useProjectCreateRemarkLoader, useProjectCreateBudgetLoader, useProjectCreateLGLoader } from '~/loaders/pmo/project'
import { format } from 'date-fns'
import BiddingValueForm from '../BiddingValueForm.vue'
import BudgetFundTypeForm from '../BudgetFundTypeForm.vue'
import BiddingTypeForm from '../BiddingTypeForm.vue'
import RemarkValueForm from '../RemarkValueForm.vue'
import BudgetPartnerForm from '../BudgetPartnerForm.vue'
import LgDateForm from '../LgDateForm.vue'
import LgValueForm from '../LgValueForm.vue'

const Button = resolveComponent('Button')
const project = usePmoProjectsPageLoader()
const noti = useNotification()
const createBidding = useProjectCreateBiddingLoader()
const createLG = useProjectCreateLGLoader()
const createBudget = useProjectCreateBudgetLoader()
const createRemark = useProjectCreateRemarkLoader()
const overlay = useOverlay()
const bidingValueModal = overlay.create(BiddingValueForm)
const biddingTypeModal = overlay.create(BiddingTypeForm)
const budgetFundTypeModal = overlay.create(BudgetFundTypeForm)
const budgetPartnerModal = overlay.create(BudgetPartnerForm)
const remarkValueModal = overlay.create(RemarkValueForm)
const lgDateModal = overlay.create(LgDateForm)
const lgValueModal = overlay.create(LgValueForm)
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหา',
    },
  },
])

watchDebounced(form.values, (values) => {
  project.fetchSearch(values.q || '', {
    params: {
      include_all: true,
    },
  })
}, {
  debounce: 300,
  deep: true,
})

const summaryRow = computed(() => {
  if (!project.fetch.items?.length) return null

  const totalBiddingValue = project.fetch.items.reduce(
    (sum, it) => sum + (Number(it.bidding_info?.bidding_value) || 0),
    0,
  )

  const totalValue = project.fetch.items.reduce(
    (sum, it) => sum + (Number(it.lg_info?.value) || 0),
    0,
  )

  return {
    name: 'Summary',
    bidding_value: totalBiddingValue,
    value: totalValue,
  }
})

const tableOptions = useTable<IPmoProject>({
  options: {
    isHidePagination: true,
  },

  repo: project,
  columns: () => [
    {
      accessorKey: 'name',
      footer: 'Summary',

      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'Name',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
    },
    {
      accessorKey: 'bidding_info.bidding_value',
      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'มูลค่า',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
      footer: () => {
        return h('div', {
          class: 'text-right font-medium',
        }, `${formatNumberUnit(summaryRow.value?.bidding_value || 0)}`)
      },
    },
    {
      accessorKey: 'lg_info.value',
      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'หลักประกัน',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
      footer: () => {
        return h('div', {
          class: 'text-right font-medium',
        }, `${formatNumberUnit(summaryRow.value?.value || 0)}`)
      },
    },
    {
      accessorKey: 'budget_info.fund_type',
      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'ประเภทงบ',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
    },
    {
      accessorKey: 'bidding_info.bidding_type',
      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'ประเภทจัดซื้อจัดจ้าง',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
    },
    {
      accessorKey: 'lg_info.start_date',
      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'Signing Date',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
    },

    {
      accessorKey: 'budget_info.partner',
      header: ({
        column,
      }) => {
        const isSorted = column.getIsSorted()

        return h(Button, {
          color: 'neutral',
          variant: 'ghost',
          size: 'sm',
          label: 'คู่สัญญา',
          icon: isSorted
            ? isSorted === 'asc'
              ? 'i-lucide-arrow-up-narrow-wide'
              : 'i-lucide-arrow-down-wide-narrow'
            : 'i-lucide-arrow-up-down',
          class: '-mx-2.5',
          onClick: () => column.toggleSorting(column.getIsSorted() === 'asc'),
        })
      },
    },

    {
      accessorKey: 'remark',
      header: 'Remark',
    },

  ],
})

project.fetchSetLoading()
onMounted(() => {
  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createBidding.status.value.isSuccess, async () => {
  bidingValueModal.close()
  biddingTypeModal.close()
  noti.success({
    title: 'แก้ไขประมูลงาน สำเร็จ',
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createBidding.status.value.isError, async () => {
  noti.error({
    title: 'แก้ไขประมูลงาน ไม่สำเร็จ',
    description: StringHelper.getError(createBidding.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขประมูลงาน กรุณาลองใหม่อีกครั้ง'),
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createLG.status.value.isSuccess, async () => {
  lgValueModal.close()
  lgDateModal.close()
  noti.success({
    title: 'แก้ไขงบหลักประกัน สำเร็จ',
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createLG.status.value.isError, async () => {
  noti.error({
    title: 'แก้ไขงบหลักประกัน ไม่สำเร็จ',
    description: StringHelper.getError(createLG.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขงบหลักประกัน กรุณาลองใหม่อีกครั้ง'),
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createBudget.status.value.isSuccess, async () => {
  budgetFundTypeModal.close()
  budgetPartnerModal.close()

  noti.success({
    title: 'แก้ไขงบโครงการ สำเร็จ',
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createBudget.status.value.isError, async () => {
  noti.error({
    title: 'แก้ไขงบโครงการ ไม่สำเร็จ',
    description: StringHelper.getError(createBudget.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขงบโครงการ กรุณาลองใหม่อีกครั้ง'),
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createRemark.status.value.isSuccess, async () => {
  remarkValueModal.close()
  noti.success({
    title: 'แก้ไขหมายเหตุ สำเร็จ',
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})

useWatchTrue(() => createRemark.status.value.isError, async () => {
  noti.error({
    title: 'แก้ไขหมายเหตุ ไม่สำเร็จ',
    description: StringHelper.getError(createRemark.status.value.errorData, 'เกิดข้อผิดพลาดในการแก้ไขหมายเหตุ กรุณาลองใหม่อีกครั้ง'),
  })

  project.fetchPage(1, '', {
    params: {
      include_all: true,
    },
  })
})
</script>
