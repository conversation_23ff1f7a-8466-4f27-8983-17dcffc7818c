<template>
  <nav
    class="
      fixed top-0 left-0 z-20 flex min-h-[80px] w-screen items-center
      justify-center bg-white
    "
    style="box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);"
  >
    <div
      class="
        mx-auto flex w-full px-4 md:px-8

      "
    >
      <div class="flex items-center gap-6">
        <svg
          class="
            cursor-pointer
            lg:hidden
          "
          width="19"
          height="18"
          viewBox="0 0 19 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          transform="scale(-1, 1)"
          @click="isShowMobileMenu = true"
        >
          <path
            d="M10 18L10 16L19 16L19 18L10 18ZM6 2L6 -2.62268e-07L19 -8.30516e-07L19 2L6 2ZM-3.49691e-07 10L-4.37114e-07 8L19 8L19 10L-3.49691e-07 10Z"
            fill="#4B5563"
          />
        </svg>
        <NuxtLink :to="routes.home.to">
          <img
            src="/logo-mini.png"
            alt="logo_mini"
            class="h-[27px] w-[172px] cursor-pointer"
          />
        </NuxtLink>
      </div>

      <div
        v-if="mode !== 'hideAll'"
        class="flex flex-1 items-center justify-end gap-2"
      >
        <DropdownMenu
          :items="profileDropdown"
          size="xl"
          :content="{
            align: 'end',
            side: 'bottom',
          }"
          :ui="{
            content: 'w-48',
          }"
        >
          <div class="relative flex cursor-pointer items-center gap-2">
            <div
              class="
                hidden flex-col justify-end text-right
                md:flex
              "
            >
              <p class="text-sm font-bold">
                {{ auth.me.value?.display_name || auth.me.value?.full_name }}
              </p>
              <p class="text-muted text-xs">
                {{ auth.me.value?.email }}
              </p>
            </div>
            <Avatar
              class="border-muted size-[36px] border text-2xl"
              icon="ri:user-line"
              :src="auth.me.value?.avatar_url"
            />
            <Icon
              name="i-ph:caret-down-light"
              class="size-5"
            />
          </div>
        </DropdownMenu>
      </div>
    </div>

    <!-- Mobile Navigation Slideover -->
    <Slideover
      v-model:open="isShowMobileMenu"
      :ui="{
        content: 'w-[80%] max-w-[260px] lg:hidden',
        overlay: 'lg:hidden',
      }"
      side="left"
    >
      <template #content>
        <div class="flex h-full flex-col bg-white">
          <!-- Mobile Header -->
          <div class="flex items-center justify-between p-4">
            <NuxtLink :to="routes.home.to">
              <img
                src="/logo.png"
                alt="logo"
                class="h-[42px]"
              />
            </NuxtLink>
            <Icon
              name="ph:x-bold"
              class="size-[24px] cursor-pointer text-gray-500"
              @click="isShowMobileMenu = false"
            />
          </div>
        </div>
      </template>
    </Slideover>
  </nav>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'

withDefaults(defineProps<{
  mode?: 'full' | 'hideMenu' | 'hideAll'
}>(), {
  mode: 'full',
})

const isShowMobileMenu = ref(false)
const profileDropdown = [
  [
    {
      label: 'View profile',
      icon: 'i-lucide-user',
      to: routes.account.profile.to,
    },

    {
      label: routes.logout.label,
      icon: 'i-lucide-log-out',
      to: routes.logout.to,
      external: true,
    },
  ],
]

const auth = useAuth()
</script>
