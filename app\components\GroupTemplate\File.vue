<template>
  <Card
    class="overflow-visible"
    :ui="{
      body: 'lg:p-0  ',
    }"
  >
    <StatusBox
      :status="documentGroup.fetch.status"
      :data="documentGroup.fetch.items"
    >
      <div class="flex items-start justify-between p-4">
        <div>
          <h3 class="text-primary-900 text-xl font-bold dark:text-slate-100">
            เอกสาร
          </h3>
          <p class="-mt-0.5 text-sm text-slate-500 dark:text-slate-400">
            Document
          </p>
        </div>

        <div class="flex gap-2">
          <Button
            v-if="documentGroup.fetch.items.find((p: any) => String(p.id) === selectedId)?.sharepoint_url"
            variant="outline"
            color="neutral"
            :icon="'mdi:microsoft-sharepoint'"
            class="rounded-lg"
            aria-label="Toggle"
            target="_blank"
            :to="documentGroup.fetch.items.find((p: any) => String(p.id) === selectedId)?.sharepoint_url"
          />
          <Button
            variant="outline"
            color="neutral"
            :icon="'weui:setting-outlined'"
            class="rounded-lg"
            aria-label="Toggle"
            @click="onOpenSharepoint"
          />
        </div>
      </div>
      <div class="h-full min-h-[400px] border-t border-t-[#D0D5DD] bg-white md:flex">
        <div class="hidden border-r border-[#D0D5DD] md:block">
          <div class="bg-primary mb-3 flex h-11 items-center justify-between p-2 text-white">
            Group
            <Button
              v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
              class="p-0 text-white"
              icon="flowbite:folder-plus-outline"
              @click="onAddGroup"
            />
          </div>
          <div class="block w-48 p-2">
            <template v-if="documentGroup.fetch.items.length">
              <div
                v-for="p in documentGroup.fetch.items"
                :key="p.id"
                class="mb-1 flex cursor-pointer items-center justify-between gap-3 px-3 py-2 hover:bg-gray-100"
                :class="{ 'bg-blue-100 font-medium': selectedId === p.id }"
                @click="selectedId = p.id"
              >
                {{ p.group_name }}

                <DropdownMenu
                  v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
                  :items="groupActions(p)"
                >
                  <Button
                    class="p-0"
                    color="neutral"
                    icon="i-heroicons-ellipsis-vertical"
                    variant="ghost"
                  />
                </DropdownMenu>
              </div>
            </template>
            <div
              v-else
              class="mt-5 flex flex-col items-center justify-center gap-2"
            >
              <p class="text-center text-sm text-gray-400">
                Start add group<br />by click at
              </p>
              <ButtonActionIcon
                class="p-0 text-gray-400"
                icon="flowbite:folder-plus-outline"
              />
            </div>
          </div>
        </div>

        <section
          v-if="documentGroup.fetch.items.length"
          class="flex flex-1 flex-col"
        >
          <div class="flex items-center justify-between px-4 md:hidden">
            <div class="overflow-x-auto">
              <Tabs
                v-model="selectedId"
                color="neutral"
                variant="link"
                :items="itemsTab"
                class="w-full min-w-max"
              />
            </div>
            <Button
              class="text-white"
              icon="flowbite:folder-plus-outline"
              @click="onAddGroup"
            />
          </div>

          <div class="border-b border-[#D0D5DD] px-4 py-3">
            <h2 class="mb-3 hidden items-center font-bold md:block">
              <span class="flex w-full items-center gap-2">
                <Icon
                  class="size-5"
                  name="material-symbols:folder-outline"
                />
                <span>
                  {{ documentGroup.fetch.items.find(p => String(p.id) === selectedId)?.group_name }}
                </span>
              </span>
            </h2>

            <div class="flex items-center justify-between gap-2">
              <Input
                placeholder="ค้นหา..."
                icon="i-heroicons-magnifying-glass-20-solid"
                class="w-full max-w-56"
              />
              <Button
                v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
                icon="i-heroicons-plus"
                color="primary"
                label="เพิ่มเอกสาร"
                @click="onAddFile"
              />
            </div>
          </div>

          <div class="overflow-auto p-4">
            <Table
              :options="tableOptions"
              @pageChange="documentItemByProject.fetchPageChange"
              @search="documentItemByProject.fetchSearch"
            >
              <template #file-cell="{ row }">
                <FileDownloadBox
                  class="border-none bg-gray-50"
                  :extension="row.original.file.name.split('.').pop()"
                  :file="row.original.file"
                  :name="row.original.name"
                  readonly
                />
              </template>
              <template #updated_by-cell="{ row }">
                <AvatarProfile
                  :item="row.original.updated_by"
                />
              </template>
              <template #actions-cell="{ row }">
                <DropdownMenu
                  v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
                  :items="fileActions(row.original)"
                >
                  <Button
                    class="p-0"
                    color="neutral"
                    icon="i-heroicons-ellipsis-vertical"
                    variant="ghost"
                  />
                </DropdownMenu>
              </template>
            </Table>
          </div>
        </section>
        <Empty
          v-else
          class="w-full text-center"
          icon=""
          message="No document items<br/> Please start add group first"
        />
      </div>
    </StatusBox>
  </Card>
</template>

<script lang="ts" setup>
import FormGroup from './FormGroup.vue'
import FormFile from './FormFile.vue'
import FormSharepoint from './FormSharepoint.vue'
import HistoryFile from './History.vue'
import type { PROJECT_TAB_TYPE } from '~/constants/projectTab'
import { usePmoDocumentGroupsByProjectIdPageLoader, usePmoDocumentItemByProjectIdPageLoader } from '~/loaders/pmo/documents'

const props = defineProps<{
  projectId: string
  tab: PROJECT_TAB_TYPE
}>()

const documentItemByProject = usePmoDocumentItemByProjectIdPageLoader(props.projectId || '')
const documentGroup = usePmoDocumentGroupsByProjectIdPageLoader(props.projectId || '')

const noti = useNotification()

const overlay = useOverlay()
const dialog = useDialog()
const editGroup = overlay.create(FormGroup)
const addGroup = overlay.create(FormGroup)
const editFile = overlay.create(FormFile)
const addFile = overlay.create(FormFile)
const historyFile = overlay.create(HistoryFile)
const editSharepoint = overlay.create(FormSharepoint)
const permission = useProjectPermission()
const selected = ref({
  id: '',
  group_name: '',
})

documentItemByProject.fetchSetLoading()
documentGroup.fetchSetLoading()
onMounted(() => {
  documentGroup.fetchPage(1, '', {
    params: {
      tab_key: props.tab,
      limit: 100,
    },
  })
})

const selectedId = computed({
  get: () => String(selected.value.id ?? ''),
  set: (val: string) => {
    selected.value.id = val
    const g = documentGroup?.fetch.items.find((p: any) => String(p.id) === val)

    selected.value.group_name = g?.group_name ?? ''
  },
})

const itemsTab = computed(() => {
  return documentGroup?.fetch.items.map((p: any) => ({
    label: p.group_name,
    value: p.id,
  }))
})

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: documentItemByProject,
  columns: () => [
    {
      accessorKey: 'file',
      header: 'เอกสาร',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'w-1/3',
        },
      },
    },
    {
      accessorKey: 'created_at',
      header: 'ลงวันที่',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'updated_at',
      header: 'แก้ไขล่าสุด',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'updated_by',
      header: 'โดย',
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

const groupActions = (value: any) => {
  return [
    {
      label: 'Edit Group',
      icon: 'i-heroicons-pencil-square-20-solid',
      onSelect() {
        editGroup.open({
          isEditing: true,
          values: value,
          status: () => documentGroup.update.status,
          onSubmit: (values: any) => {
            documentGroup.updateRun(value.id, {
              data: values,
            })
          },
        })
      },
    },
    {
      label: 'Delete Group',
      icon: 'i-heroicons-trash-20-solid',
      onSelect() {
        dialog.confirm({
          title: 'ยืนยันการลบ',
          description: `คุณต้องการลบกลุ่มเอกสารนี้หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          documentGroup.deleteRun(value.id)
        })
      },
    },
  ]
}

const fileActions = (row: any) => {
  const actions: any[] = []

  if (permission.hasPermission(props.tab, PMO_PERMISSION.MODIFY)) {
    actions.push(
      {
        label: 'Edit',
        icon: 'i-heroicons-pencil-square-20-solid',
        onSelect() {
          editFile.open({
            isEditing: true,
            values: row,
            status: () => documentItemByProject.update.status,
            onSubmit: (values: any) => {
              documentItemByProject.updateRun(row.id, {
                data: values,
              })
            },
          })
        },
      },
      {
        label: 'Delete File',
        icon: 'i-heroicons-trash-20-solid',
        onSelect() {
          dialog.confirm({
            title: 'ยืนยันการลบ',
            description: `คุณต้องการลบเอกสารนี้หรือไม่?`,
            confirmText: 'ยืนยัน',
            cancelText: 'ยกเลิก',
          }).then(() => {
            dialog.loading({
              title: 'กรุณารอสักครู่...',
              description: 'กำลังส่งข้อมูล...',
            })

            documentItemByProject.deleteRun(row.id)
          })
        },
      },
    )
  }

  actions.push(
    {
      label: 'Open SharePoint',
      icon: 'i-heroicons-folder-20-solid',
      onSelect() {
        if (row.sharepoint_url) {
          window.open(row.sharepoint_url, '_blank')
        }
      },
    },
    {
      label: 'Download File',
      icon: 'i-heroicons-arrow-down-tray-20-solid',

      async onSelect() {
        if (row.file.type) {
          await download(row)
        }
      },
    },
    {
      label: 'Version History',
      icon: 'i-heroicons-clock-20-solid',
      onSelect() {
        OpenHistory(row.id)
      },
    },
  )

  return actions
}

const OpenHistory = (documentID: string) => {
  historyFile.open({
    projectId: props.projectId,
    documentID: documentID,
  })
}

const onAddGroup = () => {
  addGroup.open({
    status: () => documentGroup.add.status,
    onSubmit: (values: IGroup) => {
      const payload: IGroup = {
        ...values,
        tab_key: props.tab,
        group_id: selectedId.value,
      }

      documentGroup.addRun({
        data: payload,
      })
    },
  })
}

// Document
const onAddFile = () => {
  addFile.open({
    status: () => documentItemByProject.add.status,
    onSubmit: (values: any) => {
      documentItemByProject.addRun({
        data: {
          ...values,
          tab_key: props.tab,
          group_id: selectedId.value,
        },
      })
    },
  })
}

// utils
const download = async (data: any) => {
  const pathname = data.file.type
  const ext = pathname.split('/')[1].trim()

  await downloadDocument({
    url: data.file.url,
    method: 'GET',
    filename: data.file.name,
    ext: ext,
  })
}

const onOpenSharepoint = () => {
  editSharepoint.open({
    status: () => documentGroup.update.status,
    values: documentGroup.fetch.items.find((p: any) => String(p.id) === selectedId.value),
    onSubmit: (values: IGroup) => {
      documentGroup.updateRun(selectedId.value, {
        data: values,
      })
    },
  })
}

useWatchTrue(() => documentGroup.add.status.isError, async () => {
  noti.error({
    title: 'เพิ่มกลุ่มเอกสารไม่สำเร็จ',
  })
})

useWatchTrue(() => documentGroup.add.status.isSuccess, async () => {
  addGroup.close()
  noti.success({
    title: 'เพิ่มกลุ่มเอกสารไม่สำเร็จ',
  })

  documentGroup.fetchPage(1, undefined, {
    params: {
      limit: 100,
      tab_key: props.tab,
    },
  })
})

useWatchTrue(() => documentGroup.update.status.isError, async () => {
  editSharepoint.close()
  noti.error({
    title: 'แก้ไขกลุ่มเอกสารไม่สำเร็จ',
    description: StringHelper.getError(documentGroup.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขกลุ่มเอกสาร กรุณาลองใหม่อีกครั้ง'),
  })
})

useWatchTrue(() => documentGroup.update.status.isSuccess, async () => {
  editSharepoint.close()
  editGroup.close()
  noti.success({
    title: 'แก้ไขกลุ่มเอกสารไม่สำเร็จ',
  })

  documentGroup.fetchPage(1, undefined, {
    params: {
      limit: 100,
      tab_key: props.tab,
    },
  })
})

watch(
  () => selectedId.value,
  () => {
    documentItemByProject.fetchPage(1, '', {
      params: {
        group_id: selectedId.value,
        limit: 100,
        tab_key: props.tab,
      },
    })
  },
  {
    immediate: true,
    deep: false,
  },
)

useWatchTrue(() => documentGroup.fetch.status.isSuccess, async () => {
  const first = documentGroup?.fetch?.items?.at(0)

  documentItemByProject.fetchPage(1, undefined, {
    params: {
      group_id: first?.id,
      limit: 100,
      tab_key: props.tab,
    },
  })

  selected.value.id = first?.id as string
  selected.value.group_name = first?.group_name as string
})

useWatchTrue(() => documentItemByProject.add.status.isSuccess, async () => {
  addFile.close()
  noti.success({
    title: 'เพิ่มเอกสารสำเร็จ',
  })

  documentItemByProject.fetchPage(1, undefined, {
    params: {
      group_id: selectedId.value,
      limit: 100,
      tab_key: props.tab,
    },
  })
})

useWatchTrue(() => documentItemByProject.add.status.isError, async () => {
  addFile.close()
  noti.error({
    title: 'เพิ่มเอกสารไม่สำเร็จ',
    description: StringHelper.getError(documentItemByProject.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มเอกสาร กรุณาลองใหม่อีกครั้ง'),
  })
})

useWatchTrue(() => documentItemByProject.update.status.isSuccess, async () => {
  editFile.close()
  noti.success({
    title: 'แก้ไขเอกสารสำเร็จ',
  })

  documentItemByProject.fetchPage(1, undefined, {
    params: {
      group_id: selectedId.value,
      limit: 100,
      tab_key: props.tab,
    },
  })
})

useWatchTrue(() => documentItemByProject.add.status.isError, async () => {
  editFile.close()
  noti.error({
    title: 'แก้ไขเอกสารไม่สำเร็จ',
    description: StringHelper.getError(documentItemByProject.add.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขเอกสาร กรุณาลองใหม่อีกครั้ง'),
  })
})
</script>
