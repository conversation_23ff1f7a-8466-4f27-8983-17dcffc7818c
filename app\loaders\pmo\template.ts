export const useTemplateDocumentPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<ITemplateDocument>({
    baseURL: `/pmo/template/documents`,
    getBaseRequestOptions: options.auth,
  })
}

export const useTemplateChecklistItemPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<ITemplateChecklistItem>({
    baseURL: `/pmo/template/checklist-items`,
    getBaseRequestOptions: options.auth,
  })
}
