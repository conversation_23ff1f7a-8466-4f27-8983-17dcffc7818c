<template>
  <Form @submit="onSubmit">
    <div class="rounded-xl border border-[#EAECF0]">
      <div class="flex items-center bg-gray-50 px-6 py-3 text-sm font-medium">
        <div class="w-1/2">
          System
        </div>
        <div class="w-1/2">
          Access Level
        </div>
      </div>
      <div
        v-for="system in systems"
        :key="system.name"
        class="flex items-center border-t border-[#EAECF0] px-6 py-4 first:border-t-0"
      >
        <div class="flex w-1/2 items-center gap-3">
          <img
            :src="system.icon"
            class="size-10"
            alt="profile_mock"
          />
          <span class="text-sm font-medium">{{ system.name }}</span>
        </div>
        <div class="w-1/2">
          <FormFields
            :options="system.fieldForm"
          />
        </div>
      </div>
    </div>
    <Separator class="mt-4 mb-5" />
    <div class="flex justify-end gap-4">
      <Button
        label="Save Changes"
        type="submit"
        :disabled="form.meta.value.touched"
      />
    </div>
  </Form>
</template>

<script lang="ts" setup>
import { useUserAccessLevelUpdateLoader } from '~/loaders/admin/user'

const props = defineProps<{
  profile: IUser
}>()

const noti = useNotification()
const accessLevel = useUserAccessLevelUpdateLoader()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    clockin: v.optional(v.pipe(v.string()), undefined),
    timesheet: v.optional(v.pipe(v.string()), undefined),
    pmo: v.optional(v.pipe(v.string()), undefined),
    setting: v.optional(v.pipe(v.string()), undefined),
  })),
  initialValues: props.profile.access_level || {
    clockin: Permission.USER,
    timesheet: Permission.USER,
    pmo: Permission.NONE,
    setting: Permission.NONE,
  },
})

const systems = ref([
  {
    icon: '/admin/clock-in-logo.png',
    name: 'Clock-In',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {
          name: 'clockin',
          options: [
            ObjectHelper.createOption(Permission.USER, PERMISSION_LABEL[Permission.USER]),
            ObjectHelper.createOption(Permission.ADMIN, PERMISSION_LABEL[Permission.ADMIN]),
          ],
        },
      },
    ]),
  },
  {
    icon: '/admin/timesheet-logo.png',
    name: 'Timesheet',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {
          name: 'timesheet',
          options: [
            ObjectHelper.createOption(Permission.USER, PERMISSION_LABEL[Permission.USER]),
            ObjectHelper.createOption(Permission.ADMIN, PERMISSION_LABEL[Permission.ADMIN]),
          ],
        },
      },
    ]),
  },
  {
    icon: '/admin/pmo-logo.png',
    name: 'PMO',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {

          name: 'pmo',
          options: [
            ObjectHelper.createOption(Permission.NONE, PERMISSION_LABEL[Permission.NONE]),
            ObjectHelper.createOption(Permission.USER, PERMISSION_LABEL[Permission.USER]),
            ObjectHelper.createOption(Permission.ADMIN, PERMISSION_LABEL[Permission.ADMIN]),
            ObjectHelper.createOption(Permission.SUPER, PERMISSION_LABEL[Permission.SUPER]),
          ],
        },
      },
    ]),
  },
  {
    icon: '/admin/super-admin-logo.png',
    name: 'Super Admin Setting',
    fieldForm: createFormFields(() => [
      {
        type: INPUT_TYPES.SELECT,
        props: {
          name: 'setting',
          options: [
            ObjectHelper.createOption(Permission.NONE, PERMISSION_LABEL[Permission.NONE]),
            ObjectHelper.createOption(Permission.SUPER, PERMISSION_LABEL[Permission.SUPER]),
          ],
        },
      },
    ]),
  },
])

const onSubmit = form.handleSubmit((values) => {
  accessLevel.run({
    urlBind: {
      id: props.profile.id,
    },
    data: values,
  })
})

useWatchTrue(() => accessLevel.status.value.isSuccess, () => {
  noti.success({
    title: 'Save Changes',
    description: 'Changes saved successfully',
  })

  form.resetForm({
    values: accessLevel.data.value?.access_level,
  })
})

useWatchTrue(() => accessLevel.status.value.isError, () => {
  noti.error({
    title: 'Save Changes Failed',
    description: StringHelper.getError(accessLevel.status.value.errorData, 'An error occurred while saving changes, please try again'),
  })
})
</script>
