<template>
  <div class="mb-6 flex flex-col gap-4">
    <Form>
      <FormFields
        :options="formField"
      />
    </Form>

    <div class="flex items-center gap-2">
      <Button
        label="Search"
        size="xl"
        class="w-fit"
        @click="search"
      />
      <Button
        v-if="checkin.fetch.items && checkin.fetch.items.length > 0"
        label="Export CSV"
        size="xl"
        class="w-fit"
        variant="outline"
        icon="proicons:add-square-multiple"
        color="neutral"
        @click="exportToExcel"
      />
    </div>
    <Table
      v-if="!checkin.fetch.status.isLoading"
      v-model:column-pinning="columnPinning"
      :options="tableOptions"
      @pageChange="checkin.fetchPage"
      @search="checkin.fetchSearch"
    >
      <template #name-cell="{ row }">
        <AvatarProfile :item="row.original" />
      </template>
      <template
        v-for="day in daysInMonth"
        #[`detail-${day}-date-cell`]="{ row }"
        :key="day"
      >
        <div
          class="flex h-[72px] min-w-[50px] flex-col items-center justify-center"
        >
          <img
            v-for="(type, index) in (row.original.detail.find(d => {
              const month = String(currentDate.getMonth() + 1).padStart(2, '0')
              const date = String(day).padStart(2, '0')
              const targetDate = `${currentDate.getFullYear()}-${month}-${date}`
              return d.date === targetDate
            })?.type || []).filter((t :string) => iconImages?.[t])"
            :key="index + day"
            :src="iconImages[type]"
            class="size-5"
            :alt="type"
          />
        </div>
      </template>
      <template #ontime-cell="{ row }">
        <div class="w-[145px] text-center">
          {{ row.original.ontime }}
        </div>
      </template>
      <template #latetime-cell="{ row }">
        <div class="w-[130px] text-center">
          {{ row.original.latetime }}
        </div>
      </template>
      <template #notcheckin-cell="{ row }">
        <div class="w-[120px] text-center">
          {{ getNotCheckinCount(row.original.detail) }}
        </div>
      </template>
    </Table>
    <Log :data-item="checkitems.groupByDateItems" />
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES, type TableColumn } from '#core/components/Table/types'
import {
  format,
  isSaturday,
  isSunday,
  getDaysInMonth,
  isSameMonth,
  isSameYear,
  startOfMonth,
  endOfMonth,
} from 'date-fns'
import { useAdminCheckInsPageLoader } from '~/loaders/admin/checkin'
import { useTeamPageLoader } from '~/loaders/admin/team'
import { useHolidaysPageLoader } from '~/loaders/admin/holiday'
import { useCheckInItems } from '~/composables/useCheckInItems'
import { useBreakpoints } from '@vueuse/core'

const breakpoints = useBreakpoints({
  mobile: 0,
  desktop: 1024,
})

const isMobile = breakpoints.smaller('desktop')
const team = useTeamPageLoader()
const checkin = useAdminCheckInsPageLoader()
const currentDate = ref(new Date())
const holiday = useHolidaysPageLoader()
const checkitems = useCheckInItems(() => checkin.fetch.items)
const columnPinning = computed(() => ({
  left: isMobile.value ? [] : ['name'],
  right: isMobile.value ? [] : ['ontime', 'latetime', 'notcheckin'],
}))

holiday.fetchSetLoading()
team.fetchSetLoading()
checkin.fetchSetLoading()
onMounted(() => {
  holiday.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })

  team.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const daysInMonth = computed(() => {
  const days = getDaysInMonth(new Date(currentDate.value))

  return Array.from({
    length: days,
  }, (_, i) => i + 1)
})

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      date: v.nullish(v.object({
        year: v.number(),
        month: v.number(),
      })),
      team_code: v.optional(v.pipe(v.string(), v.nonEmpty('')), ''),
    }),
  ),
  initialValues: {
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },
})

const formField = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'team_code',
      label: 'Team',
      placeholder: 'All Team',
      clearable: true,
      options: ArrayHelper.toOptions(team.fetch.items, 'code', 'name'),
      searchable: true,
    },
  },
])

const getCheckinStatus = (details: any[], day: number): 'ontime' | 'late' | 'none' => {
  const month = String(currentDate.value.getMonth() + 1).padStart(2, '0')
  const date = String(day).padStart(2, '0')
  const targetDate = `${currentDate.value.getFullYear()}-${month}-${date}`

  const found = details.find((d) => d.date === targetDate)
  if (!found) return 'none'

  return found.is_late ? 'late' : 'ontime'
}

const tableOptions = useTable<ICheckinItem>({
  repo: checkin,
  transformItems: () => checkitems.groupByDateItems.value as any[],
  options: {
    isHidePagination: false,
  },
  columns: () => {
    const baseColumns: TableColumn<ICheckinItem>[] = [
      {
        accessorKey: 'name',
        header: 'Name',
        type: COLUMN_TYPES.TEXT,
      },
    ]

    if (daysInMonth.value) {
      daysInMonth.value.forEach((u) => {
        baseColumns.push({
          accessorKey: `detail-${u}-date`,
          header: () => h('div', [`${u}`]),
          type: COLUMN_TYPES.TEXT,
          meta: {
            class: {
              td: (row: any) => {
                const detail = row.row.original.detail

                if (getCheckinStatus(detail, u) === 'ontime') return 'bg-[#ECFDF3] text-right'
                if (getCheckinStatus(detail, u) === 'late') return 'bg-[#FFFAEB] text-right'
                if (getCheckinStatus(detail, u) === 'none'
                  && !isHoliday(u)
                  && !isWeekend(u)) return 'bg-[#F8FAFC] text-right'
                if (isHoliday(u) || isWeekend(u)) return 'bg-[#EAECF0] text-right'

                return 'text-right'
              },
              th: 'text-center',
            },
          },
        } as TableColumn<ICheckinItem>)
      })
    }

    baseColumns.push({
      accessorKey: 'ontime',
      header: 'ontime-checkin',
      type: COLUMN_TYPES.TEXT,
    } as TableColumn<ICheckinItem>)

    baseColumns.push({
      accessorKey: 'latetime',
      header: 'late-checkin',
      type: COLUMN_TYPES.TEXT,
    } as TableColumn<ICheckinItem>)

    baseColumns.push({
      accessorKey: 'notcheckin',
      header: 'not-checkin',
      type: COLUMN_TYPES.TEXT,
    } as TableColumn<ICheckinItem>)

    return baseColumns
  },

})

const isWeekend = (day: number): boolean => {
  const date = `${currentDate.value.getFullYear()}-${currentDate.value.getMonth() + 1}-${day}`

  return isSaturday(date) || isSunday(date)
}

const isHoliday = (day: number): boolean => {
  const month = String(currentDate.value.getMonth() + 1).padStart(2, '0')
  const date = String(day).padStart(2, '0')
  const targetDate = `${currentDate.value.getFullYear()}-${month}-${date}`

  return holiday.fetch.items?.some((d) => format(d.date, 'yyyy-MM-dd') === targetDate) ?? false
}

const getNotCheckinCount = (details: any[]): number => {
  const today = new Date()
  let totalDays = getDaysInMonth(new Date(currentDate.value))

  // ถ้าเป็นเดือนปัจจุบัน ให้หยุดนับที่วันนี้
  if (isSameYear(today, new Date(currentDate.value.getFullYear(), currentDate.value.getMonth()))
    && isSameMonth(today, new Date(currentDate.value.getFullYear(), currentDate.value.getMonth()))) {
    totalDays = today.getDate()
  }

  let notCheckin = 0

  for (let day = 1; day <= totalDays; day++) {
    const date = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), day)
    const dateStr = format(date, 'yyyy-MM-dd')

    const weekend = isSaturday(date) || isSunday(date)
    const holidayMatch = holiday.fetch.items?.some((h) => h.date === dateStr)

    if (!weekend && !holidayMatch) {
      if (!details.some((d) => d.date === dateStr)) {
        notCheckin++
      }
    }
  }

  return notCheckin
}

const search = () => {
  checkin.fetchPage(1, '', {
    params: {
      limit: 9999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      team_code: form.values.team_code || undefined,
      is_unused: false,
    },
  })
}

const exportToExcel = () => {
  const header = ['Name', daysInMonth.value.map((d) => `Day-${d}`), 'ontime-checkin', 'late-checkin', 'not-checkin'].flat()
  const rows = checkitems.groupByDateItems.value.map((item) => {
    const row: (string | number)[] = [
      item.display_name,
      ...daysInMonth.value.map((day) => {
        const detail = item.detail.find((d) => {
          const month = String(currentDate.value.getMonth() + 1).padStart(2, '0')
          const date = String(day).padStart(2, '0')
          const targetDate = `${currentDate.value.getFullYear()}-${month}-${date}`

          return d.date === targetDate
        })

        return detail?.type?.map((t: string) => t).join(', ') || ''
      }),
      item.ontime || 0,
      item.latetime || 0,
      getNotCheckinCount(item.detail),
    ]

    return row
  })

  const teamName = team.fetch.items?.find((t) => t.code === form.values.team_code)?.name || 'AllTeam'

  const filename = `report_clockin_team_${teamName}_${currentDate.value.getMonth()}_${currentDate.value.getFullYear()}.xlsx`

  QuickExcel.export(
    header,
    rows,
    filename,
  )
}

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
}, {
  deep: true,
})
</script>

<style scoped>
::v-deep(.custom-datepicker .dp__input) {
  height: 32px !important;
}
</style>
