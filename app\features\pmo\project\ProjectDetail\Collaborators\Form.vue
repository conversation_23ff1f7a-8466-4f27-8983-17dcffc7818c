<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขสมาชิกโครงการ' : 'เพิ่มสมาชิกโครงการ'"
    :description="isEditing ? 'Edit project collaborators' : 'Add project collaborators'"
    :ui="{
      content: 'max-w-2xl',
    }"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <input
          type="submit"
          hidden
        />
        <div class="mt-5 mb-2 text-sm">
          <strong>กำหนดสิทธิ์ใช้งาน</strong> Confidential <strong>(Permissions Setting)</strong>
        </div>
        <Separator class="my-4" />
        <TableSimple
          :options="tableOptions"
          :ui="{
            th: 'text-[#222222] bg-white text-xs font-normal',
          }"
        >
          <template #permissions-cell="{ row }">
            <FormFields :options="createTabFields(row.original.key).value" />
          </template>

          <template #main-cell="{ row }">
            <FormFields
              class="flex justify-center"
              :options="createMainFields(row.original.key).value"
            />
          </template>
        </TableSimple>
        <Separator class="mb-4" />
      </form>
    </template>
    <template #footer>
      <div class="flex w-full justify-end gap-3">
        <Button
          variant="outline"
          color="neutral"
          @click="emits('close', false)"
        >
          ยกเลิก
        </Button>
        <Button
          icon="material-symbols:check"
          :loading="status().isLoading"
          :disabled="!form.meta.value.dirty"
          @click="onSubmit"
        >
          บันทึก
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { tabList } from '~/constants/projectTab'
import { useUserPageLoader } from '~/loaders/admin/user'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  currentMembers: ICollaborators[]
  values?: ICollaborators & { display_name: string } | null
  status: () => IStatus
  onSubmit: (values: ICollaborators & { display_name: string }) => void
}>()

const user = useUserPageLoader()

const tablePermission = tabList.map((c) => ({
  tab: c.label,
  key: c.key,
  permissions: props.values?.[`${c.key}_permission` as keyof ICollaborators],
  main: props.values?.[`${c.key}_main` as keyof ICollaborators],
}))

const form = useForm({
  initialValues: props.values
    ? props.values
    : {
      confidential_permission: PMO_PERMISSION.NONE,
      sales_permission: PMO_PERMISSION.NONE,
      presales_permission: PMO_PERMISSION.NONE,
      bidding_permission: PMO_PERMISSION.NONE,
      pmo_permission: PMO_PERMISSION.NONE,
    },
  validationSchema: toTypedSchema(
    v.object({
      user_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกพนักงาน')), ''),
      confidential_permission: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกสิทธิ์การใช้งาน')), ''),
      confidential_main: v.optional(v.pipe(v.boolean())),
      sales_permission: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกสิทธิ์การใช้งาน')), ''),
      sales_main: v.optional(v.pipe(v.boolean())),
      presales_permission: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกสิทธิ์การใช้งาน')), ''),
      presales_main: v.optional(v.pipe(v.boolean())),
      bidding_permission: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกสิทธิ์การใช้งาน')), ''),
      bidding_main: v.optional(v.pipe(v.boolean())),
      pmo_permission: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกสิทธิ์การใช้งาน')), ''),
      pmo_main: v.optional(v.pipe(v.boolean())),
    }),
  ),
})

onMounted(() => {
  user.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'user_id',
      label: 'สมาชิก',
      required: true,
      disabled: props.isEditing,
      placeholder: 'ชื่อ (Name)',
      loading: user.fetch.status.isLoading,
      options: (user.fetch.items || [])
        .map((item: IUser) => {
          const isDisabled = (props.currentMembers || []).some((it) => it.user.id === item.id)

          return {
            label: isDisabled
              ? `${item.display_name} (มีสิทธิ์อยู่แล้ว)`
              : item.display_name,
            value: item.id,
            avatar: {
              src: item.avatar_url,
              alt: item.display_name,
            },
            disabled: isDisabled,
          }
        })
        .sort((a, b) => {
          if (a.disabled === b.disabled) return 0

          return a.disabled ? 1 : -1
        }),
      searchable: true,
    },
  },
])

const createTabFields = (tabName: string) => {
  return createFormFields(() => [
    {
      type: INPUT_TYPES.RADIO,
      props: {
        name: `${tabName}_permission`,
        orientation: 'horizontal',
        required: true,
        options: PMO_PERMISSION_OPTIONS,
      },
    },
  ])
}

const createMainFields = (tabName: string) => {
  return createFormFields(() => [
    {
      type: INPUT_TYPES.CHECKBOX,
      props: {
        name: `${tabName}_main`,
      },
    }])
}

const tableOptions = useTableSimple({
  options: {
    isHidePagination: true,
    isHideCaption: true,
  },
  items: () => {
    return tablePermission as any
  },
  columns: () => [
    {
      accessorKey: 'tab',
      header: 'Tab',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'permissions',
      header: 'Permissions',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'main',
      header: 'Main Responsibility',
      type: COLUMN_TYPES.TEXT,

    },
  ],
})

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit({
    ...values,
    display_name: user.fetch.items.find((item) => item.id === values.user_id)?.display_name,
  } as ICollaborators & { display_name: string })
})
</script>
