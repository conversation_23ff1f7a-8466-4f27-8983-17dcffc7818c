import axios from 'axios'

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig()
  const token = getCookie(event, 'token')

  try {
    await axios.get('/auth/logout', {
      baseURL: config.public.baseAPI,
      headers: {
        Authorization: `Bear<PERSON> ${token}`,
      },
    })
  } catch (error) {
    console.log(error)
  } finally {
    setCookie(event, 'token', '', {
      maxAge: -1, // Expire the cookie immediately
      path: '/',
    })
  }

  return sendRedirect(event, '/login', 302)
})
