import { defineEventHandler, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '550e8400-e29b-41d4-a716-446655440000',
      code: 'FNM20250312',
      name: 'ระบบงานส่วนกลางบริษัท FineWork',
      client: 'บริษัทฟินีม่า',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'DRAFT',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      code: 'DOE20250312',
      name: 'DOE-Lakehouse',
      client: 'กรมการจัดหางาน',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'PRE_SALE',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      code: 'DPT20250312',
      name: 'ระบบงาน DPT e-Platform',
      client: 'กรมโยธาธิการและผังเมือง',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'BIZ_CO',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      code: 'DOE20250312',
      name: 'DOE-Lakehouse',
      client: 'กรมการจัดหางาน',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'KICK_OFF',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440004',
      code: 'DOE20250312',
      name: 'DOE-Lakehouse',
      client: 'กรมการจัดหางาน',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'DEVELOPMENT',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440005',
      code: 'DBD20250312',
      name: 'ระบบงาน DBD-eTacc',
      client: 'กรมธุรกิจการค้า กระทรวงพาณิชย์',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'SIGNED',
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440006',
      code: 'DBD20250312',
      name: 'ระบบงาน DBD-eTacc',
      client: 'กรมธุรกิจการค้า กระทรวงพาณิชย์',
      startDate: '2568-05-22T00:00:00+07:00',
      endDate: '2569-05-20T23:59:59+07:00',
      status: 'NGOD2',
    },
  ].map((value) => ({
    value,
    sort: Math.random(),
  }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
