import { Chart as ChartJS, Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale, ArcElement } from 'chart.js'

export default defineNuxtPlugin(() => {
  ChartJS.register(Title, Tooltip, Legend, BarElement, CategoryScale, LinearScale, ArcElement)
  ChartJS.defaults.set({
    font: {
      size: 12,
      family: 'Noto Sans Thai, sans-serif',
    },
    scales: {
      title: {
        font: {
          size: 12,
          family: 'Noto Sans Thai, sans-serif',
          weight: 'bold',
        },
      },
      ticks: {
        font: {
          size: 12,
          family: 'Noto Sans Thai, sans-serif',
        },
      },
      x: {
        ticks: {
          maxRotation: 0,
          minRotation: 0,
          stepSize: 1,
        },
      },
      y: {
        ticks: {
          maxRotation: 0,
          minRotation: 0,
          stepSize: 1,
        },
      },
    },
  })
})
