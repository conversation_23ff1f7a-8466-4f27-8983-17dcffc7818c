<template>
  <PmoCardCollapsible
    title="ข้อมูลคู่แข่ง"
    subtitle="Competitors"
  >
    <Table
      :options="tableOptions"
      @pageChange="competitor.fetchPageChange"
      @search="competitor.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div
          v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
          class="flex justify-end"
        >
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
    <div
      v-if="permission.hasPermission(tab, PMO_PERMISSION.MODIFY)"
      class="flex justify-end pt-2"
    >
      <Button
        color="primary"
        icon="i-heroicons-plus"
        class="rounded-xl"
        @click="onAdd()"
      >
        เพิ่มรายการ
      </Button>
    </div>
  </PmoCardCollapsible>
</template>

<script setup lang="ts">
import { usePmoCompetitorPageLoader, usePmoProjectsPageLoader } from '~/loaders/pmo/project'
import Form from '~/features/pmo/project/ProjectDetail/Confidential/FormTable.vue'

defineProps<{
  tab: TabKey
}>()

const project = usePmoProjectsPageLoader()
const competitor = usePmoCompetitorPageLoader(project.find.item?.id as string)
const noti = useNotification()
const dialog = useDialog()
const overlay = useOverlay()
const addModal = overlay.create(Form)
const editModal = overlay.create(Form)
const permission = useProjectPermission()
const form = {
  validationSchema: toTypedSchema(v.object({
    company: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    detail: v.nullish((v.string())),
  })),
}

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'บริษัท',
      name: 'company',
      placeholder: 'ระบุบริษัท',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'รายละเอียด',
      name: 'detail',
      placeholder: 'ระบุรายละเอียด',
    },
  },
])

competitor.fetchSetLoading()
onMounted(() => {
  competitor.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const tableOptions = useTable({
  options: {
    isHidePagination: true,
  },
  repo: competitor,
  columns: () => [
    {
      accessorKey: 'company',
      header: 'บริษัท',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'detail',
      header: 'รายละเอียด',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

const onEdit = (_value: any) => {
  editModal.open({
    isEditing: true,
    values: _value,
    title: 'ข้อมูลคู่แข่ง',
    subtitle: 'Competitors',
    formFields: formFields,
    form: form,
    status: () => competitor.update.status,
    onSubmit: (values: ITemplateDocument) => {
      competitor.updateRun(_value.id, {
        data: values,
      })
    },
  })
}

const onDelete = (values: any) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบข้อมูลคู่แข่ง "${values.company}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    competitor.deleteRun(values.id)
  })
}

const onAdd = () => {
  addModal.open({
    title: 'ข้อมูลคู่แข่ง',
    subtitle: 'Competitors',
    formFields: formFields,
    form: form,
    status: () => competitor.add.status,
    onSubmit: (values: any) => {
      competitor.addRun({
        data: values,
      })
    },
  })
}

useWatchTrue(
  () => competitor.add.status.isSuccess,
  () => {
    addModal.close()

    noti.success({
      title: 'เพิ่มข้อมูลคู่แข่งสำเร็จ',
      description: 'คุณได้เพิ่มข้อมูลคู่แข่งเรียบร้อยแล้ว',
    })

    competitor.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => competitor.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มข้อมูลคู่แข่งไม่สำเร็จ',
      description: StringHelper.getError(competitor.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มข้อมูลคู่แข่ง กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => competitor.update.status.isSuccess,
  () => {
    editModal.close()
    dialog.close()

    noti.success({
      title: 'แก้ไขข้อมูลคู่แข่งสำเร็จ',
      description: 'คุณได้แก้ไขข้อมูลคู่แข่งเรียบร้อยแล้ว',
    })

    competitor.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => competitor.update.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'แก้ไขข้อมูลคู่แข่งไม่สำเร็จ',
      description: StringHelper.getError(competitor.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขข้อมูลคู่แข่ง กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => competitor.delete.status.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'ลบข้อมูลคู่แข่งสำเร็จ',
      description: 'คุณได้ลบข้อมูลคู่แข่งเรียบร้อยแล้ว',
    })

    competitor.fetchPage(1, '', {
      params: {
        limit: 999,
      },
    })
  },
)

useWatchTrue(
  () => competitor.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบข้อมูลคู่แข่งไม่สำเร็จ',
      description: StringHelper.getError(competitor.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบข้อมูลคู่แข่ง กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
