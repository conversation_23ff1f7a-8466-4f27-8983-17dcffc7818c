<template>
  <NuxtLayout>
    <Projects />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import Projects from '~/features/admin/Projects/index.vue'

definePageMeta({
  layout: 'admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.admin.projects.permissions,
  },
})

useSeoMeta({
  title: routes.admin.projects.label,
})

useApp().definePage({
  title: routes.admin.projects.label,
  breadcrumbs: [routes.admin.projects],
  sub_title: 'Manage Projects',
})
</script>
