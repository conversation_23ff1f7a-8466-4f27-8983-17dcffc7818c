import type { TabsItem } from '@nuxt/ui'
import { PROJECT_TAB_LABEL, PROJECT_TAB_TYPE } from '~/constants/projectTab'
import { usePmoProjectsPageLoader } from '~/loaders/pmo/project'

export const useProjectPermission = () => {
  const auth = useAuth()
  const project = usePmoProjectsPageLoader()

  const hasPermission = (module: TabKey, ...permission: PMO_PERMISSION[]) => {
    if (auth.hasPermission(UserModule.PMO, Permission.SUPER)) {
      return true
    }

    if (!project.find.item?.permission) {
      return false
    }

    const key = `${module}_permission` as keyof ICollaborators
    const current = project.find.item.permission[key] as PMO_PERMISSION | null

    return current !== null && permission.includes(current)
  }

  const sidebarPmoHorizontal = (project?: IPmoProject): TabsItem[] => {
    const firstRow: TabsItem[] = []

    firstRow.push({
      label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.INFO],
      value: PROJECT_TAB_TYPE.INFO,
    })

    // เริ่มจาก filteredTabs ตาม status ก่อน
    let filteredTabs = [...tabList]

    if (
      project?.status === PROJECT_STATUS.DRAFT
      || project?.status === PROJECT_STATUS.TOR
      || project?.status === PROJECT_STATUS.BIDDING
    ) {
      filteredTabs = tabList.filter((tab) =>
        [PROJECT_TAB_TYPE.CONFIDENTIAL,
          PROJECT_TAB_TYPE.SALES,
          PROJECT_TAB_TYPE.PRESALES,
          PROJECT_TAB_TYPE.BIDDING].includes(tab.value),
      )
    }

    // วนทุก tab แล้วตรวจ permission (Super admin ก็ผ่านได้เพราะ permission ของ Super มีสิทธิ์ทั้งหมด)
    filteredTabs.forEach((tab) => {
      const userPermission = (project?.permission as any)?.[`${tab.value.toLowerCase()}_permission`]

      // Super admin หรือมีสิทธิ์ของ tab
      if (
        auth.hasPermission(UserModule.PMO, Permission.SUPER)
        || tab.permissions.includes(userPermission)
      ) {
        firstRow.push({
          label: tab.label,
          value: tab.value,
          icon: tab.icon,
        })
      }
    })

    // ถ้ามีสิทธิ์ setting super ก็เพิ่ม collaborators
    if (auth.hasPermission(UserModule.PMO, Permission.SUPER, Permission.ADMIN)) {
      firstRow.push({
        label: PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.COLLABORATORS],
        value: PROJECT_TAB_TYPE.COLLABORATORS,
        icon: 'lucide:users',
        slot: 'collaborators' as const,
      })
    }

    return firstRow
  }

  const sidebarPmoCommentChannel = (project?: IPmoProject): TabsItem[] => {
    const firstRow: TabsItem[] = []

    let filteredTabs = [...PMOCommentChannelKeyList]

    if (
      project?.status === PROJECT_STATUS.DRAFT
      || project?.status === PROJECT_STATUS.TOR
      || project?.status === PROJECT_STATUS.BIDDING
    ) {
      filteredTabs = PMOCommentChannelKeyList.filter((tab) =>
        [PMOCommentChannel.OVERALL,
          PMOCommentChannel.CONFIDENTIAL,
          PMOCommentChannel.SALES,
          PMOCommentChannel.PRESALES,
          PMOCommentChannel.BIDDING].includes(tab.value),
      )
    }

    // วนทุก tab แล้วตรวจ permission (Super admin ก็ผ่านได้เพราะ permission ของ Super มีสิทธิ์ทั้งหมด)
    filteredTabs.forEach((tab) => {
      firstRow.push({
        label: tab.label,
        value: tab.value,
        permissions: tab.permissions,
      })
    })

    return firstRow
  }

  return {
    sidebarPmoHorizontal,
    sidebarPmoCommentChannel,
    hasPermission,
  }
}
