import { eachDayOfInterval, isWeekend } from 'date-fns'

export const useWorkingDayInMonth = (
  year: number,
  month: number,
  holidays: string[],
) => {
  const start = new Date(year, month, 1)
  const end = new Date(year, month + 1, 0)

  const days = eachDayOfInterval({
    start,
    end,
  })

  const workingDays = days.reduce((count, date) => {
    if (!isWeekendOrHoliday(date, holidays)) {
      return count + 1
    }

    return count
  }, 0)

  return workingDays
}

export const isWeekendOrHoliday = (date: Date, holidays: string[]) => {
  const dateStr = TimeHelper.getDateFormTime(date)

  return isWeekend(date) || holidays.includes(dateStr)
}
