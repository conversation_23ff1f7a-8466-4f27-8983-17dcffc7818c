# FineWork Frontend - Technical Specifications

## Technologies Used

### Core Framework
- **Nuxt.js 4**: Full-stack Vue framework with SSR/SPA capabilities
- **Vue 3**: Progressive JavaScript framework with Composition API
- **TypeScript**: Type-safe JavaScript development
- **Bun**: JavaScript runtime and package manager (preferred over npm/yarn)

### UI & Styling
- **@finema/core v2.26.8**: Custom Finema design system and UI components
- **Tailwind CSS**: Utility-first CSS framework
- **Heroicons & Custom Icons**: Icon library for consistent visual elements
- **Chart.js + vue-chartjs**: Data visualization and charts

### Development Tools
- **ESLint 9.0.0**: Code linting and formatting
- **Vitest**: Unit testing framework
- **Playwright**: End-to-end testing
- **Husky**: Git hooks for code quality
- **lint-staged**: Pre-commit code formatting

### Build & Deployment
- **Docker**: Containerized deployment with multi-stage build
- **Bun**: Build tool and runtime (Alpine Linux in production)

## Development Setup

### Prerequisites
- Bun runtime installed
- Node.js environment
- Access to Finema design system (@finema/core)

### Local Development
```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Open at http://localhost:3000
```

### Environment Configuration
```bash
# Required environment variables (.env)
PORT=3000
APP_BASE_URL=http://localhost:3000
APP_BASE_API=https://finework-api.finema.dev
APP_BASE_INTERNAL_API=http://localhost:3000/api
APP_BASE_API_MOCK=http://localhost:3000/api/mock
```

### Build Commands
```bash
# Development
bun run dev           # Start dev server with auto-open

# Production
bun run build         # Build for production
bun run preview       # Preview production build

# Testing
bun run test          # Run unit tests
bun run test:run      # Run tests once
bun run test:coverage # Generate coverage report

# Linting
bun run lint          # Check code quality
bun run lint:fix      # Auto-fix linting issues
```

## Technical Constraints

### Backend Integration
- **Primary API**: `finework-api.finema.dev`
- **Authentication**: Slack OAuth with JWT tokens
- **Request Headers**: Bearer token authentication required
- **Mock Data**: Local endpoints at `/api/mock/*` for development

### Browser Support
- Modern browsers supporting ES2020+
- Mobile-responsive design required
- Progressive Web App capabilities

### Performance Requirements
- Fast initial page load times
- Optimized bundle size with code splitting
- Efficient state management with minimal re-renders

## Dependencies

### Production Dependencies
```json
{
  "@finema/core": "^2.26.8",        // Custom design system
  "chart.js": "^4.4.9",            // Chart visualization
  "nuxt": "^4.0.1",                // Framework
  "vue": "^3.5.18",                // Core library
  "vue-chartjs": "^5.3.2",         // Vue Chart.js wrapper
  "vue-router": "^4.5.1"           // Routing
}
```

### Development Dependencies
```json
{
  "@vue/test-utils": "^2.4.6",     // Vue testing utilities
  "eslint": "^9.0.0",              // Code linting
  "happy-dom": "^18.0.1",          // DOM testing environment
  "playwright-core": "^1.54.1",    // E2E testing
  "vitest": "^3.2.4"               // Unit testing
}
```

## Tool Usage Patterns

### State Management
- **Pinia**: Primary state management (via Nuxt built-in)
- **Composables**: Reusable stateful logic
- **Cookie State**: Persistent authentication state

### API Communication
- **Axios**: HTTP client for API requests
- **Composables**: Centralized request configuration
- **Error Handling**: Standardized error responses

### Code Organization
- **Feature-based structure**: Domain-driven file organization
- **Composable pattern**: Shared logic extraction
- **TypeScript interfaces**: Strong typing throughout

### Testing Strategy
- **Unit Tests**: Component and composable testing with Vitest
- **E2E Tests**: User flow testing with Playwright
- **Manual Testing**: UI/UX validation with mock data

## Docker Configuration

### Multi-stage Build
```dockerfile
# Build stage with Bun Alpine
FROM oven/bun:1.2.19-alpine AS builder

# Production stage
FROM oven/bun:1.2.19-alpine
```

### Production Environment
- **Runtime**: Bun on Alpine Linux
- **Port**: 3000
- **Health checks**: Built-in Nuxt server monitoring
- **Environment**: Production API endpoints configured
