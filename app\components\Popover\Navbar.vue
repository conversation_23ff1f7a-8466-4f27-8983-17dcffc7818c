<template>
  <Popover
    :content="{
      align: 'center',
      side: 'bottom',
      sideOffset: 8,
    }"
  >
    <Button
      icon="mingcute:dot-grid-line"
      variant="ghost"
      class="text-gray-500"
    />

    <template #content>
      <Card class="w-[300px]">
        <div
          class="grid grid-cols-3 gap-6"
        >
          <Button
            v-for="item in auth.menusNavbar.value"
            :key="item.to"
            class="flex flex-col items-center p-0 text-center"
            variant="link"
            :to="item.to"
          >
            <div class="flex h-[42px] items-center justify-center">
              <img
                :src="item.icon"
                class="h-auto w-[32px]"
              />
            </div>
            <span class="text-sm font-medium text-gray-700">
              {{ item.label }}
            </span>
          </Button>
        </div>
      </Card>
    </template>
  </Popover>
</template>

<script lang="ts" setup>
const auth = useAuth()
</script>
