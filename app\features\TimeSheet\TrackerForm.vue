<template>
  <Form
    class="space-y-4"
    @submit="onSubmit"
  >
    <FormFields
      class="
          grid gap-3
          lg:grid-cols-3
        "
      :options="formFields"
    />
    <div class="flex justify-end gap-2">
      <Button
        variant="outline"
        label="Cancel"
        @click=" emit('cancel')"
      />
      <Button
        :loading="trackers.add.status.isLoaded || trackers.update.status.isLoaded"
        :disabled="trackers.add.status.isLoaded || trackers.update.status.isLoaded"
        type="submit"
        icon="mdi-check"
        :label="isEdit ? 'Update' : 'Save'"
      />
    </div>
  </Form>
</template>

<script lang="ts" setup>
import { useProjectsPageLoader } from '~/loaders/admin/project'
import { useSGAPageLoader } from '~/loaders/admin/sga'
import { useTimeSheetPageLoader } from '~/loaders/admin/timesheet'
import { TRACKER_TYPE } from '~/constants/timesheet'

const emit = defineEmits(['cancel', 'success-create', 'success-update'])

const props = defineProps<{
  date?: string
  userId?: string
  item?: ITimesheet
  isEdit?: boolean
}>()

const noti = useNotification()
const projects = useProjectsPageLoader()
const sgaTypes = useSGAPageLoader()
const trackers = useTimeSheetPageLoader()

projects.fetchSetLoading()
sgaTypes.fetchSetLoading()
onMounted(() => {
  projects.fetchPage()
  sgaTypes.fetchPage()
})

const typeOptions = TRACKER_TYPE_OPTIONS
const projectOptions = computed(() => projects.fetch.items?.map((p: any) => ({
  value: p.code,
  label: p.name,
})) || [])

const leaveTypeOptions = LEAVE_OPTIONS
const sgaTypeOptions = computed(() => sgaTypes.fetch.items?.map((s: any) => ({
  value: s.id,
  label: s.name,
})) || [])

const timingOptions = [0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8].map((t) => ({
  value: t,
  label: t.toString(),
}))

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      type: v.pipe(v.string(), v.nonEmpty('กรุณาเลือกประเภทงาน')),
      project_code: v.nullish(v.string(), ''),
      leave_type: v.nullish(v.string(), ''),
      sga_id: v.nullish(v.string(), ''),
      timing: v.pipe(v.number(), v.minValue(0.5, 'กรุณาเลือกเวลา')),
      description: v.optional(v.pipe(v.string(), v.maxLength(256)), ''),
    }),
  ),
  initialValues: props.item
    ? {
      ...props.item,
      project_code: typeof props.item.project === 'object' ? props.item.project.code : props.item.project,
    }
    : undefined,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'type',
      placeholder: 'เลือกประเภทงาน',
      label: 'Work Type',
      required: true,
      options: typeOptions,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    isHide: ![TRACKER_TYPE.PROJECT, TRACKER_TYPE.OT].includes(form.values.type as TRACKER_TYPE),
    props: {
      name: 'project_code',
      placeholder: 'เลือก Project',
      label: 'Project',
      required: [TRACKER_TYPE.PROJECT, TRACKER_TYPE.OT].includes(form.values.type as TRACKER_TYPE),
      options: projectOptions.value,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    isHide: form.values.type !== TRACKER_TYPE.LEAVE,
    props: {
      name: 'leave_type',
      placeholder: 'เลือก Leave Type',
      label: 'Leave Type',
      required: form.values.type === TRACKER_TYPE.LEAVE,
      options: leaveTypeOptions,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    isHide: form.values.type !== TRACKER_TYPE.SGA,
    props: {
      name: 'sga_id',
      placeholder: 'เลือก SGA Type',
      label: 'SGA Type',
      required: form.values.type === TRACKER_TYPE.SGA,
      options: sgaTypeOptions.value,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'timing',
      placeholder: 'เลือกเวลา (ชั่วโมง)',
      label: 'Timing (hr.)',
      required: true,
      options: timingOptions,
    },
  },
  {
    class: 'lg:col-span-3',
    type: INPUT_TYPES.TEXTAREA,
    props: {
      name: 'description',
      placeholder: 'ระบุรายละเอียดงาน...',
      label: 'Description',
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  // const projectData = projects.fetch.items?.find((p: any) => p.code === values.project)

  const payload = {
    description: values.description,
    timing: values.timing,
    type: values.type,
    project_code: values.project_code || null,
    sga_id: values.sga_id || null,
    leave_type: values.leave_type || null,
    date: props.date,
  }

  if (props.isEdit && props.item) {
    trackers.updateRun(props.item?.id || '1', {
      data: payload,
    })
  } else {
    trackers.addRun({
      data: {
        items: [payload],
      },
    })
  }
})

useWatchTrue(() => trackers.add.status.isSuccess, () => {
  emit('success-create')

  noti.success({
    icon: 'mdi-check-circle',
    title: 'บันทึกข้อมูลสำเร็จ',
  })
})

useWatchTrue(() => trackers.add.status.isError, () => {
  noti.error({
    title: 'บันทึกข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(trackers.add.status.errorData, 'เกิดข้อผิดพลาด'),

  })
})

useWatchTrue(() => trackers.update.status.isSuccess, () => {
  emit('success-update')

  noti.success({
    icon: 'mdi-check-circle',
    title: 'อัพเดทข้อมูลสำเร็จ',
  })
})

useWatchTrue(() => trackers.update.status.isError, () => {
  noti.error({
    title: 'อัพเดทข้อมูลไม่สำเร็จ',
    description: StringHelper.getError(trackers.update.status.errorData, 'เกิดข้อผิดพลาด'),
  })
})
</script>
