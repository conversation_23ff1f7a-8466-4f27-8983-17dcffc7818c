<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่ม Project
      </Button>
    </TeleportSafe>
    <FormFields
      class="
         mb-4 grid w-full gap-4
        lg:grid-cols-6
      "
      :options="formFields"
    />
    <Table
      :options="tableOptions"
      @pageChange="projects.fetchPageChange"
      @search="projects.fetchSearch"
    >
      <template #type-team="{ row }">
        <Badge
          variant="soft"
        >
          {{ row.original.team }}
        </Badge>
      </template>

      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useProjectsPageLoader } from '~/loaders/admin/project'
import Form from '../Projects//Form.vue'
import { watchDebounced } from '@vueuse/core'

const projects = useProjectsPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)
const route = useRoute()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหาโปรเจค',
    },
  },

])

const tableOptions = useTable({
  options: {
    isRouteChange: true,
  },
  repo: projects,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'w-1/3',
        },
      },
    },
    {
      accessorKey: 'code',
      header: 'Project Code',
      type: COLUMN_TYPES.TEXT,

    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

projects.fetchSetLoading()
onMounted(() => {
  projects.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const onEdit = (values: IProject) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => projects.update.status,
    onSubmit: (payload: IProject) => {
      projects.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  addModal.open({
    status: () => projects.add.status,
    onSubmit: (payload: IProject) => {
      projects.addRun({
        data: payload,
      })
    },
  })
}

const onDelete = (values: IProject) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบ Project "${values.name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    projects.deleteRun(values.id)
  })
}

watchDebounced(form.values, (values) => {
  projects.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

useWatchTrue(
  () => projects.update.status.isSuccess,
  () => {
    editModal.close()
    projects.fetchPage()

    noti.success({
      title: 'แก้ไขProjectสำเร็จ',
      description: 'คุณได้แก้ไขProjectเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => projects.update.status.isError,
  () => {
    editModal.close()
    dialog.close()
    noti.error({
      title: 'แก้ไขProjectไม่สำเร็จ',
      description: StringHelper.getError(projects.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขProject กรุณาลองใหม่อีกครั้ง'),

    })
  },
)

useWatchTrue(
  () => projects.delete.status.isSuccess,
  () => {
    editModal.close()
    projects.fetchPage()
    dialog.close()
    noti.success({
      title: 'ลบProjectสำเร็จ',
      description: 'คุณได้ลบProjectเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => projects.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบProjectไม่สำเร็จ',
      description: StringHelper.getError(projects.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบProject กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => projects.add.status.isSuccess,
  () => {
    addModal.close()
    projects.fetchPage()
    dialog.close()
    noti.success({
      title: 'เพิ่มProjectสำเร็จ',
      description: 'คุณได้เพิ่มProjectเรียบร้อยแล้ว',
    })

    dialog.close()
  },
)

useWatchTrue(
  () => projects.add.status.isError,
  () => {
    addModal.close()
    dialog.close()
    noti.error({
      title: 'เพิ่มProjectไม่สำเร็จ',
      description: StringHelper.getError(projects.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มProject กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
