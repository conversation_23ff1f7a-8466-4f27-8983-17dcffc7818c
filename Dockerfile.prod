# Build stage
FROM oven/bun:1.2.21-alpine AS builder

ENV HOST=0.0.0.0
ENV PORT=3000
ENV APP_BASE_URL="https://work.finema.co"
ENV APP_BASE_API="https://work-api.finema.co"
ENV APP_BASE_INTERNAL_API="https://work.finema.co/api"
ENV APP_BASE_API_MOCK="https://work.finema.co/api/mock"


# Set the working directory
WORKDIR /app

# Cache npm modules
COPY package.json bun.lock ./

RUN bun install --frozen-lockfile

# Copy the source code and build the project
COPY . .
RUN bun run postinstall
RUN bun run build

# Final stage
FROM oven/bun:1.2.21-alpine

# Set the working directory
WORKDIR /app

# Copy the build output and node_modules from the build stage
COPY --from=builder /app/.output /app/.output

# Run the application
CMD ["bun", ".output/server/index.mjs"]
