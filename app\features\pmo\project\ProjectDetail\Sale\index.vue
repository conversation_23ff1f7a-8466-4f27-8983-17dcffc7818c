<template>
  <div class="space-y-8">
    <Confidential
      :tab="PROJECT_TAB_KEY[PROJECT_TAB_TYPE.SALES]"
      :project-id="projectId"
      :tab-label="PROJECT_TAB_LABEL[PROJECT_TAB_TYPE.SALES]"
    />
    <PmoCardCollapsible no-underline>
      <CheckList
        :project-id="projectId"
        :tab="PROJECT_TAB_TYPE.SALES"
      />
    </PmoCardCollapsible>

    <PmoCardCollapsible
      title="ข้อมูลอัปเดต Sales"
      sub-title="Comment"
    >
      <Comment
        :project-id="projectId"
        :channel="PROJECT_TAB_TYPE.SALES"
      />
    </PmoCardCollapsible>
    <BudgetInfo
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.SALES"
    />
    <BiddingInfo
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.SALES"
    />
    <PmoCardCollapsible
      title="เอกสารเทมเพลต"
      subtitle="Document Template"
    >
      <FileTemplate :tab="PROJECT_TAB_TYPE.SALES" />
    </PmoCardCollapsible>

    <GroupTemplateFile
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.SALES"
    />

    <Remark
      :project-id="projectId"
      :tab="PROJECT_TAB_TYPE.SALES"
    />
  </div>
</template>

<script lang="ts" setup>
import CheckList from '~/container/Pmo/CheckList.vue'
import Confidential from '../Confidential.vue'
import Comment from '../Comment.vue'
import BudgetInfo from './BudgetInfo.vue'
import BiddingInfo from '../BiddingInfo.vue'
import Remark from '../Remark.vue'
import FileTemplate from '../FileTemplate.vue'
import { PROJECT_TAB_KEY, PROJECT_TAB_TYPE } from '~/constants/projectTab'

defineProps<{ projectId: string }>()
</script>
