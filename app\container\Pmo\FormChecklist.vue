<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขงานที่ต้องทำ' : 'งานที่ต้องทำ'"
    description="Checklist"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { useUserPageLoader } from '~/loaders/admin/user'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: IChecklistItem | null
  status: () => IStatus
  onSubmit: (values: IChecklistItem) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    detail: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    assignee_id: v.nullish(v.pipe(v.string())),
  })),
})

const user = useUserPageLoader()
const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'งานที่ต้องทำ',
      name: 'detail',
      placeholder: 'ระบุงานที่ต้องทำ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'assignee_id',
      label: 'ผู้รับผิดชอบ',
      placeholder: 'ชื่อ (Name)',
      // clearable: true,
      loading: user.fetch.status.isLoading,
      options: user.fetch.items?.map((item: IUser) => ({
        label: item.display_name,
        value: item.id,
        avatar: {
          src: item.avatar_url,
          alt: item.display_name,
        },
      })) || [],
      searchable: true,
    },
  },
])

onMounted(() => {
  user.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })
})

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as IChecklistItem)
})
</script>
