<template>
  <div class="pt-6 pb-2 text-lg font-bold">
    Where are you working today?
  </div>
  <div class="grid grid-cols-2 gap-3 lg:grid-cols-3 xl:grid-cols-6">
    <div
      v-for="(item, index) in options"
      :key="index"
      :class="[
        'flex h-26 w-full cursor-pointer flex-col items-center justify-center rounded-lg border md:flex-wrap',
        selected.includes(item.value)
          ? 'border-primary-500 bg-primary-50 border-2'
          : 'border-gray-200 bg-white hover:bg-gray-50',
      ]"
      @click="toggleCheckinType(item.value)"
    >
      <img
        :src="iconImages?.[item.value]"
        :alt="iconImages?.[item.value]"
        class="size-7"
      />
      <span class="mt-2 text-sm font-medium text-gray-700">
        {{ item.label }}
      </span>
    </div>
  </div>
  <Form
    @submit="onSubmit"
  >
    <div v-if="selected.length > 0">
      <div
        v-for="checkin in selected"
        :key="checkin"
      >
        <OfficeHQ
          v-if="checkin === CHECKIN.OFFICE_HQ"
          class="mt-5"
          :form="form"
          @close="toggleCheckinType(CHECKIN.OFFICE_HQ)"
        />
        <OfficeAKV
          v-if="checkin === CHECKIN.OFFICE_AKV"
          :form="form"
          class="mt-5"
          @close="toggleCheckinType(CHECKIN.OFFICE_AKV)"
        />
        <WFH
          v-if="checkin === CHECKIN.WFH"
          :form="form"
          class="mt-5"
          @close="toggleCheckinType(CHECKIN.WFH)"
        />
        <Onsite
          v-if="checkin === CHECKIN.ONSITE"
          :form="form"
          class="mt-5"
          @close="toggleCheckinType(CHECKIN.ONSITE)"
        />
        <BusinessTrip
          v-if="checkin === CHECKIN.BUSINESS_TRIP"
          :form="form"
          class="mt-5"
          @close="toggleCheckinType(CHECKIN.BUSINESS_TRIP)"
        />
        <Leave
          v-if="checkin === CHECKIN.LEAVE"
          :form="form"
          class="mt-5"
          :is-edit="options?.length === 1"
          @close="toggleCheckinType(CHECKIN.LEAVE)"
        />
      </div>
    </div>
    <div class="mt-4 flex justify-end gap-3">
      <Button
        icon="tabler:clock-check"
        type="submit"
        :disabled="selected.length === 0"
        @click="onSubmit"
      >
        Let's Clock-In
      </Button>
    </div>
  </Form>
</template>

<script lang="ts" setup>
import OfficeHQ from './CheckinForm/OfficeHQ.vue'
import OfficeAKV from './CheckinForm/OfficeAKV.vue'
import Onsite from './CheckinForm/Onsite.vue'
import BusinessTrip from './CheckinForm/BusinessTrip.vue'
import Leave from './CheckinForm/Leave.vue'
import WFH from './CheckinForm/WFH.vue'
import { useCheckInsPageLoader } from '~/loaders/admin/checkin'

const emit = defineEmits(['success'])
const dialog = useDialog()
const selected = ref<string[]>([])
const checkin = useCheckInsPageLoader()
const noti = useNotification()
const init = ref(false)
const {
  officeAkvSchema,
  officeHqSchema,
  officeWFHSchema,
  onsiteSchema,
  businessTripSchema,
  leaveSchema,
  transformToCheckinRequest,
  sendSlackMsg,
} = useCheckinForm()

onMounted(() => {
  init.value = true
})

const periodKeys = [
  'office_hq_period',
  'office_akv_period',
  'office_wfh_period',
  'leave_period',
  'business_period',
]

const handleMultipleSelection = () => {
  // ตั้ง HALF_MORNING สำหรับทุก field
  [...periodKeys].forEach((key) => {
    if (form.values[key] === PERIOD.FULL_DAY || !form.values[key]) {
      form.setFieldValue(key, PERIOD.HALF_MORNING)
    }
  })

  // ตั้ง HALF_MORNING สำหรับ onsite_period ใน checkin
  form.values.checkin?.forEach((_: any, index: number) => {
    if (!form.values.checkin[index].onsite_period || form.values.checkin[index].onsite_period === PERIOD.FULL_DAY) {
      form.setFieldValue(`checkin.${index}.onsite_period`, PERIOD.HALF_MORNING)
    }
  })
}

const schemaMap: Record<string, any> = {
  [CHECKIN.OFFICE_HQ]: officeHqSchema,
  [CHECKIN.OFFICE_AKV]: officeAkvSchema,
  [CHECKIN.WFH]: officeWFHSchema,
  [CHECKIN.ONSITE]: onsiteSchema,
  [CHECKIN.BUSINESS_TRIP]: businessTripSchema,
  [CHECKIN.LEAVE]: leaveSchema,
}

const form = useForm({
  validationSchema: computed(() => {
    const rules: any = {
      date: v.pipe(v.string(), v.nonEmpty('')),
      type: v.optional(v.array(v.string())),
    }

    if (!init.value) return toTypedSchema(v.object(rules))

    for (const checkinType of selected.value) {
      Object.assign(rules, schemaMap[checkinType])

      if (selected.value.length > 1 || form.values.checkin?.length > 1) {
        handleMultipleSelection()

        return
      }

      // Single selection: set FULL_DAY
      switch (checkinType) {
        case CHECKIN.OFFICE_HQ:
          form.setFieldValue('office_hq_period', PERIOD.FULL_DAY)

          break
        case CHECKIN.OFFICE_AKV:
          form.setFieldValue('office_akv_period', PERIOD.FULL_DAY)

          break
        case CHECKIN.WFH:
          form.setFieldValue('office_wfh_period', PERIOD.FULL_DAY)

          break
        case CHECKIN.ONSITE:
          form.setFieldValue('checkin.0.onsite_period', PERIOD.FULL_DAY)

          break
        case CHECKIN.BUSINESS_TRIP:
          form.setFieldValue('business_period', PERIOD.FULL_DAY)

          break
        case CHECKIN.LEAVE:
          form.setFieldValue('leave_period', PERIOD.FULL_DAY)

          break
      }
    }

    return toTypedSchema(v.object(rules))
  }),
  keepValuesOnUnmount: true,
  initialValues: {
    date: new Date().toISOString(),
    type: [],
  },
})

const options = [
  {
    label: CHECKIN_LABEL[CHECKIN.OFFICE_HQ],
    value: CHECKIN.OFFICE_HQ,
    icon: CHECKIN_ICONS[CHECKIN.OFFICE_HQ],
  },
  {
    label: CHECKIN_LABEL[CHECKIN.OFFICE_AKV],
    value: CHECKIN.OFFICE_AKV,
    icon: CHECKIN_ICONS[CHECKIN.OFFICE_AKV],
  },
  {
    label: CHECKIN_LABEL[CHECKIN.WFH],
    value: CHECKIN.WFH,
    icon: CHECKIN_ICONS[CHECKIN.WFH],
  },
  {
    label: CHECKIN_LABEL[CHECKIN.ONSITE],
    value: CHECKIN.ONSITE,
    icon: CHECKIN_ICONS[CHECKIN.ONSITE],
  },
  {
    label: CHECKIN_LABEL[CHECKIN.BUSINESS_TRIP],
    value: CHECKIN.BUSINESS_TRIP,
    icon: CHECKIN_ICONS[CHECKIN.BUSINESS_TRIP],
  },
  {
    label: CHECKIN_LABEL[CHECKIN.LEAVE],
    value: CHECKIN.LEAVE,
    icon: CHECKIN_ICONS[CHECKIN.LEAVE],
  },
]

const onSubmit = form.handleSubmit((values) => {
  const result = transformToCheckinRequest(values)

  const titleDialog
    = result.items && result.items.length > 0
      ? result.items.map((t: any) => CHECKIN_LABEL[t.type === CHECKIN.LEAVE
        ? values.leave_type as CHECKIN
        : t.type as CHECKIN]
        + `${t.location ? ` - ${t.location}` : ''} ` + ` (${PERIOD_LABEL[t.period as PERIOD]})`).join(' / ')
      : ''

  dialog.confirm({
    title: 'Confirm Clock-In',
    description: `Do you confirm to Clock-In at ${titleDialog} ? `,
    confirmText: 'Confirm',
    cancelText: 'Cancel',
  }).then(() => {
    checkin.addRun({
      data: result,
    })

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
})

const toggleCheckinType = (name: string) => {
  if (selected.value.includes(name)) {
    selected.value = selected.value.filter((n) => n !== name)
  } else {
    selected.value.push(name)

    if (name === CHECKIN.ONSITE) {
      form.setFieldValue('checkin', [
        {
          location_onsite: undefined,
          onsite_period: undefined,
        },
      ])
    }
  }

  form.setFieldValue('type',
    selected.value,
  )
}

useWatchTrue(
  () => checkin.add.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'Clock-In successfully',
      description: 'You have clocked in successfully.',
    })

    sendSlackMsg(checkin.add.item as any)
    emit('success')
  },
)

useWatchTrue(
  () => checkin.add.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'Clock-In failed',
      description: StringHelper.getError(checkin.add.status.errorData, 'An error occurred while saving changes, please try again'),
    })
  },
)

watch(() => [form.values.leave_period], () => {
  if (form.values.leave_period === PERIOD.MANY_DAYS) {
    form.setFieldValue('type', [CHECKIN.LEAVE])
    selected.value = [CHECKIN.LEAVE]
  }
})

watch(() => [form.values.type], () => {
  if (form.values.leave_period === PERIOD.MANY_DAYS && form.values.type.length > 1) {
    form.setFieldValue('leave_period', undefined)
  }
})
</script>
