export const useOrgMinistryPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IMinistry>({
    baseURL: '/ministries',
    getBaseRequestOptions: options.auth,
  })
}

export const useOrgDepartmentPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IDepartment>({
    baseURL: '/departments',
    getBaseRequestOptions: options.auth,
  })
}

export const useOrgMinistryAdminPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IMinistry>({
    baseURL: '/admin/ministries',
    getBaseRequestOptions: options.auth,
  })
}

export const useOrgDepartmentAdminPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IDepartment>({
    baseURL: '/admin/departments',
    getBaseRequestOptions: options.auth,
  })
}
