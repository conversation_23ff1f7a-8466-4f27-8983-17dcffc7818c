<template>
  <NuxtLayout>
    <Project />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import Project from '~/features/pmo/project/Projects/index.vue'

definePageMeta({
  layout: 'pmo',
  middleware: 'auth',
  accessGuard: {
    permissions: routes.pmo.project.projects.permissions,
  },
})

useSeoMeta({
  title: routes.pmo.project.projects.label,
})

useApp().definePage({
  title: routes.pmo.project.projects.label,
  sub_title: 'All Projects',
  breadcrumbs: [routes.pmo.project.projects],
})
</script>
