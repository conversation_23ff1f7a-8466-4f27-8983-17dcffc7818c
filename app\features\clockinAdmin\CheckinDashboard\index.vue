<template>
  <TeleportSafe to="#page-header">
    <Button
      v-if="checkin.fetch.items && checkin.fetch.items.length > 0"
      label="Export CSV"
      size="xl"
      class="w-fit"
      variant="outline"
      icon="proicons:add-square-multiple"
      color="neutral"
      @click="exportToExcel"
    />
  </TeleportSafe>
  <div class="mb-6 flex flex-col gap-4">
    <Form>
      <FormFields
        class="
            grid items-start gap-x-2
            md:gap-x-4
            lg:grid-cols-2
          "
        :options="filterFields"
      />
    </Form>
    <Card v-if="checkin.fetch.items.length >0">
      <div class="mb-3 text-lg font-bold">
        Overview by clock-in type
      </div>
      <div class="h-[200px] md:h-[300px] lg:h-[400px]">
        <Bar
          :data="chartData"
          :options="chartOptions"
        />
      </div>
    </Card>
    <div class="grid">
      <Table
        :options="tableOptions"
        @pageChange="checkin.fetchPage"
        @search="checkin.fetchSearch"
      >
        <template #type-cell="{ row }">
          <div class="flex items-center gap-1">
            <img
              :src="useGetIconCheckin(row.original)"
              alt="icon"
              class="size-6"
            />
            {{ getLabel(row.original) }}
            <div v-if="row.original.type === CHECKIN.LEAVE">
              ({{ row.original.leave_period?.startsWith('HALF') ? 'Half Day' : 'Full Day' }})
            </div>
            <div v-else-if="row.original.type === CHECKIN.ONSITE">
              ({{ row.original.location }})
            </div>
          </div>
        </template>
        <template #name-cell="{ row }">
          <AvatarProfile :item="row.original.user" />
        </template>
        <template #check-in-cell="{ row }">
          <Badge
            v-bind="getCheckinBadge(getCheckinStatus(row.original))"
            variant="subtle"
          />
        </template>
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ICheckinItem } from '~/types/checkin'
import { Bar } from 'vue-chartjs'
import {
  CHECKIN_OPTIONS,
  CHECKIN,
} from '~/constants/checkin'
import { useAdminCheckInsPageLoader } from '~/loaders/admin/checkin'
import { useTeamPageLoader } from '~/loaders/admin/team'
import { startOfMonth, endOfMonth, format } from 'date-fns'
import { watchDebounced } from '@vueuse/core'
import { useCheckinUtils } from '~/composables/useCheckinUtils'

const team = useTeamPageLoader()
const checkin = useAdminCheckInsPageLoader()
const currentDate = ref(new Date())

const {
  getCheckinStatus,
  getLabel,
  useGetIconCheckin,
} = useCheckinUtils()

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  elements: {
    bar: {
      borderWidth: 0,
      borderRadius: 4,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        stepSize: 20,
      },
    },
  },
}

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      date: v.nullish(v.object({
        year: v.number(),
        month: v.number(),
      })),
      team_code: v.nullish(v.array(v.string()), []),
      type: v.nullish(v.pipe(v.string()), ''),
      leave_type: v.nullish(v.pipe(v.string()), ''),
      q: v.nullish(v.pipe(v.string()), ''),
    }),
  ),
  initialValues: {
    date: {
      month: new Date().getMonth(),
      year: new Date().getFullYear(),
    },
  },
})

const filterFields = createFormFields(() => [
  {
    type: INPUT_TYPES.MONTH,
    class: 'col-span-1',
    props: {
      name: 'date',
      label: 'Date',
      placeholder: 'Select Date',
      maxDate: new Date(),
    },
  },
  {
    type: INPUT_TYPES.SELECT_MULTIPLE,
    class: 'col-span-2 col-start-1 col-end-2',
    props: {
      name: 'team_code',
      label: 'Team',
      placeholder: 'Select Team',
      options: ArrayHelper.toOptions(team.fetch.items, 'code', 'name'),
      searchable: true,
      clearable: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    class: 'col-span-1',
    props: {
      name: 'type',
      label: 'Type',
      placeholder: 'Select Type',
      options: CHECKIN_OPTIONS,
      clearable: true,
      searchable: true,
    },
    on: {
      change: (value) => {
        if (value !== CHECKIN.LEAVE && form.values.leave_type) {
          form.setFieldValue('leave_type', undefined)
        }
      },
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    class: 'col-span-1',
    props: {
      name: 'q',
      label: 'Search',
      placeholder: 'Name',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    class: 'col-span-1',
    isHide: form.values.type !== CHECKIN.LEAVE,
    props: {
      name: 'leave_type',
      label: 'Leave Type',
      placeholder: 'Select Leave Type',
      options: LEAVE_OPTIONS,
      clearable: true,
      searchable: true,
    },
  },
])

checkin.fetchSetLoading()
team.fetchSetLoading()
onMounted(() => {
  team.fetchPage(1, '', {
    params: {
      limit: 999,
    },
  })

  checkin.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      is_unused: false,
    },
  })
})

const tableOptions = useTable<any>({
  repo: checkin,
  columns: () => [
    {
      accessorKey: 'date',
      header: 'Date',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'name',
      header: 'Name',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'type',
      header: 'ClockIn type',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'check-in',
      header: 'ClockIn',
      type: COLUMN_TYPES.TEXT,
    },
  ],
})

const chartData = computed(() => {
  const checkinCounts: Record<string, number> = {};

  (checkin.fetch.items as ICheckinItem[]).forEach((item: ICheckinItem) => {
    const type = (item.type === CHECKIN.LEAVE ? item.leave_type || item.type : item.type) || 'UNKNOWN'

    checkinCounts[type] = (checkinCounts[type] || 0) + 1
  })

  const labels = Object.keys(checkinCounts).map((key) => CHECKIN_LABEL[key as CheckinType] || key)

  return {
    labels,
    datasets: [
      {
        label: 'จำนวนที่เช็คอิน',
        backgroundColor: labels.map((label: string) => CHECKIN_DASHBOARD_COLORS[label] || '#9CA3AF'),
        borderColor: labels.map((label: string) => CHECKIN_DASHBOARD_COLORS[label] || '#9CA3AF'),
        borderWidth: 1,
        data: Object.values(checkinCounts),
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const value = context.raw || 0

              return `จำนวนที่เช็คอิน: ${value}`
            },
          },
        },
      },
    ],
  }
})

const getCheckinBadge = (status: string) => {
  switch (status) {
    case 'On-time':
      return {
        color: 'success' as const,
        label: 'On-time',
      }
    case 'Late':
      return {
        color: 'warning' as const,
        label: 'Late',
      }
    case 'On-time (Edited)':
      return {
        color: 'success' as const,
        label: 'On-time (Edited)',
      }
    case 'Late (Edited)':
      return {
        color: 'warning' as const,
        label: 'Late (Edited)',
      }
    default:
      return {
        color: 'neutral' as const,
        label: 'Unknown',
      }
  }
}

const exportToExcel = () => {
  const header = ['Date', 'Name', 'ClockIn type', 'ClockIn']

  const rows = checkin.fetch.items.map((item) => {
    const row: (string | number)[] = [format(item.date, 'yyyy-MM-dd'),
      item.user?.display_name,
      item.type,
      getCheckinBadge(getCheckinStatus(item)).label]

    return row
  })

  const filename = `report_clockin_${currentDate.value.getMonth()}_${currentDate.value.getFullYear()}.xlsx`

  QuickExcel.export(
    header,
    rows,
    filename,
  )
}

watch(() => form.values.date, () => {
  currentDate.value = new Date(`${form.values.date?.year}-${(form.values.date?.month || 0) + 1}-01`)
  checkin.fetchPage(1, '', {
    params: {
      limit: 999,
      start_date: format(startOfMonth(currentDate?.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate?.value), 'yyyy-MM-dd'),
    },
  })
}, {
  deep: true,
})

watchDebounced(form.values, (values) => {
  checkin.fetchSearch(values.q || '', {
    params: {
      team_code: Array.isArray(values.team_code) && values.team_code.length > 0
        ? values.team_code.join(',')
        : undefined,
      type: values.type || undefined,
      leave_type: values.leave_type || undefined,
      limit: 999,
      start_date: format(startOfMonth(currentDate.value), 'yyyy-MM-dd'),
      end_date: format(endOfMonth(currentDate.value), 'yyyy-MM-dd'),
      is_unused: false,
    },
  })
}, {
  debounce: 300,
  deep: true,
})
</script>

<style scoped>
.dp__input {
  height: 44px !important;
}
</style>
