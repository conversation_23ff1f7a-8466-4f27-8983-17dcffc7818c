<template>
  <Card>
    <div class="text-lg font-bold">
      สมาชิกโครงการ
    </div>
    <div class="text-sm">
      Project Collaborators
    </div>
    <div class="my-5 flex items-center justify-between gap-6">
      <FormFields
        class="
        mb-4 grid gap-4 md:mb-0
        lg:grid-cols-6
      "
        :options="formFields"
      />
      <Button
        icon="tabler:user-plus"
        class="h-11"
        @click="onAdd"
      >
        เพิ่มสมาชิก
      </Button>
    </div>
    <Table
      :options="tableOptions"
      @pageChange="confidential.fetchPageChange"
      @search="confidential.fetchSearch"
    >
      <template #name-cell="{ row }">
        <AvatarProfile
          :item="row.original.user"
        />
      </template>
      <template #permission-cell="{ row }">
        <div

          class="flex gap-3 md:gap-5"
        >
          <div
            v-for="value in tabList"
            :key="value.label"
            class="flex w-fit flex-col items-center justify-center gap-1"
          >
            <div class="text-xs">
              {{ value.label }}
            </div>
            <div class="flex items-center gap-1">
              <PmoRole :role="row.original[`${value.key}_permission` as keyof ICollaborators] " />
              <img
                v-if="row.original[`${value.key}_main` as keyof ICollaborators]"
                src="/pmo/crown.png"
                alt="readonly"
                class="w-4"
              />
            </div>
          </div>
        </div>
      </template>
      <template #updated_by-cell="{ row }">
        <AvatarProfile
          :item="row.original.updated_by || row.original.created_by"
        />
      </template>
      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </Card>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { usePmoCollaboratorsByProjectIdPageLoader } from '~/loaders/pmo/project'
import { watchDebounced } from '@vueuse/core'
import Form from './Form.vue'
import { tabList } from '~/constants/projectTab'

const props = defineProps<{ projectId: string }>()

const route = useRoute()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const addModal = overlay.create(Form)
const editModal = overlay.create(Form)
const confidential = usePmoCollaboratorsByProjectIdPageLoader(props.projectId)
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'Search',
    },
  },
])

const tableOptions = useTable({
  repo: confidential,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'ชื่อ',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'permission',
      header: 'Permissions',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'updated_at',
      header: 'แก้ไขล่าสุด',
      type: COLUMN_TYPES.DATE,
    },
    {
      accessorKey: 'updated_by',
      header: 'แก้ไขโดย',
    },
    {
      accessorKey: 'actions',
      header: '',
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
    },
  ],
})

confidential.fetchSetLoading()
onMounted(() => {
  confidential.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})

const onEdit = (value: ICollaborators & { display_name: string }) => {
  editModal.open({
    currentMembers: confidential.fetch.items,
    values: value,
    isEditing: true,
    status: () => confidential.add.status,

    onSubmit: (values: ICollaborators & { display_name: string }) => {
      if (values?.confidential_main || values?.sales_main || values?.presales_main
        || values?.bidding_main || values?.pmo_main) {
        dialog.confirm({
          title: 'ยืนยันการเปลี่ยนแปลง',
          description: `คุณต้องการเปลี่ยนแปลงสิทธิ์ Main Responsibility \n ให้กับผู้ใช้ "${values.display_name}" หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          confidential.updateRun(value.id, {
            data: values,
          })
        },
        )
      } else {
        confidential.updateRun(value.id, {
          data: values,
        })
      }
    },
  })
}

const onDelete = (value: ICollaborators) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบสมาชิกโครงการ "${value.user.display_name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
    type: DialogType.ERROR,
  }).then(() => {
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })

    confidential.deleteRun(value.id)
  })
}

const onAdd = () => {
  addModal.open({
    currentMembers: confidential.fetch.items,
    status: () => confidential.add.status,
    onSubmit: (values: ICollaborators & { display_name: string }) => {
      if (values?.confidential_main || values?.sales_main || values?.presales_main
        || values?.bidding_main || values?.pmo_main) {
        dialog.confirm({
          title: 'ยืนยันการเปลี่ยนแปลง',
          description: `คุณต้องการให้สิทธิ์ Main Responsibility \n กับผู้ใช้ "${values.display_name}" หรือไม่?`,
          confirmText: 'ยืนยัน',
          cancelText: 'ยกเลิก',
        }).then(() => {
          dialog.loading({
            title: 'กรุณารอสักครู่...',
            description: 'กำลังส่งข้อมูล...',
          })

          confidential.addRun({
            data: values,
          })
        })
      } else {
        confidential.addRun({
          data: values,
        })
      }
    },
  })
}

watchDebounced(form.values, (values) => {
  confidential.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

useWatchTrue(
  () => confidential.add.status.isSuccess,
  () => {
    dialog.close()
    addModal.close()
    confidential.fetchPage()

    noti.success({
      title: 'เพิ่มสมาชิกโครงการสำเร็จ',
      description: 'คุณได้เพิ่มสมาชิกโครงการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => confidential.add.status.isError,
  () => {
    dialog.close()

    noti.error({
      title: 'เพิ่มสมาชิกโครงการไม่สำเร็จ',
      description: StringHelper.getError(confidential.add.status.errorData, 'เกิดข้อผิดพลาดในการเพิ่มสมาชิกโครงการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => confidential.update.status.isSuccess,
  () => {
    dialog.close()
    editModal.close()
    confidential.fetchPage()

    noti.success({
      title: 'แก้ไขสมาชิกโครงการสำเร็จ',
      description: 'คุณได้แก้ไขสมาชิกโครงการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => confidential.update.status.isError,
  () => {
    dialog.close()

    noti.error({
      title: 'แก้ไขสมาชิกโครงการไม่สำเร็จ',
      description: StringHelper.getError(confidential.update.status.errorData, 'เกิดข้อผิดพลาดในการแก้ไขสมาชิกโครงการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)

useWatchTrue(
  () => confidential.delete.status.isSuccess,
  () => {
    dialog.close()
    confidential.fetchPage()

    noti.success({
      title: 'ลบสมาชิกโครงการสำเร็จ',
      description: 'คุณได้ลบสมาชิกโครงการเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => confidential.delete.status.isError,
  () => {
    dialog.close()

    noti.error({
      title: 'ลบสมาชิกโครงการไม่สำเร็จ',
      description: StringHelper.getError(confidential.delete.status.errorData, 'เกิดข้อผิดพลาดในการลบสมาชิกโครงการ กรุณาลองใหม่อีกครั้ง'),
    })
  },
)
</script>
