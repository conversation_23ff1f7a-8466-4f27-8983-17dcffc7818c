import { LEAVE_TYPE, LEAVE_LABEL, type LeaveType } from './leave'

export enum CHECKIN {
  OFFICE_HQ = 'OFFICE_HQ',
  OFFICE_AKV = 'OFFICE_AKV',
  WFH = 'WFH',
  ONSITE = 'ONSITE',
  BUSINESS_TRIP = 'BUSINESS_TRIP',
  LEAVE = 'LEAVE',
}

export type CheckinType = CHECKIN | LeaveType

export const CHECKIN_LABEL: Record<CheckinType, string> = {
  [CHECKIN.OFFICE_HQ]: 'Office HQ',
  [CHECKIN.OFFICE_AKV]: 'Office AKV2',
  [CHECKIN.WFH]: 'Work from Home',
  [CHECKIN.ONSITE]: 'Onsite',
  [CHECKIN.BUSINESS_TRIP]: 'Business Trip',
  [CHECKIN.LEAVE]: 'Leave',
  ...LEAVE_LABEL,
}

export const CHECKIN_TYPES = [
  CHECKIN.OFFICE_HQ,
  CHECKIN.OFFICE_AKV,
  CHECKIN.WFH,
  CHECKIN.ONSITE,
  CHECKIN.BUSINESS_TRIP,
  CHECKIN.LEAVE,
] as const

export const CHECKIN_ICONS: Record<string, string> = {
  [CHECKIN.OFFICE_HQ]: 'ri:building-2-line',
  [CHECKIN.OFFICE_AKV]: 'pixelarticons:building',
  [CHECKIN.WFH]: 'tdesign:home',
  [CHECKIN.ONSITE]: 'mingcute:location-line',
  [CHECKIN.BUSINESS_TRIP]: 'mingcute:suitcase-line',
  [CHECKIN.LEAVE]: 'ri:calendar-line',
  [LEAVE_TYPE.ANNUAL]: 'ri:calendar-line',
  [LEAVE_TYPE.SICK]: 'ri:calendar-line',
  [LEAVE_TYPE.BUSINESS]: 'mingcute:suitcase-line',
  [LEAVE_TYPE.MENSTRUAL]: 'ri:calendar-line',
  [LEAVE_TYPE.BIRTHDAY]: 'ri:calendar-line',
  [LEAVE_TYPE.ORDINATION]: 'ri:calendar-line',
}

export const checkinIcons: Record<string, string> = {
  [CHECKIN.OFFICE_HQ]: '🏢',
  [CHECKIN.OFFICE_AKV]: '🏢',
  [CHECKIN.ONSITE]: '🚘',
  [CHECKIN.WFH]: '🏠',
  [CHECKIN.BUSINESS_TRIP]: '✈️',
  [CHECKIN.LEAVE]: '🤒',
  [LEAVE_TYPE.SICK]: '🤒',
  [LEAVE_TYPE.MENSTRUAL]: '😔',
  [LEAVE_TYPE.ANNUAL]: '🏖️',
  [LEAVE_TYPE.BUSINESS]: '💼',
  [LEAVE_TYPE.BIRTHDAY]: '🎂',
  [LEAVE_TYPE.ORDINATION]: '🙏',
}

export const CHECKIN_OPTIONS = CHECKIN_TYPES.map((type) => ({
  label: CHECKIN_LABEL[type],
  value: type,
}))

export const iconImages: Record<string, string> = {
  [CHECKIN.OFFICE_HQ]: '/checkin/office_hq.png',
  [CHECKIN.OFFICE_AKV]: '/checkin/office_akv.png',
  [CHECKIN.ONSITE]: '/checkin/onsite_icon.svg',
  [CHECKIN.WFH]: '/checkin/wfh_icon.svg',
  [CHECKIN.BUSINESS_TRIP]: '/checkin/business_trip.png',
  [CHECKIN.LEAVE]: '/checkin/vacation_icon.svg',
  [LEAVE_TYPE.SICK]: '/checkin/sick_icon.svg',
  [LEAVE_TYPE.MENSTRUAL]: '/checkin/mensural_icon.svg',
  [LEAVE_TYPE.ANNUAL]: '/checkin/vacation_icon.svg',
  [LEAVE_TYPE.BUSINESS]: '/checkin/vacation_icon.svg',
  [LEAVE_TYPE.BIRTHDAY]: '/checkin/birthday_icon.svg',
  [LEAVE_TYPE.ORDINATION]: '/checkin/ordination_icon.png',
}

export const GROUP_LABELS: Record<string, string> = {
  [CHECKIN.OFFICE_HQ]: CHECKIN_LABEL[CHECKIN.OFFICE_HQ],
  [CHECKIN.OFFICE_AKV]: CHECKIN_LABEL[CHECKIN.OFFICE_AKV],
  [CHECKIN.ONSITE]: CHECKIN_LABEL[CHECKIN.ONSITE],
  [CHECKIN.WFH]: CHECKIN_LABEL[CHECKIN.WFH],
  [CHECKIN.BUSINESS_TRIP]: CHECKIN_LABEL[CHECKIN.BUSINESS_TRIP],
  [LEAVE_TYPE.SICK]: LEAVE_LABEL[LEAVE_TYPE.SICK],
  [LEAVE_TYPE.MENSTRUAL]: LEAVE_LABEL[LEAVE_TYPE.MENSTRUAL],
  [LEAVE_TYPE.ANNUAL]: LEAVE_LABEL[LEAVE_TYPE.ANNUAL],
  [LEAVE_TYPE.BUSINESS]: LEAVE_LABEL[LEAVE_TYPE.BUSINESS],
  [LEAVE_TYPE.BIRTHDAY]: LEAVE_LABEL[LEAVE_TYPE.BIRTHDAY],
  [LEAVE_TYPE.ORDINATION]: LEAVE_LABEL[LEAVE_TYPE.ORDINATION],
}

export const CHECKIN_COLORS: Record<string, string> = {
  [CHECKIN.OFFICE_HQ]: 'text-[#1570EF]',
  [CHECKIN.OFFICE_AKV]: 'text-[#1570EF]',
  [CHECKIN.WFH]: 'text-[#1570EF]',
  [CHECKIN.ONSITE]: 'text-[#079455]',
  [CHECKIN.BUSINESS_TRIP]: 'text-[#079455]',
  [LEAVE_TYPE.SICK]: 'text-[#D92D20]',
  [LEAVE_TYPE.MENSTRUAL]: 'text-[#D92D20]',
  [LEAVE_TYPE.ANNUAL]: 'text-[#D92D20]',
  [LEAVE_TYPE.BUSINESS]: 'text-[#D92D20]',
  [LEAVE_TYPE.BIRTHDAY]: 'text-[#D92D20]',
  [LEAVE_TYPE.ORDINATION]: 'text-[#D92D20]',
}

export const CHECKIN_DASHBOARD_COLORS: Record<string, string> = {
  [LEAVE_LABEL[LEAVE_TYPE.ANNUAL]]: '#17B26A',
  [LEAVE_LABEL[LEAVE_TYPE.SICK]]: '#F79009',
  [CHECKIN_LABEL[CHECKIN.ONSITE]]: '#F04438',
  [CHECKIN_LABEL[CHECKIN.OFFICE_HQ]]: '#2E90FA',
  [CHECKIN_LABEL[CHECKIN.OFFICE_AKV]]: '#84CAFF',
  [CHECKIN_LABEL[CHECKIN.WFH]]: '#8B5CF6',
  [LEAVE_LABEL[LEAVE_TYPE.MENSTRUAL]]: '#FDA29B',
  [LEAVE_LABEL[LEAVE_TYPE.BUSINESS]]: '#1849A9',
  [LEAVE_LABEL[LEAVE_TYPE.BIRTHDAY]]: '#FEDF89',
  [LEAVE_LABEL[LEAVE_TYPE.ORDINATION]]: '#FF5733',
}
