<template>
  <NuxtLayout>
    <TimesheetReport />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import TimesheetReport from '~/features/timesheetAdmin/TimesheetReport/index.vue'

definePageMeta({
  layout: 'timesheet-admin',
  middleware: ['auth', 'permissions'],
  accessGuard: {
    permissions: routes.adminTimesheet.timesheetReport.permissions,
  },
})

useSeoMeta({
  title: routes.adminTimesheet.timesheetReport.label,
})

useApp().definePage({
  title: routes.adminTimesheet.timesheetReport.label,
  breadcrumbs: [routes.adminTimesheet.timesheetReport],
  sub_title: 'Time Sheet Report',
})
</script>
