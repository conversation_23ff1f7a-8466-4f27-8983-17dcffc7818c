<template>
  <div
    :class="[theme.base({
      class: [$props.class, ui?.base],
    })]"
  >
    <component
      :is="componentMap[option.type]?.component"
      v-for="option in options.filter((item) => !item.isHide)"
      :key="option.props.name"
      :class="option.class"
      :form="form"
      v-bind="{
        ...getFieldBinding(option),
        ...componentMap[option.type]?.props,
      }"
      v-on="option.on ?? {}"
    />
  </div>
</template>

<script lang="ts" setup>
import type { FormContext } from 'vee-validate'
import type { Component } from 'vue'
import { computed } from 'vue'
import FormInputTextarea from './InputTextarea/index.vue'
import FormInputText from './InputText/index.vue'
import FormInputSearch from './InputSearch/index.vue'
import FormInputNumber from './InputNumber/index.vue'
import FormInputToggle from './InputToggle/index.vue'
import FormInputCheckbox from './InputCheckbox/index.vue'
import FormInputSelect from './InputSelect/index.vue'
import FormInputSelectMultiple from './InputSelectMultiple/index.vue'
import FormInputComponent from './InputComponent/index.vue'
import FormInputRadio from './InputRadio/index.vue'
import FormInputDateTime from './InputDateTime/index.vue'
import FormInputTime from './InputTime/index.vue'
import FormInputMonth from './InputMonth/index.vue'
import FormInputDateTimeRange from './InputDateTimeRange/index.vue'
import FormInputUploadDropzoneAuto from './InputUploadDropzoneAuto/index.vue'
import FormInputUploadDropzone from './InputUploadDropzone/index.vue'
import FormInputTag from './InputTags/index.vue'
import FormInputWYSIWYG from './InputWYSIWYG/index.vue'
import { type IFormField, INPUT_TYPES } from '#core/components/Form/types'
import { formTheme } from '#core/theme/form'
import { useUiConfig } from '#core/composables/useConfig'

const props = withDefaults(
  defineProps<{
    form?: FormContext
    options: IFormField[]
    orientation?: 'horizontal' | 'vertical'
    class?: any
    ui?: typeof formTheme['slots']
  }>(),
  {
    orientation: 'vertical',
  },
)

interface ComponentMapEntry {
  component: Component
  props?: Record<string, any>
}

const componentMap: Record<INPUT_TYPES, ComponentMapEntry | undefined> = {
  [INPUT_TYPES.TEXT]: {
    component: FormInputText,
    props: {},
  },
  [INPUT_TYPES.SEARCH]: {
    component: FormInputSearch,
    props: {},
  },
  [INPUT_TYPES.TEXTAREA]: {
    component: FormInputTextarea,
    props: {},
  },
  [INPUT_TYPES.TOGGLE]: {
    component: FormInputToggle,
    props: {},
  },
  [INPUT_TYPES.CHECKBOX]: {
    component: FormInputCheckbox,
    props: {},
  },
  [INPUT_TYPES.SELECT]: {
    component: FormInputSelect,
    props: {},
  },
  [INPUT_TYPES.SELECT_MULTIPLE]: {
    component: FormInputSelectMultiple,
    props: {},
  },
  // For INPUT_TYPES.COMPONENT, it will use option.component directly from the template
  [INPUT_TYPES.COMPONENT]: {
    component: FormInputComponent,
    props: {},
  },
  // Add other INPUT_TYPES here if they have dedicated components not covered by option.component
  [INPUT_TYPES.NUMBER]: {
    component: FormInputNumber,
    props: {},
  }, // Example: Map to FormInputText or a specific Number input if exists
  [INPUT_TYPES.PASSWORD]: {
    component: FormInputText,
    props: {
      type: 'password',
    },
  },
  [INPUT_TYPES.EMAIL]: {
    component: FormInputText,
    props: {
      type: 'email',
    },
  },
  [INPUT_TYPES.STATIC]: undefined,
  [INPUT_TYPES.RADIO]: {
    component: FormInputRadio,
    props: {},
  },
  [INPUT_TYPES.DATE_TIME]: {
    component: FormInputDateTime,
    props: {},
  },
  [INPUT_TYPES.TIME]: {
    component: FormInputTime,
    props: {},
  },
  [INPUT_TYPES.DATE]: {
    component: FormInputDateTime,
    props: {
      disabledTime: true,
    },
  },
  [INPUT_TYPES.MONTH]: {
    component: FormInputMonth,
    props: {},
  },
  [INPUT_TYPES.DATE_RANGE]: {
    component: FormInputDateTimeRange,
    props: {
      disabledTime: true,
    },
  },
  [INPUT_TYPES.DATE_TIME_RANGE]: {
    component: FormInputDateTimeRange,
    props: {},
  },
  [INPUT_TYPES.UPLOAD_FILE_CLASSIC]: undefined,
  [INPUT_TYPES.UPLOAD_FILE_CLASSIC_AUTO]: undefined,
  [INPUT_TYPES.UPLOAD_IMAGE_AUTO]: undefined,
  [INPUT_TYPES.UPLOAD_DROPZONE]: {
    component: FormInputUploadDropzone,
    props: {},

  },
  [INPUT_TYPES.UPLOAD_DROPZONE_AUTO]: {
    component: FormInputUploadDropzoneAuto,
    props: {},
  },
  [INPUT_TYPES.UPLOAD_DROPZONE_AUTO_MULTIPLE]: undefined,
  [INPUT_TYPES.UPLOAD_DROPZONE_IMAGE_AUTO_MULTIPLE]: undefined,
  [INPUT_TYPES.WYSIWYG]: {
    component: FormInputWYSIWYG,
    props: {},
  },
  [INPUT_TYPES.TAGS]: {
    component: FormInputTag,
    props: {},
  },
}

const theme = computed(() => useUiConfig(formTheme, 'form')({
  orientation: props.orientation,
}))

const getFieldBinding = (field: IFormField) => {
  const {
    props: fieldProps,
    ui: fieldUi,
  } = field

  const allProps = {
    ...fieldProps,
    containerUi: {
      ...fieldUi,
    },
  }

  if (props.orientation === 'horizontal') {
    return {
      ...allProps,
      containerUi: {
        root: theme.value.wrapper({
          class: [props.ui?.wrapper],
        }),
        labelWrapper: theme.value.container({
          class: [props.ui?.container],
        }),
      },
    }
  }

  return allProps
}
</script>
