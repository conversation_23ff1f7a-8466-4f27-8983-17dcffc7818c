## Form (Component)

### Overview
Form is a lightweight wrapper that renders a native `<form>` element and is designed to be used together with FormFields and vee-validate. It provides the foundation for building complex, validated forms with consistent styling and behavior.

### Key Features
- 🔧 **Seamless Integration** - Works perfectly with <PERSON>Fields and vee-validate
- ✅ **Built-in Validation** - Automatic error handling and display
- 🎨 **Consistent Styling** - Follows the design system automatically
- 📱 **Responsive Design** - Mobile-friendly out of the box
- ♿ **Accessibility** - ARIA labels and keyboard navigation

### Registration
- Auto-registered by @finema/core
- Supports optional prefix via nuxt.config.ts core.prefix (e.g., `<F-Form>`)

### Props
- None (serves as a structural wrapper)
- Accepts all standard HTML form attributes via `v-bind="$attrs"`

### Events
- **@submit** - Native submit event; typically handled by vee-validate's `form.handleSubmit`
- All other native form events are supported

### Basic Usage

```vue
<template>
  <Form @submit="onSubmit">
    <FormFields :options="formFields" />
    <Button type="submit">Submit</Button>
  </Form>
</template>

<script setup lang="ts">
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.pipe(v.string(), v.minLength(2)),
      email: v.pipe(v.string(), v.email())
    })
  )
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'Full Name',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true
    }
  }
])

const onSubmit = form.handleSubmit((values) => {
  console.log('Form submitted:', values)
}, moveToError)
</script>
```

### Advanced Example (From Playground)

This example demonstrates a comprehensive form with various input types, validation, and error handling:

```vue
<template>
  <div class="max-w-2xl mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">User Registration</h1>
    
    <!-- Debug Panel (Development only) -->
    <Log
      v-if="isDev"
      title="Form State"
      :data-items="[form.values, form.errors]"
      class="mb-6"
    />
    
    <Form @submit="onSubmit" class="space-y-6">
      <!-- Basic Information -->
      <div>
        <h2 class="text-lg font-semibold mb-4 border-b pb-2">
          Basic Information
        </h2>
        <FormFields :options="basicFields" />
      </div>
      
      <!-- Contact Details -->
      <div>
        <h2 class="text-lg font-semibold mb-4 border-b pb-2">
          Contact Details
        </h2>
        <FormFields :options="contactFields" />
      </div>
      
      <!-- Preferences -->
      <div>
        <h2 class="text-lg font-semibold mb-4 border-b pb-2">
          Preferences
        </h2>
        <FormFields :options="preferenceFields" />
      </div>
      
      <!-- Actions -->
      <div class="flex gap-4 pt-4">
        <Button 
          type="submit" 
          :loading="isSubmitting"
          :disabled="!form.meta.valid"
        >
          Create Account
        </Button>
        <Button 
          variant="outline" 
          @click="resetForm"
          :disabled="isSubmitting"
        >
          Reset
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup lang="ts">
const isDev = process.dev

// Form setup with comprehensive validation
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      // Basic info
      firstName: v.pipe(
        v.string(), 
        v.minLength(2, 'First name must be at least 2 characters')
      ),
      lastName: v.pipe(
        v.string(), 
        v.minLength(2, 'Last name must be at least 2 characters')
      ),
      username: v.pipe(
        v.string(),
        v.minLength(3, 'Username must be at least 3 characters'),
        v.regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
      ),
      
      // Contact
      email: v.pipe(v.string(), v.email('Please enter a valid email')),
      phone: v.nullish(
        v.pipe(
          v.string(),
          v.regex(/^\d{3}-\d{3}-\d{4}$/, 'Phone must be in format: ************')
        )
      ),
      
      // Preferences
      newsletter: v.boolean(),
      theme: v.string(),
      bio: v.nullish(
        v.pipe(v.string(), v.maxLength(500, 'Bio must be less than 500 characters'))
      )
    })
  ),
  initialValues: {
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    phone: '',
    newsletter: true,
    theme: 'light',
    bio: ''
  }
})

// Form field configurations
const basicFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'firstName',
      label: 'First Name',
      placeholder: 'Enter your first name',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'lastName',
      label: 'Last Name',
      placeholder: 'Enter your last name',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'username',
      label: 'Username',
      placeholder: 'Choose a unique username',
      description: 'This will be your public identifier',
      required: true
    }
  }
])

const contactFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      placeholder: '<EMAIL>',
      required: true
    }
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'phone',
      label: 'Phone Number',
      mask: '###-###-####',
      placeholder: '************',
      description: 'Optional - for account recovery'
    }
  }
])

const preferenceFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TOGGLE,
    props: {
      name: 'newsletter',
      label: 'Newsletter Subscription',
      description: 'Receive updates and news via email'
    }
  },
  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'theme',
      label: 'Preferred Theme',
      options: [
        { value: 'light', label: 'Light', description: 'Clean and bright interface' },
        { value: 'dark', label: 'Dark', description: 'Easy on the eyes' },
        { value: 'auto', label: 'Auto', description: 'Follow system preference' }
      ]
    }
  },
  {
    type: INPUT_TYPES.TEXTAREA,
    props: {
      name: 'bio',
      label: 'Bio',
      placeholder: 'Tell us about yourself...',
      rows: 3,
      description: 'Optional - share a bit about yourself'
    }
  }
])

// Form handlers
const isSubmitting = ref(false)
const notification = useNotification()

const onSubmit = form.handleSubmit(async (values) => {
  isSubmitting.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    notification.success({
      title: 'Account Created!',
      description: `Welcome ${values.firstName}! Your account has been created successfully.`
    })
    
    // Reset form after successful submission
    form.resetForm()
    
  } catch (error) {
    notification.error({
      title: 'Registration Failed',
      description: 'There was an error creating your account. Please try again.'
    })
  } finally {
    isSubmitting.value = false
  }
}, moveToError)

const resetForm = () => {
  form.resetForm()
  notification.info({
    title: 'Form Reset',
    description: 'All fields have been cleared to their default values.'
  })
}
</script>
```

### Form Validation Patterns

#### Required Fields
```vue
<script setup>
const schema = v.object({
  name: v.pipe(v.string(), v.minLength(1, 'Name is required'))
})
</script>
```

#### Email Validation
```vue
<script setup>
const schema = v.object({
  email: v.pipe(v.string(), v.email('Please enter a valid email address'))
})
</script>
```

#### Custom Validation
```vue
<script setup>
const schema = v.object({
  password: v.pipe(
    v.string(),
    v.minLength(8, 'Password must be at least 8 characters'),
    v.regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
  )
})
</script>
```

### Error Handling

The Form component automatically handles validation errors through the `moveToError` helper:

```vue
<script setup>
const onSubmit = form.handleSubmit(
  (values) => {
    // Success handler
    console.log('Valid form data:', values)
  },
  moveToError // Automatically scrolls to first error field
)
</script>
```

### Theming
- The Form wrapper inherits styling from the global theme configuration
- Individual field styling is handled by FormFields and input components
- Custom styling can be applied via CSS classes

### Related Components
- **FormFields** - Dynamic field rendering
- **FieldWrapper** - Individual field container
- **Input Components** - All available input types

### Related Composables
- **useForm** - Form state management (from vee-validate)
- **createFormFields** - Reactive field configuration
- **moveToError** - Error navigation helper

### Best Practices

1. **Always use validation schemas** for data integrity
2. **Group related fields** using visual separators
3. **Provide clear error messages** that help users fix issues
4. **Use appropriate input types** for better UX
5. **Handle loading states** during form submission
6. **Reset forms** after successful submission
7. **Provide feedback** via notifications

### Common Patterns

#### Multi-step Forms
```vue
<template>
  <Form @submit="onSubmit">
    <FormFields v-if="step === 1" :options="step1Fields" />
    <FormFields v-if="step === 2" :options="step2Fields" />
    
    <div class="flex justify-between">
      <Button v-if="step > 1" @click="step--">Previous</Button>
      <Button v-if="step < 2" @click="step++">Next</Button>
      <Button v-if="step === 2" type="submit">Submit</Button>
    </div>
  </Form>
</template>
```

#### Conditional Fields
```vue
<script setup>
const showAdvanced = ref(false)

const fields = createFormFields(() => [
  // Always visible fields
  { type: INPUT_TYPES.TEXT, props: { name: 'name', label: 'Name' } },
  
  // Conditional fields
  ...(showAdvanced.value ? [
    { type: INPUT_TYPES.TEXT, props: { name: 'advanced', label: 'Advanced Option' } }
  ] : [])
])
</script>
```

### Notes
- Form components are fully accessible with proper ARIA attributes
- All form events bubble up naturally for custom handling
- The component works seamlessly with Nuxt's SSR capabilities
- Validation runs both client-side and can be extended server-side
